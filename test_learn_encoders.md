# Test des handlers d'encoders pour le mode learn

## Architecture implémentée

### Go (OSCBridgeGo)
- ✅ `liveLearn_types.go` : Ajout de `EncoderBuffers map[int]float64`
- ✅ `liveLearn_hardwareHandlers.go` : Méthode `handleEncoderEvent()`
- ✅ `liveLearnMode.go` : Initialisation et nettoyage des buffers

### Python (AbletonOSC)
- ✅ `learn_mode_helper/learn_mode_encoder_handlers.py` : Logique complète
- ✅ `learn.py` : Intégration et handler OSC `/live/learn/adjust/parameter`

## Fonctionnalités à tester

### 1. Gestion des buffers Go
- [ ] Vérifier que les buffers d'encoders sont initialisés
- [ ] Tester le seuil 0.005 pour l'envoi des deltas
- [ ] Vérifier le calcul des steps et la mise à jour des buffers

### 2. Communication OSC
- [ ] Vérifier que les messages `/live/learn/adjust/parameter` sont envoyés
- [ ] Tester le format : `[parameter_index, delta_value]`
- [ ] Vérifier que `parameter_index = encoder_index + 1`

### 3. Logique <PERSON>
- [ ] Tester la détection du device en cours d'apprentissage
- [ ] Vérifier le scaling adaptatif (devices normaux vs racks)
- [ ] Tester les paramètres quantifiés avec seuil 0.5
- [ ] Vérifier les paramètres continus

### 4. Cas d'usage spécifiques
- [ ] Device normal avec paramètres continus (0-1)
- [ ] Rack avec paramètres continus (0-127)
- [ ] Paramètres quantifiés (listes de valeurs)
- [ ] Paramètres booléens (on/off)

## Procédure de test

### Étape 1 : Compilation Go
```bash
cd /Users/<USER>/Programming/Go/OSCBridgeGo
go build
```

### Étape 2 : Test en mode learn
1. Lancer OSCBridgeGo
2. Passer en mode learn
3. Apprendre un paramètre de device sur un slot
4. Tourner les encoders 0-7
5. Vérifier les logs Go et Python

### Étape 3 : Vérifications
- Logs Go : Messages d'encoders avec buffers et deltas
- Logs Python : Réception OSC et ajustement des paramètres
- Ableton : Changement effectif des valeurs de paramètres

## Messages de log attendus

### Go
```
Mode learn: Encodeur 0 tourné avec la valeur 50
Encodeur 0: buffer=0.050000, steps=10, deltaToSend=0.050000, newBuffer=0.000000
Envoi OSC /live/learn/adjust/parameter: paramètre 1, delta 0.050000
```

### Python
```
adjust_learn_parameter: param_index=1, delta=0.050000
Device en apprentissage trouvé: Operator
Paramètre 1 détecté comme continu (rack: False)
Paramètre 1 ajusté: 0.500 -> 0.510 (delta: 0.050000, quantifié: False, rack: False)
```

## Points critiques à valider

### ✅ Architecture
- [x] Même structure que le mode device
- [x] Séparation Go/Python respectée
- [x] Buffers et seuils cohérents

### 🔍 À tester
- [ ] Scaling correct pour tous types de devices
- [ ] Paramètres quantifiés non-circulaires
- [ ] Gestion des cas d'erreur
- [ ] Performance et fluidité

### 🎯 Objectif
Avoir exactement le même comportement que le mode device, mais appliqué au device en cours d'apprentissage dans le mode learn.
