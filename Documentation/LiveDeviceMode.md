# Mode Device - Documentation

## Introduction

Le Mode Device est un composant essentiel de l'application qui permet aux utilisateurs de visualiser, sélectionner et manipuler les devices (effets, instruments, etc.) dans Ableton Live. Ce mode offre une interface intuitive pour naviguer dans les racks, ajuster les paramètres des devices et gérer les chaînes.

## Structure du Mode

Le Mode Device est organisé en deux sous-modes principaux :

1. **Sous-mode Device** (SubMode = 1) : Permet de visualiser et de manipuler les devices individuels.
2. **Sous-mode Chain** (SubMode = 0) : Permet de visualiser et de manipuler les chaînes dans un rack.

## Navigation et Hiérarchie

Le Mode Device utilise un système de navigation basé sur un chemin (`Path`) qui représente la position actuelle dans la hiérarchie des devices :

- Un chemin vide `[]` représente le niveau racine (devices de la piste sélectionnée).
- Un chemin avec un seul élément `[0]` représente le device à l'index 0.
- Un chemin avec deux éléments `[0, 1]` représente la chaîne à l'index 1 du rack à l'index 0.
- Un chemin avec trois éléments `[0, 1, 2]` représente le device à l'index 2 dans la chaîne à l'index 1 du rack à l'index 0.

Le sous-mode est déterminé par la longueur du chemin :
- Longueur paire (0, 2, 4...) = Sous-mode Chain
- Longueur impaire (1, 3, 5...) = Sous-mode Device

## Fonctionnalités Principales

### 1. Affichage des Devices

Le Mode Device affiche les devices de plusieurs façons :

- **Liste des Devices** : Affiche jusqu'à 8 devices par page avec pagination.
- **Paramètres du Device** : Affiche jusqu'à 8 paramètres par page pour le device sélectionné.
- **Informations du Device** : Affiche le nom du device, son état (actif/inactif), et d'autres propriétés.

### 2. Manipulation des Devices

Le Mode Device permet plusieurs actions sur les devices :

- **Sélection** : Toucher un device pour le sélectionner.
- **Activation/Désactivation** : Basculer l'état actif d'un device.
- **Déplacement** : Déplacer un device à une autre position.
- **Navigation dans les Racks** : Entrer dans un rack pour voir ses chaînes et devices internes.
- **Ajustement des Paramètres** : Modifier les paramètres d'un device à l'aide des encodeurs.

### 3. Gestion des Racks et Chaînes

Le Mode Device offre des fonctionnalités spécifiques pour les racks :

- **Navigation dans les Chaînes** : Sélectionner et naviguer entre les chaînes d'un rack.
- **Paramètres de Chaîne** : Ajuster le volume, le panoramique, le mute et le solo des chaînes.
- **Drum Racks** : Support spécial pour les Drum Racks avec affichage des pads et navigation.

### 4. Verrouillage de Device

Le Mode Device permet de verrouiller un device spécifique pour le manipuler même lorsque la sélection change :

- **Verrouillage** : Verrouiller le device actuellement sélectionné.
- **Déverrouillage** : Déverrouiller le device pour revenir au comportement normal.

## Interface Utilisateur

### Messages d'Affichage

Le Mode Device utilise plusieurs types de messages pour l'affichage :

- `mo,2` : Active l'affichage du mode device.
- `di,...` : Affiche les informations des paramètres (valeurs normalisées et textuelles).
- `nn,...` : Affiche les noms des paramètres.
- `dd,...` : Affiche les devices avec leur état de sélection.
- `ds,...` : Affiche le nom du device sélectionné.
- `pd,...` : Affiche l'état de pagination.
- `pv,...` : Met à jour la valeur d'un paramètre spécifique.

### Interaction Matérielle

Le Mode Device répond à plusieurs types d'événements matériels :

- **Encodeurs** : Ajustent les paramètres du device sélectionné.
- **Boutons** : Contrôlent la pagination et le verrouillage du device.
- **Touches** : Permettent de sélectionner des devices, naviguer dans les racks, et effectuer diverses actions.

## Communication OSC

Le Mode Device utilise le protocole OSC pour communiquer avec Ableton Live :

### Messages Entrants

- `/live/device/get/parameter/value` : Reçoit les valeurs des paramètres.
- `/live/device/get/isActive` : Reçoit l'état actif du device.
- `/live/deviceMode/get/parameters/bulk` : Reçoit les informations de plusieurs paramètres en une fois.
- `/live/deviceMode/get/environment/` : Reçoit l'environnement actuel (devices, chaînes, etc.).
- `/live/device/get/is_collapsed` : Reçoit l'état replié du device.
- `/live/device/get/can_have_chains` : Reçoit si le device peut avoir des chaînes.

### Messages Sortants

- `/live/select/device` : Sélectionne un device.
- `/live/select/chain` : Sélectionne une chaîne.
- `/live/device/set/parameter/value` : Définit la valeur d'un paramètre.
- `/live/device/set/isActiveToggle` : Bascule l'état actif d'un device.
- `/live/device/move_by_path` : Déplace un device.

## Gestion des Paramètres

Le Mode Device gère les paramètres des devices de manière efficace :

- **Pagination** : Affiche 8 paramètres par page avec navigation entre les pages.
- **Normalisation** : Convertit les valeurs des paramètres en pourcentages (0-100) pour l'affichage.
- **Mise en File d'Attente** : Utilise une file d'attente pour éviter de surcharger l'interface.
- **Paramètres Quantifiés** : Affiche une indication spéciale pour les paramètres quantifiés.

## Cas Spéciaux

### Drum Racks

Les Drum Racks sont traités de manière spéciale :

- Affichage d'une grille de pads avec leur état.
- Navigation directe vers les chaînes associées aux pads.
- Support pour les notes MIDI des pads.

### Devices Verrouillés

Lorsqu'un device est verrouillé :

- Les paramètres du device verrouillé sont affichés même si un autre device est sélectionné.
- Les modifications s'appliquent au device verrouillé plutôt qu'au device sélectionné.
- Un indicateur visuel montre que le mode verrouillé est actif.

## Architecture du Code

Le Mode Device est organisé en plusieurs fichiers :

- `liveDeviceMode.go` : Structure principale et méthodes d'initialisation.
- `liveDevice_types.go` : Définitions des types et constantes.
- `liveDevice_oscHandlers.go` : Gestionnaires des messages OSC.
- `liveDevice_hardwareHandlers.go` : Gestionnaires des événements matériels.
- `liveDevice_parameterManager.go` : Gestion des paramètres des devices.
- `liveDevice_propertyManager.go` : Gestion des propriétés des devices.

## Managers

Le Mode Device utilise deux managers principaux :

### DeviceParameterManager

Gère l'affichage et la mise à jour des paramètres de device :

- Stocke les informations des paramètres (nom, valeur, plage, etc.).
- Gère la pagination des paramètres.
- Envoie les messages d'affichage pour les paramètres.

### DevicePropertyManager

Gère les propriétés et l'affichage des devices :

- Stocke les propriétés des devices (nom, état actif, etc.).
- Gère l'affichage des listes de devices.
- Envoie les messages d'affichage pour les devices.

## Conclusion

Le Mode Device est un composant complexe mais puissant qui offre une interface intuitive pour manipuler les devices dans Ableton Live. Sa structure hiérarchique permet une navigation facile dans les racks et les chaînes, tandis que son système de gestion des paramètres offre un contrôle précis sur les devices.
