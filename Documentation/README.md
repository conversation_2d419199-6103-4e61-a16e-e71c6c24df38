# Documentation OSCBridgeGo

## Organisation de la Documentation

Cette documentation est organisée en deux catégories principales :

### 📋 Documentation des Modes Live

Documentation détaillée de chaque mode de l'application :

- **[`LiveDeviceMode.md`](LiveDeviceMode.md)** - Mode Device : Gestion des devices et effets
- **[`LiveLearnMode.md`](LiveLearnMode.md)** - Mode Learn : Système d'apprentissage des paramètres
- **[`LiveModeVolume.md`](LiveModeVolume.md)** - Mode Volume : Contrôle des volumes, pan et sends
- **[`LiveTrackMode.md`](LiveTrackMode.md)** - Mode Track : Gestion des pistes et QuickView

### 🎛️ Documentation Technique

Documentation technique des systèmes transversaux :

- **[`ButtonManagement_Final_Solution.md`](ButtonManagement_Final_Solution.md)** - **SOLUTION FINALE SIMPLIFIÉE** : Gestion directe des boutons transversaux (Architecture ultra-simple)

## Guide de Lecture

### Pour comprendre un mode spécifique

1. Lire la documentation du mode correspondant (ex: `LiveDeviceMode.md`)
2. Consulter `ButtonManagement_Final_Solution.md` pour les boutons transversaux

### Pour comprendre la gestion des boutons

- **Lire uniquement** `ButtonManagement_Final_Solution.md` - Solution finale simplifiée et fonctionnelle

#### Philosophie de Simplification

L'architecture des boutons transversaux a été **radicalement simplifiée** :

- ❌ **Suppression** du `ButtonManager` (couche d'abstraction inutile)
- ❌ **Suppression** de l'interface `ButtonMode` (complexité non nécessaire)
- ✅ **Adoption** d'une logique directe avec un simple `switch` dans `HardwareManager`

**Principe** : Pour 4 boutons transversaux, un `switch` de 40 lignes est plus efficace que 200+ lignes d'abstraction.

## Architecture Générale

L'application OSCBridgeGo est organisée autour de 5 modes principaux :

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│    Volume   │    │    Track    │    │   Device    │
│   (Mixage)  │    │  (Pistes)   │    │  (Effets)   │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       └───────────────────┼───────────────────┘
                           │
┌─────────────┐    ┌─────────────┐
│   Browser   │    │    Learn    │
│ (Navigation)│    │(Apprentiss.)│
└─────────────┘    └─────────────┘
```

### Système QuickView

Le système QuickView permet d'accéder temporairement au mode Track depuis n'importe quel autre mode via le bouton B05.

### Communication

- **ESP32** ↔ **OSCBridgeGo** ↔ **Ableton Live**
- Protocoles : Série (ESP32) + OSC (Ableton Live)

---

_Dernière mise à jour : 25/05/2025 - Architecture des boutons transversaux simplifiée_
