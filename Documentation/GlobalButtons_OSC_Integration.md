# Intégration OSC des Boutons Globaux (B10, B11, B12)

## Problème résolu

Les boutons B10 (Play), B11 (Stop) et B12 (Record) émettaient des événements via le `HardwareManager` mais n'envoyaient pas de messages OSC vers Ableton Live.

## Solution implémentée

### 1. Nouvelle méthode dans LiveModeManager

Ajout de la méthode `SetupGlobalHardwareListeners()` dans [`Live/liveModeManager.go`](../Live/liveModeManager.go:99) qui :

- Écoute les événements `globalPlay`, `globalStop`, `globalRecord` du HardwareManager
- Convertit ces événements en messages OSC appropriés
- Envoie les messages vers Ableton Live

### 2. Messages OSC envoyés

| Bouton | Événement    | Message OSC                | Valeur |
| ------ | ------------ | -------------------------- | ------ |
| B10    | globalPlay   | `/live/song/start_playing` | 1      |
| B11    | globalStop   | `/live/song/stop_playing`  | 1      |
| B12    | globalRecord | `/live/song/record`        | 1      |

### 3. Configuration dans main.go

Modification de [`main.go`](../main.go:154) pour appeler la configuration des écouteurs globaux lors de l'initialisation.

## Architecture

```mermaid
graph TD
    A[ESP32 Boutons B10/B11/B12] --> B[HardwareManager]
    B --> C[Emit globalPlay/Stop/Record]
    C --> D[LiveModeManager écoute]
    D --> E[Send OSC Messages]
    E --> F[Ableton Live]

    B --> G[Mode Actif]
    G --> H[Autres événements hardware]
```

## Avantages de cette approche

1. **Cohérence architecturale** : Suit le même pattern que les autres événements OSC
2. **Séparation des responsabilités** : HardwareManager reste focalisé sur les événements hardware
3. **Réutilisation** : Exploite la capacité OSC existante du LiveModeManager
4. **Minimal invasif** : Pas de modification majeure de l'architecture existante

## Test

Pour tester l'implémentation :

1. Compiler l'application : `go build -o oscbridge`
2. Lancer l'application avec Ableton Live ouvert
3. Appuyer sur les boutons B10, B11, B12
4. Vérifier dans les logs que les messages OSC sont envoyés
5. Vérifier dans Ableton Live que les actions sont exécutées

## Logs attendus

```
[LiveModeManager] Événement globalPlay reçu - Envoi OSC /live/song/start_playing
[LiveModeManager] Événement globalStop reçu - Envoi OSC /live/song/stop_playing
[LiveModeManager] Événement globalRecord reçu - Envoi OSC /live/song/record
```
