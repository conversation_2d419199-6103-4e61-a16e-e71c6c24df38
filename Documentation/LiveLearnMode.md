# Documentation du Mode Learn

## Introduction

Le Mode Learn est un composant essentiel de l'application qui permet de mapper des paramètres d'Ableton Live à des contrôleurs physiques. Ce document explique le fonctionnement général du mode Learn et détaille les deux méthodes principales pour configurer un slot de learn : via une action directe dans Ableton Live ou via une "simulation" avec l'ESP32.

## Concepts de base

### Slots de Learn

- Le système dispose de 32 slots de learn au total (numérotés de 0 à 31)
- Les slots sont organisés en pages de 8 slots chacune (4 pages au total)
- Sur chaque page, les slots sont numérotés de 0 à 7 pour l'affichage

### Types de paramètres

Le mode Learn peut mapper différents types de paramètres :

| Type | Valeur | Description |
|------|--------|-------------|
| Volume | 1 | Volume d'une piste |
| Pan | 2 | Panoramique d'une piste |
| Send | 3 | Envoi d'une piste vers une autre |
| Device | 4 | Paramètre d'un device |
| Chain Volume | 5 | Volume d'une chaîne dans un rack |
| Chain Pan | 6 | Panoramique d'une chaîne dans un rack |
| Mute | 7 | État de mute d'une piste |
| Solo | 8 | État de solo d'une piste |
| Chain Mute | 9 | État de mute d'une chaîne |
| Chain Solo | 10 | État de solo d'une chaîne |

## Architecture du système

Le mode Learn est implémenté dans le dossier `Live/LiveLearnMode/` et comprend plusieurs fichiers :

- `liveLearnMode.go` : Fichier principal contenant la structure et les méthodes de base
- `liveLearn_types.go` : Définition des types et constantes
- `liveLearn_displayManager.go` : Gestion de l'affichage
- `liveLearn_hardwareHandlers.go` : Gestion des événements matériels (ESP32)
- `liveLearn_learningHandlers.go` : Handlers pour l'apprentissage des paramètres
- `liveLearn_registration.go` : Enregistrement des handlers OSC
- `liveLearn_slotHandlers.go` : Gestion des slots

## Communication OSC

Le mode Learn utilise le protocole OSC (Open Sound Control) pour communiquer avec Ableton Live. Les messages OSC sont échangés entre :

1. L'application Go (OSCBridgeGo)
2. Le plugin Python dans Ableton Live (AbletonOSC)
3. Le contrôleur matériel ESP32

### Messages OSC principaux

#### Messages d'écoute (de Live vers l'application)
- `/live/learnslot/get/properties` : Propriétés d'un slot
- `/live/tracklearn/get/volume` : Volume d'une piste
- `/live/tracklearn/get/panning` : Panoramique d'une piste
- `/live/tracklearn/get/sends` : Envois d'une piste
- `/live/devicelearn/get/parameter` : Paramètre d'un device
- `/live/chainlearn/get/volume` : Volume d'une chaîne
- `/live/chainlearn/get/panning` : Panoramique d'une chaîne
- `/live/readyToListen` : Live est prêt à écouter

#### Messages de configuration (de l'application vers Live)
- `/live/learn/slot` : Configuration d'un slot
- `/live/learn/stop` : Arrêt de l'apprentissage
- `/live/learn/setup_track` : Configuration pour l'apprentissage de piste
- `/live/learn/setup_device` : Configuration pour l'apprentissage de device
- `/live/learn/del_slot` : Suppression d'un slot

#### Messages vers l'ESP32
- `ls,<slot>,<type>,<value>,<display>` : État d'un slot
- `lo,<slot>,<device>,<track>,<color>` : Propriétés d'un slot
- `ll,<slot>` : Statut d'apprentissage

## Méthodes de configuration d'un slot

### 1. Configuration via action dans Ableton Live

Cette méthode utilise l'interaction directe avec Ableton Live pour configurer un slot.

#### Processus :

1. **Activation du mode apprentissage** :
   - L'utilisateur touche un slot vide sur l'interface ESP32
   - L'application envoie `StartLearning(slotIndex)` qui :
     - Met à jour l'état interne (`state.IsLearning = true`)
     - Envoie des messages de configuration à Live (`/live/learn/setup_track`, `/live/learn/setup_device`)
     - Met à jour l'affichage sur l'ESP32 (`ll,<slot>` et `ls,<slot>,learn,-,-,-`)

2. **Manipulation dans Live** :
   - L'utilisateur manipule un paramètre dans Live (ex: volume d'une piste)
   - Live détecte cette action et envoie un message OSC correspondant (ex: `/live/tracklearn/get/volume`)

3. **Traitement du message** :
   - L'application reçoit le message et appelle le handler approprié (ex: `handleTrackLearningVolume`)
   - Le handler met à jour les informations du slot avec les données reçues
   - L'application envoie un message de confirmation à Live (`/live/learn/slot`)
   - L'application met à jour l'affichage sur l'ESP32 (`ls` et `lo`)
   - Le mode apprentissage est désactivé (`disableLearningMode()`)

### 2. Configuration via simulation avec l'ESP32

Cette méthode utilise une "simulation" pour configurer un slot sans interaction directe avec Live.

#### Processus :

1. **Préparation des données** :
   - Dans un autre mode (ex: mode Volume), l'utilisateur sélectionne un paramètre
   - Le mode crée un objet `LearnData` contenant les informations du paramètre
   - Ces données sont stockées dans le `LiveModeManager`

2. **Activation du mode Learn** :
   - L'utilisateur passe au mode Learn
   - Les données `LearnData` sont conservées dans le `LiveModeManager`

3. **Sélection d'un slot** :
   - L'utilisateur touche un slot sur l'ESP32
   - L'application vérifie si des données `LearnData` sont disponibles
   - Si oui, elle active le mode apprentissage pour ce slot

4. **Simulation de la réponse** :
   - Au lieu d'attendre un message de Live, l'application simule la réponse en appelant directement le handler approprié
   - Par exemple, pour un paramètre de volume (type 1), elle appelle `handleTrackLearningVolume` avec les données du `LearnData`
   - Le reste du processus est identique à la méthode 1 (mise à jour du slot, envoi de confirmation, etc.)

5. **Nettoyage** :
   - Après la simulation, les données `LearnData` sont effacées (sauf pour certains types de paramètres qui nécessitent des étapes supplémentaires)

## Différences entre les deux méthodes

| Aspect | Via Live | Via Simulation (ESP32) |
|--------|----------|------------------------|
| Source des données | Messages OSC de Live | Objet `LearnData` préparé à l'avance |
| Interaction utilisateur | Manipulation directe dans Live | Sélection dans un autre mode puis toucher d'un slot |
| Timing | Asynchrone (dépend de la réponse de Live) | Immédiat (simulation directe) |
| Avantages | Flexibilité, accès à tous les paramètres | Rapidité, workflow optimisé |
| Limitations | Nécessite une interaction avec Live | Limité aux paramètres déjà sélectionnés |

## Flux de données

### Configuration via Live
```
Utilisateur → ESP32 → Application → Live → Application → ESP32
                       (StartLearning)  (message OSC)  (mise à jour de l'affichage)
```

### Configuration via Simulation
```
Utilisateur → Mode Volume → LiveModeManager → Mode Learn → ESP32
              (sélection)    (stockage LearnData)  (simulation)  (mise à jour de l'affichage)
```

## Messages d'affichage

Les messages envoyés à l'ESP32 pour l'affichage des slots suivent ce format :

- `ls,<slot>,<type>,<value>,<display>` : État d'un slot
  - `slot` : Index relatif du slot sur la page courante (0-7)
  - `type` : Type de paramètre (volume, pan, etc.)
  - `value` : Valeur numérique (0-100 pour volume, 0-100 pour pan)
  - `display` : Affichage formaté (ex: "-3.0 dB", "25%R")

- `lo,<slot>,<device>,<track>,<color>` : Propriétés d'un slot
  - `slot` : Index relatif du slot sur la page courante (0-7)
  - `device` : Nom du device (préfixé par "• " si présent)
  - `track` : Nom de la piste
  - `color` : Couleur de la piste (format hexadécimal)

## Conclusion

Le mode Learn offre deux méthodes complémentaires pour configurer les slots d'apprentissage, permettant une grande flexibilité dans l'utilisation de l'application. La méthode via Live est plus interactive et permet d'accéder à tous les paramètres disponibles, tandis que la méthode via simulation offre un workflow plus rapide pour les paramètres déjà sélectionnés dans d'autres modes.
