# Documentation du Mode Volume

## Introduction

Le Mode Volume est l'un des modes principaux de l'application OSCBridge. Il permet de contrôler les volumes, panoramiques et envois (sends) des pistes dans Ableton Live via une interface matérielle (hardware) connectée par OSC.

## Structure du Mode Volume

Le Mode Volume est implémenté dans le package `liveVolumeMode` et se compose de plusieurs fichiers :

- `liveVolumeMode.go` : Fichier principal contenant la structure et les méthodes de base
- `liveVolume_types.go` : Définition des types et constantes
- `liveVolume_oscHandlers.go` : Gestion des messages OSC
- `liveVolume_hardwareHandlers.go` : Gestion des événements matériels
- `liveVolume_pageHandlers.go` : Gestion des changements de page
- `liveVolume_displayManager.go` : Gestion de l'affichage

## Fonctionnalités principales

### Sous-modes

Le Mode Volume propose plusieurs sous-modes :

1. **Volume (vo)** : Contrôle du volume des pistes
2. **Pan (pa)** : Contrôle du panoramique des pistes
3. **Sends (s0, s1, ...)** : Contrôle des envois vers les pistes de retour

### Pagination

Le Mode Volume affiche 8 pistes à la fois (SlotsPerPage = 8) et permet de naviguer entre les pages pour accéder à toutes les pistes du projet.

- Chaque slot est identifié par une lettre (A à H)
- La navigation entre les pages se fait via les boutons Page Up/Down
- L'affichage indique la page actuelle et le nombre total de pages

### Verrouillage de page

Le Mode Volume permet de verrouiller la page actuelle pour éviter les changements automatiques de page lors de la sélection d'une piste :

- Lorsque le verrouillage est désactivé, la page change automatiquement pour afficher la piste sélectionnée
- Lorsque le verrouillage est activé, la page reste fixe quelle que soit la piste sélectionnée

## Flux de données

1. **Réception des données** : Les messages OSC sont reçus depuis Ableton Live
2. **Traitement des données** : Les données sont traitées et stockées dans la structure `tracksData`
3. **Affichage** : Les données sont envoyées au matériel via le `displayManager`

### Structure des données

Chaque piste est représentée par une structure `TrackData` contenant :

- Volume
- Panoramique
- Envois (sends)
- Couleur
- Nom
- État de repliage (fold state)
- État de mute
- État d'armement (arm)

## Communication OSC

### Messages entrants

Le Mode Volume reçoit plusieurs types de messages OSC :

- `/live/volumeMode/get/volume` : Valeur de volume d'une piste
- `/live/volumeMode/get/pan` : Valeur de panoramique d'une piste
- `/live/volumeMode/get/color` : Couleur d'une piste
- `/live/volumeMode/get/name` : Nom d'une piste
- `/live/volumeMode/get/send` : Valeur d'un envoi
- `/live/volumeMode/get/is_foldable` : Si une piste est repliable
- `/live/volumeMode/get/mute_state` : État de mute d'une piste
- `/live/volumeMode/get/arm` : État d'armement d'une piste
- `/live/volumeMode/structure/*` : Messages de structure pour initialiser les données

### Messages sortants

Le Mode Volume envoie plusieurs types de messages au matériel :

- `mo,1` : Activation du mode volume
- `vp1/4` : Information de page (page 1 sur 4)
- `stA` : Sélection du slot A
- `coA16711680` : Couleur du slot A (format RGB en décimal)
- `naANom de la piste` : Nom du slot A
- `vo,A100,-0.0 dB` : Volume du slot A (100%, 0.0 dB)
- `pa,A50,C` : Panoramique du slot A (50%, centré)
- `vs,2,Reverb` : Nom du send actif (mode send 2, "Reverb")

## Interaction avec le matériel

Le Mode Volume gère trois types d'événements matériels :

1. **Touch** : Événements tactiles (sélection de piste, mute, solo)
2. **Button** : Événements de bouton (changement de page, verrouillage, changement de mode)
3. **Encoder** : Événements d'encodeur rotatif (ajustement de volume, pan, send)

### Exemples d'interactions

- Toucher un slot pour sélectionner une piste
- Utiliser les encodeurs pour ajuster le volume/pan/send
- Appuyer sur les boutons de page pour naviguer entre les pages
- Appuyer sur le bouton de verrouillage pour activer/désactiver le verrouillage de page
- Appuyer sur les boutons de mode pour changer de sous-mode (volume, pan, sends)

## Cycle de vie

1. **Initialisation** : `Initialize()` configure les handlers OSC et les écouteurs d'événements
2. **Activation** : `Activate()` active le mode et configure les handlers matériels
3. **Désactivation** : `Deactivate()` désactive le mode et supprime les handlers matériels
4. **Nettoyage** : `CleanupForExit()` nettoie toutes les ressources avant la sortie de l'application

## Gestion de l'affichage

Le `LiveVolumeDisplayManager` gère l'affichage des données sur le matériel :

- Mise à jour des valeurs de volume, pan et sends
- Affichage des noms et couleurs des pistes
- Gestion des slots vides
- Mise à jour de la sélection de piste
- Mise à jour des informations de page

## Conclusion

Le Mode Volume offre une interface complète pour contrôler les paramètres de mixage des pistes dans Ableton Live. Sa structure modulaire permet une gestion efficace des données et une communication fluide avec le matériel via OSC.
