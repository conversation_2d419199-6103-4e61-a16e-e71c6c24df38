# Mode Track - Documentation

## Introduction

Le Mode Track est l'un des modes principaux de l'application, permettant de contrôler et visualiser les paramètres des pistes dans Ableton Live. Il offre un accès rapide aux fonctionnalités essentielles comme le volume, le panoramique, les sends, ainsi que les états mute, solo et arm.

## Fonctionnalités principales

- Affichage et contrôle du volume des pistes
- Ajustement du panoramique
- Gestion des sends vers les pistes de retour
- Contrôle des états mute, solo et arm
- Verrouillage sur une piste spécifique (mode Lock)
- Accès rapide via QuickView

## Structure du mode

Le mode Track est implémenté par la structure `LiveTrackMode` qui gère:
- L'affichage des informations de piste via `LiveTrackDisplayManager`
- Les interactions avec le matériel via les gestionnaires d'événements
- La communication avec Ableton Live via des messages OSC
- La gestion de l'état du mode (normal, verrouillé, QuickView)

## Le QuickView

### Concept

Le QuickView est une fonctionnalité spéciale du mode Track qui permet d'accéder rapidement aux contrôles de piste sans quitter complètement le mode actuel. C'est un mode temporaire qui s'active par-dessus le mode courant, permettant de visualiser et modifier les paramètres de la piste sélectionnée, puis de revenir facilement au mode précédent.

### Caractéristiques du QuickView

- **Accès temporaire**: Le QuickView est conçu comme un accès rapide et temporaire au mode Track
- **Conservation du contexte**: Le système mémorise le mode précédent pour y revenir après utilisation
- **Interface simplifiée**: Affiche uniquement les informations essentielles de la piste
- **Fonctionnalités limitées**: Certaines fonctionnalités comme le verrouillage de piste ne sont pas disponibles en QuickView

### Activation et désactivation

Le QuickView s'active via:
- Un appel à `EnterTrackQuickView()` du `LiveModeManager`
- L'émission de l'événement `requestEnterQuickView`

Il se désactive via:
- Un appel à `ExitTrackQuickView()` du `LiveModeManager`
- L'émission de l'événement `requestExitQuickView`
- L'appui sur le bouton d'index 5 du contrôleur

### Différences avec le mode Track normal

| Fonctionnalité | Mode Track normal | QuickView |
|----------------|-------------------|-----------|
| Verrouillage de piste | Disponible | Non disponible |
| Affichage | Complet | Simplifié |
| Persistance | Mode principal | Temporaire |
| Message OSC | `tq1` | `tq0` |

## Interface utilisateur

### Affichage principal

Le mode Track affiche les informations suivantes pour la piste sélectionnée ou verrouillée:

```
sltXXXXXX,index,couleur,nom,numéro,volume%,volumeDb
```

Où:
- `sltXXXXXX` est un préfixe contenant des flags d'état (foldable, fold state, grouped, mute, solo, arm)
- `index` est l'index d'affichage de la piste
- `couleur` est la couleur de la piste en format décimal
- `nom` est le nom de la piste (limité à 20 caractères)
- `numéro` est le numéro formaté de la piste (ex: "1/10")
- `volume%` est le pourcentage de volume (0-100)
- `volumeDb` est la valeur du volume en dB

### Contrôles matériels

Le mode Track répond aux événements matériels suivants:

#### Boutons
- **Bouton 0**: Page précédente (sends)
- **Bouton 1**: Page suivante (sends)
- **Bouton 2**: Verrouiller/déverrouiller la piste (non disponible en QuickView)
- **Bouton 5**: Sortir du QuickView (uniquement en QuickView)

#### Encodeurs
- **Encodeur 0**: Ajuster le volume
- **Encodeur 1**: Ajuster le panoramique
- **Encodeurs 2-5**: Ajuster les sends (selon la page actuelle)

#### Zones tactiles
- **mut**: Basculer l'état mute
- **sol**: Basculer l'état solo
- **arm**: Basculer l'état arm
- **lkt**: Verrouiller/déverrouiller la piste
- **paninit**: Réinitialiser le panoramique à 0
- **volinit**: Réinitialiser le volume à 0.85 (environ -1.4dB)

## Messages OSC

### Messages entrants

Le mode Track écoute plusieurs adresses OSC pour recevoir des mises à jour:

- `/live/trackMode/get/volume`: Mise à jour du volume
- `/live/trackMode/get/panning`: Mise à jour du panoramique
- `/live/trackMode/get/name`: Mise à jour du nom
- `/live/trackMode/get/color`: Mise à jour de la couleur
- `/live/trackMode/get/mute`: Mise à jour de l'état mute
- `/live/trackMode/get/solo`: Mise à jour de l'état solo
- `/live/trackMode/get/arm`: Mise à jour de l'état arm
- `/live/trackMode/get/sends`: Mise à jour des sends

### Messages sortants

Le mode Track envoie plusieurs types de messages au matériel:

- `mo,0`: Indication du mode Track
- `tq0`/`tq1`: État du QuickView (0=actif, 1=inactif)
- `lt,0`/`lt,1`: État du verrouillage (0=déverrouillé, 1=verrouillé)
- `sltXXXXXX,...`: Informations principales de la piste
- `pan,valeur`: Valeur du panoramique
- `ts,index,valeur`: Valeur d'un send

## Exemples d'utilisation

### Scénario 1: Ajustement rapide du volume d'une piste

1. Depuis un autre mode, activer le QuickView
2. Utiliser l'encodeur 0 pour ajuster le volume
3. Sortir du QuickView pour revenir au mode précédent

### Scénario 2: Configuration complète d'une piste

1. Passer en mode Track normal
2. Ajuster le volume avec l'encodeur 0
3. Ajuster le panoramique avec l'encodeur 1
4. Configurer les sends avec les encodeurs 2-5
5. Utiliser les zones tactiles pour mute/solo/arm si nécessaire

### Scénario 3: Verrouillage sur une piste

1. Passer en mode Track normal
2. Sélectionner la piste souhaitée dans Ableton Live
3. Appuyer sur le bouton 2 ou la zone tactile "lkt" pour verrouiller
4. Continuer à contrôler cette piste même si la sélection change dans Live
5. Appuyer à nouveau sur le même bouton pour déverrouiller

## Implémentation technique

Le mode Track est implémenté dans plusieurs fichiers:

- `liveTrackMode.go`: Structure principale et logique du mode
- `liveTrack_displayManager.go`: Gestion de l'affichage
- `liveTrack_oscHandlers.go`: Gestion des messages OSC
- `liveTrack_hardwareHandlers.go`: Gestion des événements matériels
- `liveTrack_types.go`: Définition des types et constantes

## Conclusion

Le mode Track, avec sa fonctionnalité QuickView, offre une interface flexible pour contrôler les paramètres essentiels des pistes dans Ableton Live. Sa conception permet à la fois un accès rapide via QuickView et un contrôle approfondi en mode normal, s'adaptant ainsi aux différents flux de travail des utilisateurs.
