# Handlers dans les fichiers du mode Learn

## Handlers Background (toujours actifs)

### Handlers de slots (liveLearn_slotHandlers.go)

1. `handleSlotProperties` - Gère les propriétés des slots
2. `handleSlotCleared` - Gère l'effacement d'un slot

### Handlers de paramètres de piste (liveLearn_trackParamHandlers.go)

3. `handleTrackLearnGetVolume` - Récupère le volume d'une piste
4. `handleTrackLearnGetPanning` - Récupère le panoramique d'une piste
5. `handleTrackLearnGetSends` - Récupère les envois d'une piste
6. `handleTrackLearnGetMute` - Récupère l'état de mute d'une piste
7. `handleTrackLearnGetSolo` - Récupère l'état de solo d'une piste

### Handlers de paramètres de device (liveLearn_deviceParamHandlers.go)

8. `handleDeviceLearnGetParamValue` - Récupère la valeur d'un paramètre de device
9. `handleDeviceLearningBulkParams` - <PERSON>ère les paramètres en masse d'un device
10. `handleDeviceLearningName` - Gère le nom d'un device

### Handlers de paramètres de chaîne (liveLearn_chainParamHandlers.go)

11. `handleChainLearnGetVolume` - Récupère le volume d'une chaîne
12. `handleChainLearnGetPanning` - Récupère le panoramique d'une chaîne
13. `handleChainLearnGetMute` - Récupère l'état de mute d'une chaîne
14. `handleChainLearnGetSolo` - Récupère l'état de solo d'une chaîne

## Handlers d'apprentissage (liveLearn_learningHandlers)

### Handlers d'apprentissage de paramètres de piste

15. `handleTrackLearningVolume` - Apprentissage du volume d'une piste
16. `handleTrackLearningPanning` - Apprentissage du panoramique d'une piste
17. `handleTrackLearningSends` - Apprentissage des envois d'une piste
18. `handleTrackLearningMute` - Apprentissage de l'état de mute d'une piste
19. `handleTrackLearningSolo` - Apprentissage de l'état de solo d'une piste

### Handlers d'apprentissage de paramètres de device

20. `handleDeviceLearningParamValue` - Apprentissage de la valeur d'un paramètre de device
21. `handleDeviceLearningParamValueStr` - Apprentissage de la valeur d'un paramètre de device (version chaîne)
22. `handleGetLockedDeviceParamProps` - Récupère les propriétés d'un paramètre de device verrouillé

### Handlers d'apprentissage de paramètres de chaîne

23. `handleChainLearningVolume` - Apprentissage du volume d'une chaîne
24. `handleChainLearningPanning` - Apprentissage du panoramique d'une chaîne
25. `handleChainLearningMute` - Apprentissage de l'état de mute d'une chaîne
26. `handleChainLearningSolo` - Apprentissage de l'état de solo d'une chaîne

### Autres handlers d'apprentissage

27. `handleReadyToListen` - Indique que Live est prêt à écouter les messages d'apprentissage

## Méthodes d'enregistrement et de désenregistrement (liveLearn_registration.go)

- `registerBackgroundOSCHandlers` - Enregistre les handlers OSC toujours actifs
- `unregisterBackgroundOSCHandlers` - Supprime les handlers OSC toujours actifs
- `registerActiveOSCHandlers` - Enregistre les handlers OSC actifs uniquement quand le mode est actif
- `unregisterActiveOSCHandlers` - Supprime les handlers OSC actifs
