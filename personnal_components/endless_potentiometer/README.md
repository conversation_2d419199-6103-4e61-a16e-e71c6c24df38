# Potentiomètre rotatif **sans butée** à double piste – Guide Complet

Ce document décrit en détail le fonctionnement matériel et logiciel du module
`endless_potentiometer` qui permet d'exploiter un potentiomètre rotatif **à
rotation infinie** muni de **deux sorties analogiques** (pistes A et B).

Les sections suivantes couvrent : le principe physique du capteur, le schéma
haut-niveau de la pile logicielle, les algorithmes internes,
la signification de chaque paramètre, ainsi que la méthodologie de réglage.

---

## 1. Spécificité du potentiomètre double piste

Un potentiomètre standard fournit une seule tension qui varie de 0 % à 100 %
quand on atteint la butée mécanique. Sur un modèle **sans butée** la piste est
circulaire et deux sorties séparées (A et B) donnent des tensions sinusoïdales
**décalées de 90 °**. Ce déphasage permet de déterminer le sens de rotation en
comparant l'évolution relative des deux voies.

```
A ────╮    tension 0-100-0-100-…
       │ décalage 90°
B ──╮  │    tension 0-100-0-100-…
    └──╯
```

Les valeurs sont lues par l'ADC sur 12 bits (0-4095).

---

## 2. Architecture logicielle

```
+-------------------+           +--------------------+
|     Hardware      |           |      Firmware      |
| Potentiomètre A/B |─ ADC ───►|  EndlessPot (C++)  |⇢ sens + delta
+-------------------+           +--------------------+
                                            │
                                            ▼
                                    +--------------------+
                                    |  InfiniPot (C++)   |⇢ position absolue
                                    +--------------------+
```

* **EndlessPot** : analyse instantanément une paire de mesures A/B puis calcule :
  * direction (`CLOCKWISE` ou `COUNTER_CLOCKWISE`)
  * amplitude du mouvement (`valueChanged`)

* **InfiniPot** : ajoute des couches de filtrage et de gestion de zone morte :
  * filtre médian sur `READS` lectures
  * zone morte adaptative (`dzValue`) qui grandit pendant la rotation
  * régulateur `stepAccumulator` qui délivre au maximum un pas par
    actualisation afin d'éliminer les bonds

---

## 3. Algorithmes détaillés

### 3.1 Filtrage médian

Pour chaque boucle, `READS` (impair, par défaut 5) échantillons sont prélevés
sur chaque piste. La médiane garantit la suppression des valeurs aberrantes
sans introduire de retard excessif.

### 3.2 Détection du sens et magnitude (classe `EndlessPot`)

1. **Seuil inclusif** :
   ```cpp
   if (valueA >= previousA + threshold) dirA = UP;
   if (valueA <= previousA - threshold) dirA = DOWN;
   ```
   Avec `threshold = 1`, la plus petite variation est détectée.
2. **Combinaison des directions A/B** pour déduire le sens global, en
   intégrant une *safety net* qui évite les inversions sur les extrémités non
   linéaires.
3. **Sélection de la piste la plus linéaire** puis calcul du delta minimal
   entre A et B.
4. **Accumulation des micro-variations** : `pendingChange` additionne les
   écarts plus petits que `sensitivity` jusqu'à produire un pas exploitable.

### 3.3 Zone morte dynamique & accumulation (classe `InfiniPot`)

```
dzValue   ← min(dzValue + |delta|, DZ_MAX_VALUE)
if dzValue > DZ_THRESHOLD :
    stepAccumulator += delta   # mouvement validé
else :
    stepAccumulator += delta   # mouvement stocké
```

`dzValue` décroît avec le temps selon `DZ_DECAY_DIVISOR` (μs) afin de revenir
progressivement à une pleine sensibilité après un geste rapide.

### 3.4 Régulateur de pas (`stepAccumulator`)

À chaque appel de `update()` :
```cpp
if (stepAccumulator > 0)  { pot_value += 1; stepAccumulator -= 1; }
if (stepAccumulator < 0)  { pot_value -= 1; stepAccumulator += 1; }
```
Cela garantit :
* **aucun bond** même si un grand delta arrive d'un coup,
* **prise en compte immédiate** des micro-mouvements car un pas maximal sort
  à chaque cycle.

---

## 4. Tableau complet des paramètres

| Paramètre                   | Valeur par défaut | Rôle précis | Effet bas    | Effet haut   |
|-----------------------------|-------------------|-------------|--------------|--------------|
| `READS`                     | 5                 | taille du filtre médian | faible latence | filtrage élevé |
| `threshold`                 | 1                 | détection UP/DOWN | très sensible | ignore micro crans |
| `sensitivity`               | 2                 | division du delta | sortie fine | moins de pas |
| `DZ_THRESHOLD`              | 8                 | déclencheur de zone morte | bouge tout de suite | bonne stabilité |
| `DZ_DECAY_DIVISOR`          | 5000              | vitesse de décroissance | revient vite | inertia longue |
| `DZ_MAX_VALUE`              | 80                | plafond de dzValue | peu d'inertie | grande tolérance |
| `flushTimeoutUs` (`EndlessPot`) | 150 000      | délai avant flush auto | réaction rapide | pas de flush |

---

## 5. Méthodologie de réglage pas à pas

1. **Filtre** : choisir `READS` impair. Commencer à 5.
2. **Seuil** : régler `threshold` sur 1, puis augmenter uniquement si la sortie
   oscille alors que le bouton est immobile.
3. **Sensibilité** : démarrer à 2 pour un pas toutes les deux unités ADC.
4. **Zone morte** : augmenter `DZ_THRESHOLD` si des tremblements subsistent.
5. **Décroissance** : ajuster `DZ_DECAY_DIVISOR` pour contrôler la durée
   pendant laquelle `dzValue` reste élevée après un mouvement rapide.
6. **Inertie** : jouer sur `DZ_MAX_VALUE` quand les très grands gestes gênent
   la précision immédiatement après arrêt.

Toujours modifier **un seul** paramètre à la fois et mesurer l'impact.

---

## 6. Boucle de lecture type

```cpp
endless_pot::InfiniPot pot(8, 4000, 60); // dzThreshold, dzDecayDivisor, dzMax

pot.setSensitivity(2);
pot.setFlushTimeoutUs(120000); // 120 ms

pot.runReadLoop(
    adc_read,    // lambda ou fonction ADC(channel)
    CHANNEL_A,
    CHANNEL_B,
    5,           // délai en ms entre deux boucles
    [](auto &p) {
        printf("pos=%d\n", p.getPotValue());
    }
);
```

---

## 7. Bonnes pratiques matérielles

* **Couplage RC** 1 kΩ / 100 nF sur chaque voie pour filtrer le bruit haute
  fréquence.
* **Référence de masse** soignée entre la carte microcontrôleur et le
  potentiomètre.
* **Câblage torsadé** pour les pistes A et B afin de réduire le couplage
  capacitif vers les lignes de puissance.

---

## 8. Foire aux questions

| Question | Réponse |
|----------|---------|
| La valeur reste figée après un geste rapide | Réduire `DZ_DECAY_DIVISOR` ou `DZ_MAX_VALUE` |
| La valeur bouge toute seule | Augmenter `threshold` ou `READS` |
| Les pas sont trop rapides | Augmenter `sensitivity` ou `READS` |
| Les pas sont manquants à très basse vitesse | Réduire `threshold` à 1 et vérifier comparaisons inclusives |

© 2024 – Module `endless_potentiometer` – Licence MIT 