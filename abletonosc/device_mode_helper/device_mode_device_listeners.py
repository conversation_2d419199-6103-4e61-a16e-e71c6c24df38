from typing import Dict, Any, Callable
import logging
import traceback
import json
import unicodedata
from ..utils.parameter_throttler import ParameterThrottler




def _setup_device_listeners(self, device, mode_prefix: str, listeners_dict: Dict, track_id: int = -3):
        """
        Configuration générique des listeners pour un device
        mode_prefix: 'deviceMode' ou 'deviceLockMode'
        listeners_dict: self.deviceMode_listeners ou self.deviceLockMode_listeners        """
        
        
        if not device or not hasattr(device, 'parameters') or not device.parameters:
            self.logger.warning("Le device n'a pas de paramètres")
            return

        # Initialiser le throttler s'il n'existe pas déjà
        if not hasattr(self, 'parameter_throttler'):
            self.parameter_throttler = ParameterThrottler()

        # Préparation de toutes les informations en une seule fois
        bulk_data = []
        for param_index, p in enumerate(device.parameters):
            bulk_data.extend([
                param_index,
                p.name,
                p.is_quantized,
                p.min,
                p.max,
                p.value,
                p.str_for_value(p.value)
            ])

        # Envoi de toutes les informations en un seul message
        self.osc_server.send("/live/deviceMode/get/parameters/bulk", 
                            (track_id, len(device.parameters), *bulk_data))

        # Configuration des listeners pour tous les paramètres
        for param_index, parameter in enumerate(device.parameters):
            def create_parameter_callback(param_index, parameter, is_selected_device):
                def callback():
                    # Éviter l'envoi pendant un toggle explicite
                    if hasattr(self.manager, 'is_toggling_device') and self.manager.is_toggling_device:
                        return
                        
                    value = parameter.value
                    value_string = parameter.str_for_value(value)
                    
                    # Créer un identifiant unique pour ce paramètre
                    param_id = f"{device.name}_{param_index}"
                    
                    if param_index == 0:
                        # Pour le paramètre 0, on crée une fonction de callback spécifique
                        def send_param0_update(val):
                            if is_selected_device:
                                self.osc_server.send("/live/device/get/isActive", 
                                                   (track_id, val))
                            self.send_device_environment(device)
                            
                        self.parameter_throttler.update_parameter(param_id, value, send_param0_update)
                    else:
                        # Pour les autres paramètres
                        def send_param_update(val):
                            self.osc_server.send("/live/device/get/parameter/value", 
                                               (track_id, param_index, val, parameter.str_for_value(val)))
                            
                        self.parameter_throttler.update_parameter(param_id, value, send_param_update)
                
                return callback
            
            # Vérifie si c'est le device sélectionné
            is_selected_device = (device == self.song.view.selected_track.view.selected_device)
            callback = create_parameter_callback(param_index, parameter, is_selected_device)
            listeners_dict[f"parameter_{param_index}"] = (parameter, callback)
            parameter.add_value_listener(callback)

        # Ajouter les listeners pour les autres devices de la chaîne
        current_chain = device.canonical_parent
        if hasattr(current_chain, 'devices'):
            # Ajouter les listeners pour solo et mute de la chaîne active
            def create_chain_status_callback(chain):
                def callback():
                    self.send_device_environment(device)
                return callback
            
            chain_callback = create_chain_status_callback(current_chain)
            listeners_dict[f"chain_{id(current_chain)}_solo"] = (current_chain, chain_callback)
            listeners_dict[f"chain_{id(current_chain)}_mute"] = (current_chain, chain_callback)
            current_chain.add_solo_listener(chain_callback)
            current_chain.add_mute_listener(chain_callback)
            
            # Ajouter les listeners pour les chaînes sœurs
            parent_rack = current_chain.canonical_parent
            if hasattr(parent_rack, 'chains'):
                for sister_chain in parent_rack.chains:
                    if sister_chain != current_chain:
                        sister_callback = create_chain_status_callback(sister_chain)
                        listeners_dict[f"chain_{id(sister_chain)}_solo"] = (sister_chain, sister_callback)
                        listeners_dict[f"chain_{id(sister_chain)}_mute"] = (sister_chain, sister_callback)
                        sister_chain.add_solo_listener(sister_callback)
                        sister_chain.add_mute_listener(sister_callback)
            
            # Ajouter les listeners pour les autres devices de la chaîne
            for other_device in current_chain.devices:
                if other_device != device and hasattr(other_device, 'parameters') and other_device.parameters:
                    try:
                        param_0 = other_device.parameters[0]
                        def create_other_device_callback(other_device):
                            def callback():
                                # Vérifier si l'appareil existe toujours
                                if other_device and hasattr(other_device, 'name'):
                                    self.logger.info(f"Appel dans setup_device_listeners: Changement détecté sur l'appareil: {other_device.name}")
                                    self.send_device_environment(other_device)
                            return callback
                        
                        other_callback = create_other_device_callback(other_device)
                        # Utiliser une clé unique pour chaque appareil
                        listener_key = f"other_device_{id(other_device)}_param_0"
                        
                        # Vérifier si on a déjà un listener pour ce paramètre
                        if listener_key in listeners_dict:
                            # Supprimer l'ancien listener s'il existe
                            old_obj, old_callback = listeners_dict[listener_key]
                            if old_obj and hasattr(old_obj, 'value_has_listener') and old_obj.value_has_listener(old_callback):
                                old_obj.remove_value_listener(old_callback)
                        
                        # Ajouter le nouveau listener
                        listeners_dict[listener_key] = (param_0, other_callback)
                        param_0.add_value_listener(other_callback)
                        self.logger.info(f"Listener ajouté pour {other_device.name} (ID: {id(other_device)})")
                    except Exception as e:
                        self.logger.error(f"Erreur lors de l'ajout du listener pour {other_device.name}: {str(e)}")
                        import traceback
                        self.logger.error(traceback.format_exc())

        self.send_device_environment(device)

def _stop_device_listeners(self, listeners_dict: Dict):
        """Méthode générique pour arrêter les listeners"""
        if not listeners_dict:
            return
                
        # D'abord arrêter les listeners de chaîne
        self._stop_chain_listeners()
        
        for key, (obj, callback) in list(listeners_dict.items()):
            try:
                if obj:
                    # Gestion des différents types de listeners
                    if key == "device_name" and hasattr(obj, 'name_has_listener'):
                        if obj.name_has_listener(callback):
                            obj.remove_name_listener(callback)
                    elif key == "track_name" and hasattr(obj, 'name_has_listener'):
                        if obj.name_has_listener(callback):
                            obj.remove_name_listener(callback)
                    elif key == "track_color" and hasattr(obj, 'color_has_listener'):
                        if obj.color_has_listener(callback):
                            obj.remove_color_listener(callback)
                    elif key.startswith("parameter_") and hasattr(obj, 'value_has_listener'):
                        if obj.value_has_listener(callback):
                            obj.remove_value_listener(callback)
                    elif key.startswith("chain_") and key.endswith("_solo"):
                        if obj.solo_has_listener(callback):
                            obj.remove_solo_listener(callback)
                    elif key.startswith("chain_") and key.endswith("_mute"):
                        if obj.mute_has_listener(callback):
                            obj.remove_mute_listener(callback)
                    elif key.startswith("other_device_") and key.endswith("_param_0"):
                        # Vérification plus stricte pour les listeners d'autres appareils
                        if hasattr(obj, 'value_has_listener') and obj.value_has_listener(callback):
                            try:
                                obj.remove_value_listener(callback)
                                self.logger.info(f"Listener supprimé pour {key}")
                            except Exception as e:
                                self.logger.error(f"Échec de suppression du listener pour {key}: {str(e)}")
                    elif key == "selected_chain" and hasattr(obj, 'selected_chain_has_listener'):
                        if obj.selected_chain_has_listener(callback):
                            obj.remove_selected_chain_listener(callback)
            except Exception as e:
                self.logger.error(f"Erreur lors de la suppression du listener pour {key}: {str(e)}")
                import traceback
                self.logger.error(traceback.format_exc())
        
        # Ajouter un log pour mieux suivre les opérations
        self.logger.info(f"Nettoyage de {len(listeners_dict)} listeners terminé")
        listeners_dict.clear()

def start_selected_device_listeners(self, *args):
        self.logger.warning("=== DÉBUT start_selected_device_listeners ===")
        # D'abord arrêter les listeners existants et réinitialiser le chemin
        self._current_chain_path = None
        self.stop_selected_device_listeners()
        
        selected_track = self.song.view.selected_track
        
        # Configuration des listeners de piste (déplacé avant la vérification du device)
        def track_properties_callback():
            track_name = selected_track.name
            track_color = selected_track.color
            track_index = list(self.song.tracks).index(selected_track) if selected_track in self.song.tracks else -1
            
            self.osc_server.send("/live/deviceMode/selTrack/properties", 
                                (track_name, track_color))
       
        self.deviceMode_listeners["track_name"] = (selected_track, track_properties_callback)
        self.deviceMode_listeners["track_color"] = (selected_track, track_properties_callback)
        
        selected_track.add_name_listener(track_properties_callback)
        selected_track.add_color_listener(track_properties_callback)
        
        # Envoi initial des propriétés de piste
        track_properties_callback()
        
        # Lancer la configuration récursive avec le contexte de la chaîne actuelle
        self._setup_rack_listeners_recursive(selected_track.devices, None)

        # Configuration du device sélectionné s'il existe
        selected_device = selected_track.view.selected_device
        if selected_device:
            self._setup_device_listeners(selected_device, "deviceMode", self.deviceMode_listeners)
            
            # Ajout du listener pour le nom du device
            self.deviceMode_listeners["device_name"] = (selected_device, self.send_device_environment)
            selected_device.add_name_listener(self.send_device_environment)
            
            self.manager.lastSelectedDevice = selected_device
        else:
            self.manager.lastSelectedDevice = None

def stop_selected_device_listeners(self, *args):
        self._current_chain_path = None
        self._stop_device_listeners(self.deviceMode_listeners)
        self.manager.lastSelectedDevice = None