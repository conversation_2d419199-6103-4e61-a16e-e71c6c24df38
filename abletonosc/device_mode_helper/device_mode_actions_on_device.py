from typing import Dict, Any, Callable
import logging
import traceback
import json
import unicodedata



def toggle_device_active(self, params):
        """
        Bascule l'état d'activation d'un device (toggle)
        params[0]: -3 (selected) ou -4 (locked)

        """
        try:
            device_mode = params[0]

            # Sélection du device approprié
            target_device = None
            if device_mode == -3:
                target_device = self.song.view.selected_track.view.selected_device
            elif device_mode == -4:
                target_device = self.manager.lockedDevice

            if target_device and target_device.parameters:
                # Mise en place d'un flag pour éviter l'envoi en double
                self.manager.is_toggling_device = True

                # Récupération de l'état actuel et inversion
                current_state = target_device.parameters[0].value
                new_state = 0.0 if current_state > 0 else 1.0

                # Application du nouvel état
                target_device.parameters[0].value = new_state

                # Envoi explicite de l'environnement mis à jour
                if device_mode == -3:
                    self.send_device_environment(target_device)

                # Réinitialisation du flag après un court délai
                self.manager.schedule_task(0.2, lambda: setattr(self.manager, 'is_toggling_device', False))

        except Exception as e:
            self.logger.error(f"Erreur dans toggle_device_active: {str(e)}")
            # Assurez-vous de réinitialiser le flag même en cas d'erreur
            self.manager.is_toggling_device = False

def toggle_device_active_by_path(self, params):
        """
        Bascule l'état d'activation d'un device en utilisant son chemin de navigation
        params: chemin JSON ou liste [rack,chain,...,device]
        """
        try:
            if not params:
                self.logger.warning("Aucun paramètre reçu")
                return
            raw = params[0]
            if isinstance(raw, (bytes, bytearray)):
                raw = raw.decode('utf-8')
            if isinstance(raw, str):
                path = json.loads(raw)
            else:
                path = raw

            cont = self.song.view.selected_track.devices
            target = None
            if len(path) == 1:
                idx = int(path[0])
                if idx < len(cont):
                    target = cont[idx]
            else:
                for i in range(0, len(path)-1, 2):
                    r, c = int(path[i]), int(path[i+1])
                    cont = cont[r].chains[c].devices
                idx = int(path[-1])
                if idx < len(cont):
                    target = cont[idx]

            if target and target.parameters:
                # Récupération de l'état actuel et inversion
                current_state = target.parameters[0].value
                new_state = 0.0 if current_state > 0 else 1.0

                # Application du nouvel état
                target.parameters[0].value = new_state

                #self.send_device_environment(target_device)
            else:
                self.logger.warning("Device cible non trouvé")

        except Exception as e:
            self.logger.error(f"Erreur dans toggle_device_active_by_path: {str(e)}")
            traceback.print_exc()
import json
import logging
import traceback

def move_device_by_path(self, params):
    """
    Déplace un device en utilisant deux chemins collés :
    params peut être une string JSON "[...]" ou une liste plate d'entiers.
    """
    try:
        if not params:
            self.logger.warning("Aucun paramètre reçu pour move_device_by_path")
            return

        # 1) Si c'est du JSON string/bytes -> parser
        first = params[0]
        if isinstance(first, (bytes, bytearray)):
            first = first.decode('utf-8')
        if isinstance(first, str):
            full = json.loads(first)
        else:
            # 2) sinon c'est déjà un flat-list : on convertit chaque élément
            full = [int(x) for x in params]

        # On sépare source et target
        half = len(full) // 2
        src_list = full[:half]
        tgt_list = full[half:]

        # Helper pour descendre le path
        def nav(path):
            cont = self.song.view.selected_track.devices
            chain = None
            for i in range(0, len(path) - 1, 2):
                r, c = int(path[i]), int(path[i + 1])
                rack = cont[r]
                chain = rack.chains[c]
                cont = chain.devices
            return (cont, chain, int(path[-1])) if chain else (cont, self.song.view.selected_track, int(path[-1]))

        src_cont, src_chain, src_idx = nav(src_list)
        tgt_cont, tgt_chain, tgt_idx = nav(tgt_list)

        device = src_cont[src_idx]
        self._stop_chain_listeners()
        final_pos = self.song.move_device(device, tgt_chain, tgt_idx)
        self.logger.info(f"Device déplacé à la position {final_pos}")

    except Exception as e:
        self.logger.error(f"Erreur dans move_device_by_path: {e}")
        traceback.print_exc()


def delete_device_by_path(self, params):
    """
    Supprime un device en utilisant un chemin :
    params peut être "[..]" ou une liste d'entiers.
    """
    try:
        if not params:
            self.logger.warning("Aucun paramètre reçu pour delete_device_by_path")
            return

        first = params[0]
        if isinstance(first, (bytes, bytearray)):
            first = first.decode('utf-8')
        if isinstance(first, str):
            path = json.loads(first)
        else:
            path = [int(x) for x in params]

        # Récupérer le device sélectionné pour comparaison
        selected_track = self.song.view.selected_track
        selected_device = selected_track.view.selected_device

        # Vérifier si le device à supprimer est le device sélectionné
        is_selected_device = False

        cont = selected_track.devices
        target_device = None
        parent_chain = None

        # Si chemin de longueur 1 -> piste principale
        if len(path) == 1:
            idx = int(path[0])
            if idx < len(cont):
                target_device = cont[idx]
                parent_chain = selected_track

                # Vérifier si c'est le device sélectionné
                if selected_device and target_device == selected_device:
                    is_selected_device = True

                # Supprimer le device
                selected_track.delete_device(idx)
                self.logger.info(f"Device supprimé à l'index {idx}")
            else:
                self.logger.warning(f"Index invalide: {idx}")
                return
        else:
            # Navigation racks/chains
            rack = None
            for i in range(0, len(path) - 1, 2):
                r, c = int(path[i]), int(path[i + 1])
                rack = cont[r]
                cont = rack.chains[c].devices

            idx = int(path[-1])
            if idx < len(cont):
                target_device = cont[idx]
                parent_chain = rack.chains[c]

                # Vérifier si c'est le device sélectionné
                if selected_device and target_device == selected_device:
                    is_selected_device = True

                # Supprimer le device
                parent_chain.delete_device(idx)
                self.logger.info(f"Device supprimé à l'index {idx}")
            else:
                self.logger.warning(f"Index invalide: {idx}")
                return

        # Si ce n'est pas le device sélectionné, forcer une mise à jour de l'environnement
        if not is_selected_device and parent_chain:
            self.logger.info("Forçage de la mise à jour de l'environnement après suppression d'un device non sélectionné")

            # Si parent_chain est une track, utiliser send_device_environment
            if parent_chain == selected_track:
                if selected_device:
                    self.send_device_environment(selected_device)
                else:
                    # Si pas de device sélectionné, envoyer un environnement vide
                    environment_data = {
                        "path": [],
                        "chains": [],
                        "devices": self._build_devices_data(selected_track)
                    }
                    self._send_environment_data(environment_data)
            else:
                # Sinon c'est une chain, utiliser send_chain_environment
                self.send_chain_environment(parent_chain)

    except Exception as e:
        self.logger.error(f"Erreur dans delete_device_by_path: {e}")
        traceback.print_exc()


def set_device_parameter_by_path(self, params):
    """
    Modifie un paramètre via son chemin :
    params peut être :
    - [param_index, value] pour les devices au premier niveau
    - ["[...]", param_index, value] pour les devices dans des racks (chemin JSON)
    - [rack, chain, ..., device, param_index, value] pour les devices dans des racks (flat list)
    """
    try:
        if len(params) < 2:
            self.logger.warning("Paramètres insuffisants pour set_device_parameter_by_path")
            return

        # Cas spécial : seulement param_index et value (device au premier niveau)
        if len(params) == 2:
            param_idx = int(params[0])
            new_val = float(params[1])
            device = self.song.view.selected_track.view.selected_device

            if device and hasattr(device, 'parameters'):
                if param_idx < len(device.parameters):
                    device.parameters[param_idx].value = new_val
                    self.logger.info(f"Paramètre {param_idx} changé à {new_val}")
                else:
                    self.logger.warning(f"Index de paramètre invalide: {param_idx}")
            return

        # Sinon, on a un chemin + param_index + value
        *raw_path, pidx, val = params

        # Parser le chemin
        if len(raw_path) == 1 and isinstance(raw_path[0], (bytes, bytearray, str)):
            s = raw_path[0]
            if isinstance(s, (bytes, bytearray)):
                s = s.decode('utf-8')
            path = json.loads(s)
        else:
            path = [int(x) for x in raw_path]

        param_idx = int(pidx)
        new_val = float(val)
        self.logger.info(f"Path:{path}, Param:{param_idx}, Value:{new_val}")

        # Navigation jusqu'au device
        cont = self.song.view.selected_track.devices
        target = None

        if len(path) == 1:
            idx = int(path[0])
            if idx < len(cont):
                target = cont[idx]
        else:
            for i in range(0, len(path) - 1, 2):
                r, c = path[i], path[i+1]
                rack = cont[int(r)]
                cont = rack.chains[int(c)].devices
            idx = int(path[-1])
            if idx < len(cont):
                target = cont[idx]

        # Application de la valeur
        if target and hasattr(target, 'parameters'):
            if param_idx < len(target.parameters):
                target.parameters[param_idx].value = new_val
                self.logger.info(f"Paramètre {param_idx} changé à {new_val}")
            else:
                self.logger.warning(f"Index de paramètre invalide: {param_idx}")
        else:
            self.logger.warning("Device cible non trouvé")

    except Exception as e:
        self.logger.error(f"Erreur dans set_device_parameter_by_path: {e}")
        traceback.print_exc()