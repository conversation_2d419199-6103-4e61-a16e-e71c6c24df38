from typing import Dict, Any, Callable
import logging
import traceback
import json
import unicodedata



def _get_chain_status(self, chain):
        """Calcule le statut d'une chaîne"""
        if chain.get("muted", False) and not chain.get("soloed", False):
            return 1
        elif not chain.get("muted", False) and chain.get("soloed", False):
            return 2
        elif chain.get("muted", False) and chain.get("soloed", False):
            return 3
        return 0

def _get_device_status(self, device):
        """Calcule le statut d'un device"""
        is_active = device["active"]
        can_have_chains = device["can_have_chains"]
        can_have_drum_pads = device["can_have_drum_pads"]
        
        if is_active:
            if can_have_drum_pads:
                return 2
            elif can_have_chains:
                return 1
            return 0
        else:
            if can_have_drum_pads:
                return 5
            elif can_have_chains:
                return 4
            return 3

def _build_chains_data(self, parent_rack):
        """Construit les données des chaînes pour un rack"""
        chains_data = []
        
        if parent_rack.can_have_drum_pads:
            for pad in parent_rack.drum_pads:
                if pad.chains:
                    chain_data = {
                        "note": pad.note,
                        "muted": pad.chains[0].mute,
                        "soloed": pad.chains[0].solo
                    }
                    chains_data.append({
                        "note": pad.note,
                        "status": self._get_chain_status(chain_data)
                    })
        else:
            chains_data = [
                {
                    "name": chain.name,
                    "status": self._get_chain_status({
                        "muted": chain.mute,
                        "soloed": chain.solo
                    })
                } for chain in parent_rack.chains
            ]
        
        return chains_data

def _build_devices_data(self, container):
        """Construit les données des devices pour un conteneur (chaîne ou piste)"""
        return [
            {
                "name": device.name,
                "status": self._get_device_status({
                    "active": device.is_active,
                    "can_have_chains": device.can_have_chains,
                    "can_have_drum_pads": device.can_have_drum_pads
                })
            } for device in container.devices
        ]

def send_chain_environment(self, chain):
        # Éviter l'envoi si on est en train de déplacer un device ou de faire un toggle
        if (hasattr(self.manager, 'is_moving_device') and self.manager.is_moving_device) or \
           (hasattr(self.manager, 'is_toggling_device') and self.manager.is_toggling_device):
            return
        
        try:
            self.logger.info("=== DÉBUT send_chain_environment ===")
            
            if not chain:
                self.logger.warning("Pas de chaîne fournie")
                return

            parent_rack = chain.canonical_parent
            if not hasattr(parent_rack, 'chains'):
                self.logger.warning("Le parent n'est pas un rack")
                return

            # Utilise le chemin stocké s'il existe, sinon le recalcule
            path = getattr(self, '_current_chain_path', None)
            if path is None:
                path = self._get_chain_path_indices(chain)

            environment_data = {
                "path": path,
                "chains": self._build_chains_data(parent_rack),
                "devices": self._build_devices_data(chain)
            }

            self.logger.info(f"Données structurées: {environment_data}")
            self._send_environment_data(environment_data)

        except Exception as e:
            self.logger.error(f"Erreur dans send_chain_environment: {str(e)}")

def send_device_environment(self, *args):
        # Éviter l'envoi redondant si c'est nous qui avons initié le toggle
        if hasattr(self.manager, 'is_toggling_device') and self.manager.is_toggling_device:
            # Si args[0] est un device (cas d'appel explicite dans toggle_device_active)
            if args and hasattr(args[0], 'parameters'):
                # On continue l'exécution car c'est un appel explicite
                pass
            else:
                # Sinon c'est un appel via le callback, on l'ignore
                return
                
        try:
            selected_track = self.song.view.selected_track
            if not selected_track or not selected_track.view.selected_device:
                return

            selected_device = selected_track.view.selected_device
            self.logger.info(f"Device sélectionné: {selected_device.name}")

            environment_data = {
                "path": [],
                "chains": [],
                "devices": []
            }

            current_chain = selected_device.canonical_parent
            parent_container = current_chain.canonical_parent if hasattr(current_chain, 'canonical_parent') else None

            if hasattr(parent_container, 'chains'):
                path = self._get_chain_path_indices(current_chain)
                device_index = list(current_chain.devices).index(selected_device)
                path.append(device_index)
                environment_data["chains"] = self._build_chains_data(parent_container)
            else:
                device_index = list(selected_track.devices).index(selected_device)
                path = [device_index]
                current_chain = selected_track

            environment_data["path"] = path
            environment_data["devices"] = self._build_devices_data(current_chain)

            self.logger.info(f"Données envoyées: {environment_data}")
            self._send_environment_data(environment_data)

        except Exception as e:
            self.logger.error(f"Erreur dans send_device_environment: {str(e)}")
            self.logger.error(traceback.format_exc())

def _send_environment_data(self, environment_data):
        try:
            # Vérifier si on est en mode device lock
            if self.manager.deviceLock:
                return
            
            path_string = self._build_path_string(environment_data["path"])
            self.osc_server.send("/live/deviceMode/get/path_string", (path_string,))
            
            is_drum_rack = 0
            if environment_data["chains"]:
                is_drum_rack = 1 if "note" in environment_data["chains"][0] else 0
            
            path_indices = environment_data["path"]
            path_count = len(path_indices)
            
            chain_data = []
            chain_count = len(environment_data["chains"])
            
            for chain in environment_data["chains"]:
                if "note" in chain:
                    chain_data.extend([chain["note"], chain["status"]])
                else:
                    chain_data.extend([
                        self._normalize_name(chain["name"]),
                        chain["status"]
                    ])
            
            device_data = []
            device_count = len(environment_data["devices"])
            for device in environment_data["devices"]:
                device_data.extend([
                    self._normalize_name(device["name"]),
                    device["status"]
                ])
            
            self.osc_server.send("/live/deviceMode/get/environment/", 
                                (is_drum_rack,
                                path_count, path_indices,
                                chain_count, chain_data,
                                device_count, device_data))
            
            self.logger.info(f"Message OSC envoyé: "
                            f"is_drum_rack={is_drum_rack}, "
                            f"path_count={path_count}, path={path_indices}, "
                            f"chain_count={chain_count}, chains={chain_data}, "
                            f"device_count={device_count}, devices={device_data}")
                            
        except Exception as e:
            self.logger.error(f"Erreur dans _send_environment_data: {str(e)}")

    

def _get_chain_path_indices(self, chain):
        try:
            self.logger.info(f"=== DÉBUT _get_chain_path_indices pour la chaîne: {chain.name} ===")
            
            def find_chain_path(devices, target_chain):
                for rack_index, device in enumerate(devices):
                    self.logger.info(f"Examen du device: {device.name} (index: {rack_index})")
                    
                    if hasattr(device, 'chains'):
                        # Vérifier si la chaîne cible est directement dans ce rack
                        for chain_index, current_chain in enumerate(device.chains):
                            self.logger.info(f"  Examen de la chaîne: {current_chain.name} (index: {chain_index})")
                            
                            if current_chain == target_chain:
                                self.logger.info(f"  Chaîne trouvée! Rack: {device.name}, Index: [{rack_index}, {chain_index}]")
                                return [rack_index, chain_index]
                            
                            # Chercher dans les devices de cette chaîne
                            if current_chain.devices:
                                deeper_path = find_chain_path(current_chain.devices, target_chain)
                                if deeper_path:
                                    self.logger.info(f"  Chemin plus profond trouvé: {[rack_index, chain_index] + deeper_path}")
                                    return [rack_index, chain_index] + deeper_path
                return None

            # Commencer la recherche depuis les devices de la piste
            selected_track = self.song.view.selected_track
            path = find_chain_path(selected_track.devices, chain) or []
            
            self.logger.info(f"=== FIN _get_chain_path_indices - Chemin final: {path} ===")
            return path
            
        except Exception as e:
            self.logger.error(f"Erreur dans _get_chain_path_indices: {str(e)}")
            return []

def send_drum_grid_state(self, drum_rack, path_indices):
        """
        Envoie uniquement la liste des pads utilisés dans le DrumRack
        Format: [note1, note2, note3, ...]
        """
        if not drum_rack.can_have_drum_pads:
            return

        # Collecte uniquement les notes utilisées avec vérification
        used_notes = []
        for pad in drum_rack.drum_pads:
            if pad.chains:  # Si le pad a des chaînes associées
                if isinstance(pad.note, (int, float)):  # Vérifie que c'est bien un nombre
                    used_notes.append(int(pad.note))
                else:
                    self.logger.warning(f"Note invalide pour le pad: {pad.note}")
        
        # Trie les notes pour une meilleure lisibilité
        used_notes.sort()
        
        # Envoie uniquement la liste des notes utilisées
        self.osc_server.send("/live/drumrack/get/grid", 
                            (len(used_notes), *used_notes))

def _build_path_string(self, path_indices):
        """
        Construit une représentation string du chemin de navigation
        Format: "DeviceRack > Chain > DeviceRack > Chain > Device"
        """
        try:
            if not path_indices:
                return ""

            selected_track = self.song.view.selected_track
            path_elements = []
            current_container = selected_track.devices

            # Parcours du chemin par paires (rack_index, chain_index)
            for i in range(0, len(path_indices) - 1, 2):
                rack_index = path_indices[i]
                chain_index = path_indices[i + 1]

                if rack_index >= len(current_container):
                    break

                # Ajoute le nom du rack
                rack = current_container[rack_index]
                path_elements.append(rack.name)

                if chain_index >= len(rack.chains):
                    break

                # Ajoute le nom de la chaîne
                chain = rack.chains[chain_index]
                path_elements.append(chain.name)
                current_container = chain.devices

            # Ajoute le dernier device si présent
            if len(path_indices) % 2 == 1:
                last_index = path_indices[-1]
                if last_index < len(current_container):
                    path_elements.append(current_container[last_index].name)

            # Construction de la chaîne finale avec " > " comme séparateur
            return " > ".join(path_elements)

        except Exception as e:
            self.logger.error(f"Erreur dans _build_path_string: {str(e)}")
            return ""
