from typing import Dict, Any, Callable
import logging
import traceback
import json
import unicodedata
from ..utils.parameter_throttler import ParameterThrottler


def _setup_chain_listeners(self, chain, path_indices):
        """
        Configure les listeners pour une chaîne et met en place la surveillance du rack parent
        """
        # Arrête d'abord les listeners existants
        self._stop_chain_listeners()
        self.stop_selected_device_listeners()
        
        # Initialiser le throttler s'il n'existe pas déjà
        if not hasattr(self, 'parameter_throttler'):
            self.parameter_throttler = ParameterThrottler()
        
        selected_track = self.song.view.selected_track
        
        # Lancer la configuration récursive avec le contexte de la chaîne actuelle
        self._setup_rack_listeners_recursive(selected_track.devices, None)

        # Envoi initial des paramètres en bulk
        path_list = list(path_indices)
        values_list = [
            chain.mixer_device.volume.value,
            chain.mixer_device.panning.value,
            int(chain.mute),
            int(chain.solo)
        ]
        
        # Envoi avec deux listes distinctes : path et valeurs
        self.osc_server.send("/live/chain/get/parameters/bulk",
                            (path_list, values_list))
        
        # Configuration des listeners pour le volume
        def volume_callback():
            # Créer un identifiant unique pour le volume de cette chaîne
            param_id = f"chain_volume_{id(chain)}"
            
            def send_volume_update(val):
                self.osc_server.send("/live/chain/get/volume", 
                                   (*path_indices, val))
                                   
            self.parameter_throttler.update_parameter(param_id, chain.mixer_device.volume.value, send_volume_update)
            
        self.chain_listeners[f"chain_volume"] = (chain.mixer_device.volume, volume_callback)
        chain.mixer_device.volume.add_value_listener(volume_callback)
        
        # Configuration des listeners pour le pan
        def pan_callback():
            # Créer un identifiant unique pour le pan de cette chaîne
            param_id = f"chain_pan_{id(chain)}"
            
            def send_pan_update(val):
                self.osc_server.send("/live/chain/get/pan", 
                                   (*path_indices, val))
                                   
            self.parameter_throttler.update_parameter(param_id, chain.mixer_device.panning.value, send_pan_update)
            
        self.chain_listeners[f"chain_pan"] = (chain.mixer_device.panning, pan_callback)
        chain.mixer_device.panning.add_value_listener(pan_callback)
        
        # Configuration des listeners pour mute
        def mute_callback():
            self.osc_server.send("/live/chain/get/mute", 
                                (*path_indices, int(chain.mute)))
        self.chain_listeners[f"chain_mute"] = (chain, mute_callback)
        chain.add_mute_listener(mute_callback)
        
        # Configuration des listeners pour solo
        def solo_callback():
            self.osc_server.send("/live/chain/get/solo", 
                                (*path_indices, int(chain.solo)))
        self.chain_listeners[f"chain_solo"] = (chain, solo_callback)
        chain.add_solo_listener(solo_callback)
        
        # Configuration du listener pour le nom de la chaîne
        def name_callback():
            self.send_chain_environment(chain)
        self.chain_listeners[f"chain_name"] = (chain, name_callback)
        chain.add_name_listener(name_callback)

        # Stocke l'état actuel
        parent_rack = chain.canonical_parent
        self.current_rack = parent_rack
        self.last_environment_data = self._get_current_environment_data(parent_rack)

        # NOUVEAU: Ajouter un listener pour détecter la sélection d'une chaîne dans le rack parent
        if hasattr(parent_rack, 'view') and hasattr(parent_rack.view, 'selected_chain') and hasattr(parent_rack.view, 'add_selected_chain_listener'):
            def selected_chain_callback():
                selected_chain = parent_rack.view.selected_chain
                if selected_chain and selected_chain != chain:
                    self.logger.info(f"Chaîne sélectionnée changée vers: {selected_chain.name}")
                    # Reconfigurer les listeners pour la nouvelle chaîne sélectionnée
                    new_path_indices = self._get_chain_path_indices(selected_chain)
                    if new_path_indices:
                        self._setup_chain_listeners(selected_chain, new_path_indices)
            
            self.chain_listeners["selected_chain_changed"] = (parent_rack.view, selected_chain_callback)
            parent_rack.view.add_selected_chain_listener(selected_chain_callback)
            self.logger.info(f"Listener ajouté pour détecter la sélection de chaîne dans le rack {parent_rack.name}")

        # NOUVEAU: Ajouter des listeners pour les chaînes sœurs
        if hasattr(parent_rack, 'chains'):
            for sister_chain in parent_rack.chains:
                if sister_chain != chain:
                    # Créer une fonction de callback pour détecter les changements dans les chaînes sœurs
                    def create_sister_chain_callback(sister_chain):
                        def callback():
                            self.logger.info(f"Changement détecté dans la chaîne sœur: {sister_chain.name}")
                            # Si c'est une activation/désactivation de solo, il faut peut-être reconfigurer
                            if sister_chain.solo:
                                self.logger.info(f"Chaîne sœur {sister_chain.name} mise en solo")
                            # Mise à jour de l'environnement pour refléter les changements
                            self.send_chain_environment(chain)
                        return callback
                    
                    sister_callback = create_sister_chain_callback(sister_chain)
                    # Utiliser des clés uniques pour les listeners des chaînes sœurs
                    solo_key = f"sister_chain_{id(sister_chain)}_solo"
                    mute_key = f"sister_chain_{id(sister_chain)}_mute"
                    
                    self.chain_listeners[solo_key] = (sister_chain, sister_callback)
                    self.chain_listeners[mute_key] = (sister_chain, sister_callback)
                    
                    sister_chain.add_solo_listener(sister_callback)
                    sister_chain.add_mute_listener(sister_callback)
                    self.logger.info(f"Listeners ajoutés pour la chaîne sœur: {sister_chain.name}")

        # Vérifie si c'est un DrumRack
        is_drum_rack = parent_rack.can_have_drum_pads
        if is_drum_rack:
            # Envoie la grille complète des pads utilisés
            self.send_drum_grid_state(parent_rack, path_indices[:-1])

        # Configuration des listeners pour les paramètres 0 de tous les devices de la chaîne
        for device_index, device in enumerate(chain.devices):
            if hasattr(device, 'parameters') and device.parameters:
                try:
                    param_0 = device.parameters[0]
                    
                    def create_device_param_callback(device, chain):
                        def callback():
                            # Vérifier si l'appareil existe toujours
                            if device and hasattr(device, 'name') and chain and hasattr(chain, 'name'):
                                self.logger.info(f"Changement détecté sur le paramètre 0 du device: {device.name} dans la chaîne: {chain.name}")
                                self.send_chain_environment(chain)
                        return callback
                    
                    device_callback = create_device_param_callback(device, chain)
                    # Utiliser une clé unique pour chaque device
                    listener_key = f"chain_device_{id(device)}_param_0"
                    
                    # Vérifier si on a déjà un listener pour ce paramètre
                    if listener_key in self.chain_listeners:
                        # Supprimer l'ancien listener s'il existe
                        old_obj, old_callback = self.chain_listeners[listener_key]
                        if old_obj and hasattr(old_obj, 'value_has_listener') and old_obj.value_has_listener(old_callback):
                            old_obj.remove_value_listener(old_callback)
                    
                    # Ajouter le nouveau listener
                    self.chain_listeners[listener_key] = (param_0, device_callback)
                    param_0.add_value_listener(device_callback)
                    self.logger.info(f"Listener ajouté pour le paramètre 0 du device {device.name} (ID: {id(device)}) dans la chaîne {chain.name}")
                except Exception as e:
                    self.logger.error(f"Erreur lors de l'ajout du listener pour le device {device.name} dans la chaîne: {str(e)}")
                    import traceback
                    self.logger.error(traceback.format_exc())

        self.send_chain_environment(chain)
        
def _stop_chain_listeners(self):
        """Arrête les listeners spécifiques aux chaînes"""
        if not self.chain_listeners:
            return

        # Réinitialiser le chemin stocké
        self._current_chain_path = None
        
        for key, (obj, callback) in list(self.chain_listeners.items()):
            try:
                if obj:
                    if key == "selected_chain_changed" and hasattr(obj, 'selected_chain_has_listener'):
                        if obj.selected_chain_has_listener(callback):
                            obj.remove_selected_chain_listener(callback)
                    elif key.startswith("selected_chain_") and hasattr(obj, 'selected_chain_has_listener'):
                        if obj.selected_chain_has_listener(callback):
                            obj.remove_selected_chain_listener(callback)
                    elif key == "chain_volume" and hasattr(obj, 'value_has_listener'):
                        if obj.value_has_listener(callback):
                            obj.remove_value_listener(callback)
                    elif key == "chain_pan" and hasattr(obj, 'value_has_listener'):
                        if obj.value_has_listener(callback):
                            obj.remove_value_listener(callback)
                    elif key == "chain_mute" and hasattr(obj, 'mute_has_listener'):
                        if obj.mute_has_listener(callback):
                            obj.remove_mute_listener(callback)
                    elif key == "chain_solo" and hasattr(obj, 'solo_has_listener'):
                        if obj.solo_has_listener(callback):
                            obj.remove_solo_listener(callback)
                    elif key == "chain_name" and hasattr(obj, 'name_has_listener'):
                        if obj.name_has_listener(callback):
                            obj.remove_name_listener(callback)
                    elif key.startswith("chain_device_") and key.endswith("_param_0") and hasattr(obj, 'value_has_listener'):
                        if obj.value_has_listener(callback):
                            obj.remove_value_listener(callback)
                            self.logger.info(f"Listener supprimé pour {key}")
                    # Ajout des cas pour les chaînes sœurs
                    elif key.startswith("sister_chain_") and key.endswith("_solo") and hasattr(obj, 'solo_has_listener'):
                        if obj.solo_has_listener(callback):
                            obj.remove_solo_listener(callback)
                            self.logger.info(f"Listener solo supprimé pour chaîne sœur {key}")
                    elif key.startswith("sister_chain_") and key.endswith("_mute") and hasattr(obj, 'mute_has_listener'):
                        if obj.mute_has_listener(callback):
                            obj.remove_mute_listener(callback)
                            self.logger.info(f"Listener mute supprimé pour chaîne sœur {key}")
            except Exception as e:
                self.logger.error(f"Erreur lors de la suppression du listener pour {key}: {str(e)}")
        
        # Ajout de logging pour déboguer
        self.logger.info(f"Nettoyage de {len(self.chain_listeners)} chain listeners")
        self.chain_listeners.clear()
        self.last_environment_data = None
        self.current_rack = None
        
def _get_current_environment_data(self, rack):
        """
        Retourne un dictionnaire contenant l'état actuel du rack
        """
        is_drum_rack = rack.can_have_drum_pads
        
        # Si c'est un DrumRack, envoie immédiatement l'état de la grille
        if is_drum_rack:
            self.send_drum_grid_state(rack, self._get_chain_path_indices(rack.chains[0])[:-1])
        
        chains_data = []
        for chain in rack.chains:
            chain_data = {
                "name": chain.name,
                "muted": chain.mute,
                "soloed": chain.solo,
                "devices": [device.name for device in chain.devices]
            }
            
            # Ajoute les informations de note MIDI pour les DrumChains
            if is_drum_rack and hasattr(chain, 'out_note'):
                chain_data["note"] = chain.out_note
            
            chains_data.append(chain_data)
        
        return {
            "chains_count": len(rack.chains),
            "is_drum_rack": is_drum_rack,
            "chains": chains_data
        }