"""
Handlers pour les encoders du mode device
Gère les ajustements des paramètres de device et de chain via les encoders
"""

import json
import traceback


def adjust_device_parameter(self, params):
    """
    Ajuste un paramètre de device en appliquant un delta à la valeur actuelle
    Détecte automatiquement si le paramètre est quantifié et applique la logique appropriée
    params: [parameter_index, delta_value]
    """
    try:
        if not params or len(params) != 2:
            self.logger.warning(f"Format de paramètres incorrect pour adjust_device_parameter: {params}")
            return
            
        parameter_index = int(params[0])
        delta_value = float(params[1])
        
        self.logger.debug(f"adjust_device_parameter: param_index={parameter_index}, delta={delta_value}")
        
        # Vérifier si on est en mode lock device
        if self.manager.deviceLock and self.manager.lockedDevice:
            device = self.manager.lockedDevice
            self.logger.debug(f"Mode lock device actif, utilisation du device verrouillé: {device.name}")
        else:
            # Utiliser le device sélectionné
            selected_track = self.song.view.selected_track
            if not selected_track or not selected_track.view.selected_device:
                self.logger.warning("Pas de device sélectionné")
                return
            device = selected_track.view.selected_device
            self.logger.debug(f"Utilisation du device sélectionné: {device.name}")
        
        # Vérifier que l'index du paramètre est valide
        if not hasattr(device, 'parameters') or parameter_index >= len(device.parameters):
            self.logger.warning(f"Index de paramètre invalide: {parameter_index} (max: {len(device.parameters)-1 if hasattr(device, 'parameters') else 'N/A'})")
            return
            
        parameter = device.parameters[parameter_index]
        current_value = parameter.value
        
        # Détecter si le device est un rack
        is_rack = _is_device_rack(self, device)
        
        # Détecter si le paramètre est quantifié
        is_quantized = _is_parameter_quantized(self, parameter)
        
        if is_quantized:
            # Pour les paramètres quantifiés, utiliser un système d'accumulation
            self.logger.debug(f"Paramètre {parameter_index} détecté comme quantifié (rack: {is_rack})")
            
            # Créer un ID unique pour ce paramètre
            param_id = f"{device.name}_{parameter_index}"
            
            # Récupérer le buffer actuel pour ce paramètre
            current_buffer = self.quantized_param_buffers.get(param_id, 0.0)
            
            # Ajouter le delta au buffer
            current_buffer += delta_value
            
            # Seuil pour déclencher un changement (plus élevé pour voir les valeurs intermédiaires)
            threshold = 0.5  # 100x plus élevé que le seuil normal (0.005) - optimal selon tests
            
            if abs(current_buffer) >= threshold:
                # Calculer la direction du changement
                direction = 1 if current_buffer > 0 else -1
                
                # Calculer la nouvelle valeur potentielle
                potential_new_value = current_value + direction
                
                # Vérifier si on peut faire le changement (pas aux extrémités)
                if potential_new_value > parameter.max:
                    # On est déjà au maximum, ne pas changer et vider le buffer
                    new_value = parameter.max
                    self.quantized_param_buffers[param_id] = 0.0
                    self.logger.debug(f"Paramètre quantifié {parameter_index}: déjà au maximum ({parameter.max}), buffer vidé")
                    return  # Ne pas appliquer de changement
                elif potential_new_value < parameter.min:
                    # On est déjà au minimum, ne pas changer et vider le buffer
                    new_value = parameter.min
                    self.quantized_param_buffers[param_id] = 0.0
                    self.logger.debug(f"Paramètre quantifié {parameter_index}: déjà au minimum ({parameter.min}), buffer vidé")
                    return  # Ne pas appliquer de changement
                else:
                    # On peut faire le changement
                    new_value = potential_new_value
                    # Réduire le buffer du montant utilisé
                    self.quantized_param_buffers[param_id] = current_buffer - (direction * threshold)
                    self.logger.debug(f"Paramètre quantifié {parameter_index}: {current_value} -> {new_value}, buffer réduit")
            else:
                # Pas assez d'accumulation, garder la valeur actuelle et stocker le buffer
                new_value = current_value
                self.quantized_param_buffers[param_id] = current_buffer
                self.logger.debug(f"Paramètre quantifié {parameter_index}: buffer={current_buffer:.3f} (en dessous du seuil)")
                return  # Ne pas appliquer de changement
        else:
            # Pour les paramètres continus, ajuster le scaling selon le type de device
            self.logger.debug(f"Paramètre {parameter_index} détecté comme continu (rack: {is_rack})")
            
            if is_rack:
                # Pour les racks, les paramètres vont de 0 à 127
                # Un tour complet doit parcourir toute la plage, comme pour 0-1
                # Si 0-1 utilise /5.0, alors 0-127 doit utiliser /5.0 * 127 = *25.4
                scaled_delta = delta_value * 25.4  # Scaling pour que 1 tour = plage complète
            else:
                # Pour les devices normaux, paramètres 0-1, scaling comme pour le volume
                scaled_delta = delta_value / 5.0
            
            new_value = current_value + scaled_delta
            new_value = max(parameter.min, min(parameter.max, new_value))
        
        # Appliquer la nouvelle valeur
        parameter.value = new_value
        
        self.logger.debug(f"Paramètre {parameter_index} ajusté: {current_value} -> {new_value} (delta: {delta_value}, quantifié: {is_quantized}, rack: {is_rack}, plage: {parameter.min}-{parameter.max})")
        
    except Exception as e:
        self.logger.error(f"Erreur dans adjust_device_parameter: {str(e)}")
        import traceback
        self.logger.error(traceback.format_exc())


def _is_device_rack(self, device):
    """
    Détermine si un device est un rack
    """
    try:
        # Vérifier si le device peut avoir des chaînes
        if hasattr(device, 'can_have_chains') and device.can_have_chains:
            return True
        
        # Vérifier si le device peut avoir des drum pads (DrumRack)
        if hasattr(device, 'can_have_drum_pads') and device.can_have_drum_pads:
            return True
            
        return False
        
    except Exception as e:
        self.logger.error(f"Erreur lors de la détection du type de device: {str(e)}")
        return False


def _is_parameter_quantized(self, parameter):
    """
    Détermine si un paramètre est quantifié (discret) ou continu
    """
    try:
        # Vérifier si le paramètre a une propriété is_quantized
        if hasattr(parameter, 'is_quantized'):
            return parameter.is_quantized
        
        # Vérifier si le paramètre a des value_items (liste de valeurs discrètes)
        if hasattr(parameter, 'value_items') and parameter.value_items:
            return True
        
        # Heuristique basée sur la plage de valeurs
        value_range = parameter.max - parameter.min
        
        # Si la plage est très petite (0-1 ou similaire), c'est probablement quantifié
        if value_range <= 1.0:
            return True
        
        # Si la plage correspond à des valeurs MIDI typiques (0-127), c'est probablement quantifié
        if abs(value_range - 127.0) < 0.1:
            return True
        
        # Par défaut, considérer comme continu
        return False
        
    except Exception as e:
        self.logger.error(f"Erreur lors de la détection du type de paramètre: {str(e)}")
        return False


def adjust_chain_parameter(self, params):
    """
    Ajuste un paramètre de chain (volume, pan, mute, solo)
    params: [path_json, parameter_type, delta_or_direction]
    parameter_type: 0=volume, 1=pan, 2=mute, 3=solo
    """
    try:
        if not params or len(params) != 3:
            self.logger.warning(f"Format de paramètres incorrect pour adjust_chain_parameter: {params}")
            return

        path_json = params[0]
        parameter_type = int(params[1])
        value = float(params[2])

        self.logger.debug(f"adjust_chain_parameter: path={path_json}, type={parameter_type}, value={value}")

        # Trouver la chain à partir du chemin
        target_chain = self._find_chain_by_path(path_json)
        if not target_chain:
            self.logger.warning("Chain non trouvée pour le chemin spécifié")
            return

        if not hasattr(target_chain, 'mixer_device'):
            self.logger.warning("Chain sans mixer_device")
            return

        mixer = target_chain.mixer_device

        if parameter_type == 0:  # Volume
            if hasattr(mixer, 'volume'):
                current_value = mixer.volume.value
                # Appliquer le même scaling que pour le volume des tracks
                scaled_delta = value / 5.0
                new_value = current_value + scaled_delta
                new_value = max(mixer.volume.min, min(mixer.volume.max, new_value))
                mixer.volume.value = new_value
                self.logger.debug(f"Volume chain ajusté: {current_value} -> {new_value} (delta: {value}, scaled: {scaled_delta})")

        elif parameter_type == 1:  # Pan
            if hasattr(mixer, 'panning'):
                current_value = mixer.panning.value
                # Appliquer le même scaling que pour le pan des tracks
                scaled_delta = value / 5.0
                new_value = current_value + scaled_delta
                new_value = max(mixer.panning.min, min(mixer.panning.max, new_value))
                mixer.panning.value = new_value
                self.logger.debug(f"Pan chain ajusté: {current_value} -> {new_value} (delta: {value}, scaled: {scaled_delta})")

        elif parameter_type == 2:  # Mute (toggle)
            # Utiliser le même système de buffers que pour les paramètres quantifiés
            if hasattr(target_chain, 'mute'):
                # Créer un ID unique pour ce paramètre mute
                param_id = f"chain_mute_{path_json}"

                # Récupérer le buffer actuel pour ce paramètre
                current_buffer = self.quantized_param_buffers.get(param_id, 0.0)

                # Ajouter le delta au buffer
                current_buffer += value

                # Seuil pour déclencher un changement (même que pour les paramètres quantifiés)
                threshold = 0.5

                if abs(current_buffer) >= threshold:
                    # Calculer la direction du changement
                    direction = 1 if current_buffer > 0 else -1
                    current_mute = target_chain.mute

                    # Vérifier si on peut faire le changement selon la direction
                    if direction > 0 and current_mute:
                        # On veut activer le mute mais il est déjà activé, bloquer
                        self.quantized_param_buffers[param_id] = 0.0
                        self.logger.debug(f"Mute chain déjà activé, buffer vidé")
                        return
                    elif direction < 0 and not current_mute:
                        # On veut désactiver le mute mais il est déjà désactivé, bloquer
                        self.quantized_param_buffers[param_id] = 0.0
                        self.logger.debug(f"Mute chain déjà désactivé, buffer vidé")
                        return
                    else:
                        # On peut faire le changement
                        target_chain.mute = not current_mute
                        self.logger.debug(f"Mute chain togglé: {current_mute} -> {target_chain.mute}")
                        # Réduire le buffer du montant utilisé
                        self.quantized_param_buffers[param_id] = current_buffer - (direction * threshold)
                else:
                    # Stocker le buffer mis à jour
                    self.quantized_param_buffers[param_id] = current_buffer
                    self.logger.debug(f"Mute chain buffer: {current_buffer:.3f} (en dessous du seuil)")
            else:
                self.logger.warning("Pas de propriété mute trouvée sur la chain")

        elif parameter_type == 3:  # Solo (toggle)
            # Utiliser le même système de buffers que pour les paramètres quantifiés
            if hasattr(target_chain, 'solo'):
                # Créer un ID unique pour ce paramètre solo
                param_id = f"chain_solo_{path_json}"

                # Récupérer le buffer actuel pour ce paramètre
                current_buffer = self.quantized_param_buffers.get(param_id, 0.0)

                # Ajouter le delta au buffer
                current_buffer += value

                # Seuil pour déclencher un changement (même que pour les paramètres quantifiés)
                threshold = 0.5

                if abs(current_buffer) >= threshold:
                    # Calculer la direction du changement
                    direction = 1 if current_buffer > 0 else -1
                    current_solo = target_chain.solo

                    # Vérifier si on peut faire le changement selon la direction
                    if direction > 0 and current_solo:
                        # On veut activer le solo mais il est déjà activé, bloquer
                        self.quantized_param_buffers[param_id] = 0.0
                        self.logger.debug(f"Solo chain déjà activé, buffer vidé")
                        return
                    elif direction < 0 and not current_solo:
                        # On veut désactiver le solo mais il est déjà désactivé, bloquer
                        self.quantized_param_buffers[param_id] = 0.0
                        self.logger.debug(f"Solo chain déjà désactivé, buffer vidé")
                        return
                    else:
                        # On peut faire le changement
                        target_chain.solo = not current_solo
                        self.logger.debug(f"Solo chain togglé: {current_solo} -> {target_chain.solo}")
                        # Réduire le buffer du montant utilisé
                        self.quantized_param_buffers[param_id] = current_buffer - (direction * threshold)
                else:
                    # Stocker le buffer mis à jour
                    self.quantized_param_buffers[param_id] = current_buffer
                    self.logger.debug(f"Solo chain buffer: {current_buffer:.3f} (en dessous du seuil)")
            else:
                self.logger.warning("Pas de propriété solo trouvée sur la chain")

        else:
            self.logger.warning(f"Type de paramètre chain non supporté: {parameter_type}")

    except Exception as e:
        self.logger.error(f"Erreur dans adjust_chain_parameter: {str(e)}")
        import traceback
        self.logger.error(traceback.format_exc())
