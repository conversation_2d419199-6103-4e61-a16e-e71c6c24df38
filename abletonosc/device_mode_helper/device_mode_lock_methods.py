from typing import Dict, Any, Callable
import logging
import traceback
import json
import unicodedata



def start_locked_device_listeners(self, *args):
        # D'abord arrêter les listeners existants
        self.stop_locked_device_listeners()
        
        if not self.manager.deviceLock or not self.manager.lockedDevice:
            return
        
        locked_device = self.manager.lockedDevice
        locked_track = self._find_device_track(locked_device)
        
        # Utilisation directe de send_locked_device_reference comme callback
        self.deviceLockMode_listeners["device_name"] = (locked_device, self.send_locked_device_reference)
        self.deviceLockMode_listeners["track_name"] = (locked_track, self.send_locked_device_reference)
        self.deviceLockMode_listeners["track_color"] = (locked_track, self.send_locked_device_reference)
        
        locked_device.add_name_listener(self.send_locked_device_reference)
        locked_track.add_name_listener(self.send_locked_device_reference)
        locked_track.add_color_listener(self.send_locked_device_reference)
        
        self._setup_device_listeners(locked_device, "deviceLockMode", 
                                   self.deviceLockMode_listeners, track_id=-4)
        self.manager.lastLockedDevice = locked_device
        
        # Envoi initial des références
        self.send_locked_device_reference()

def stop_locked_device_listeners(self, *args):
        self._stop_device_listeners(self.deviceLockMode_listeners)
        self.manager.lastLockedDevice = None

       
def send_locked_device_reference(self):
        """Envoie le chemin du device verrouillé et les propriétés de sa piste"""
        if not self.manager.lockedDevice:
            return
        
        device = self.manager.lockedDevice
        track = self._find_device_track(device)
        
        # Construction du path_string pour le device verrouillé
        path_indices = []
        current_device = device
        current_chain = current_device.canonical_parent
        
        while hasattr(current_chain, 'canonical_parent'):
            parent_rack = current_chain.canonical_parent
            if hasattr(parent_rack, 'chains'):
                chain_index = list(parent_rack.chains).index(current_chain)
                rack_index = list(current_chain.devices).index(current_device)
                path_indices = [rack_index, chain_index] + path_indices
                current_device = parent_rack
                current_chain = current_device.canonical_parent
            else:
                break
        
        # Ajout de l'index final du device s'il est directement sur la piste
        if current_chain == track:
            device_index = list(track.devices).index(current_device)
            path_indices = [device_index] + path_indices
        
        path_string = self._build_path_string(path_indices)
        track_name = track.name if track else "Unknown Track"
        track_color = track.color if track else 0
        
        self.osc_server.send("/live/device_lock_reference", (path_string, track_name, track_color))

def _find_device_track(self, device):
        self.logger.info(f"Recherche de la piste pour le device: {device.name}")
        """Trouve la piste qui contient le device spécifié, y compris dans les racks"""
        def search_in_devices(devices):
            for d in devices:
                if d == device:
                    return True
                # Vérifier si c'est un rack (instrument, audio, ou midi)
                if hasattr(d, 'chains'):
                    for chain in d.chains:
                        # Recherche récursive dans les devices de la chaîne
                        if search_in_devices(chain.devices):
                            return True
            return False

        all_tracks = list(self.song.tracks) + list(self.song.return_tracks) + [self.song.master_track]
        for track in all_tracks:
            if search_in_devices(track.devices):
                return track
        return None

    
def deviceLock_handler(self, value):
        """
        Gère le verrouillage/déverrouillage d'un device
        """
        try:
            if value[0] == '1':
                self.manager.deviceLock = True
                selected_track = self.song.view.selected_track
                selected_device = selected_track.view.selected_device
                
                if selected_device:
                    self.manager.lockedDevice = selected_device
                    self.logger.info(f"Locked device found: {self.manager.lockedDevice.name}")
                    self.osc_server.send("/live/device_lock")
                    self.start_locked_device_listeners()
                else:
                    self.logger.info("No locked device found")
                    self.manager.deviceLock = False
                    self.osc_server.send("/live/device_unlock")
                    self.stop_locked_device_listeners()
                    self.manager.on_selected_track_or_device_changed()    
                
            else:
                self.manager.deviceLock = False
                self.stop_locked_device_listeners()                
                
                selected_track = self.song.view.selected_track
                if selected_track:
                    selected_device = selected_track.view.selected_device
                    track_name = selected_track.name
                    track_color = selected_track.color
                    
                    self.osc_server.send("/live/deviceMode/selTrack/properties", 
                                       (track_name, track_color))
                        
                  
                    self.manager.on_selected_track_or_device_changed()
                    
            
        except Exception as e:
            self.logger.error(f"Erreur dans deviceLock_handler: {str(e)}")
            self.manager.deviceLock = False
            self.osc_server.send("/live/device_unlock")

def verify_lock_device_validity(self):
        """
        Vérifie si le device verrouillé existe toujours dans Live
        et met à jour les états en conséquence
        """
        if not self.manager.lockedDevice:
            self.logger.info("Aucun périphérique verrouillé trouvé")
            self.manager.deviceLock = False
            self.osc_server.send("/live/device_unlock")
            return

        # Recherche du device dans toutes les pistes
        all_tracks = list(self.song.tracks) + list(self.song.return_tracks) + [self.song.master_track]
        device_found = False

        def search_device_in_chain(chain):
            for device in chain.devices:
                if device == self.manager.lockedDevice:
                    return True
                if hasattr(device, 'chains'):
                    for sub_chain in device.chains:
                        if search_device_in_chain(sub_chain):
                            return True
            return False

        # Recherche dans toutes les pistes
        for track in all_tracks:
            if search_device_in_chain(track):
                device_found = True
                self.logger.info(f"Le périphérique verrouillé est {self.manager.lockedDevice.name}")
                self.send_locked_device_reference()
                break

        if not device_found:
            self.manager.lockedDevice = None
            self.manager.deviceLock = False
            self.logger.info("Périphérique déverrouillé en raison de la suppression")
            self.osc_server.send("/live/device_unlock")