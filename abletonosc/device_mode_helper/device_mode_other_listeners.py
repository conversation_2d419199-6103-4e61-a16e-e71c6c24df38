from typing import Dict, Any, Callable
import logging
import traceback
import json
import unicodedata

    
def _create_chain_selection_callback(self, rack, original_chain):
        """
        Crée un callback pour la sélection de chaîne qui gère le chemin et les listeners
        """
        def selected_chain_changed():
            selected_chain = rack.view.selected_chain
            if selected_chain:
                # Calculer et stocker le nouveau chemin
                new_path = self._get_chain_path_indices(selected_chain)
                self._current_chain_path = new_path
                # Évite la récursion infinie en vérifiant le parent
                if selected_chain.canonical_parent != original_chain:
                    self._setup_chain_listeners(selected_chain, new_path)
        return selected_chain_changed

def _setup_rack_listeners_recursive(self, devices, parent_chain=None):
        """
        Configure récursivement les listeners pour tous les racks dans un conteneur
        """
        for device in devices:
            if hasattr(device, 'chains'):
                callback = self._create_chain_selection_callback(device, parent_chain)
                listener_key = f"selected_chain_{id(device)}"
                self.chain_listeners[listener_key] = (device.view, callback)
                
                if hasattr(device.view, 'add_selected_chain_listener'):
                    if not device.view.selected_chain_has_listener(callback):
                        device.view.add_selected_chain_listener(callback)
                
                # Parcourir récursivement les chaînes avec le contexte correct
                for sub_chain in device.chains:
                    self._setup_rack_listeners_recursive(sub_chain.devices, sub_chain)