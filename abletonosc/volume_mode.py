import logging
from .utils.parameter_throttler import ParameterThrottler

class VolumeMode:
    def __init__(self, track_handler):
        self.track_handler = track_handler
        self.logger = logging.getLogger("abletonosc")
        self.logger.setLevel(logging.DEBUG)  # Forcer le niveau de log à DEBUG
        self.volumeMode_listeners = {}
        self.tracked_tracks = set()
        self.parameter_throttler = ParameterThrottler()

    def start(self, _):
        """Initialise le volumeMode sur toutes les pistes"""
        self.logger.debug("=== Starting volumeMode with DEBUG level ===")

        visible_tracks = [t for t in self.track_handler.song.tracks if t.is_visible]
        return_tracks = list(self.track_handler.song.return_tracks)
        all_tracks = visible_tracks + return_tracks + [self.track_handler.song.master_track]

        self.logger.debug(f"\n=== Nombre total de pistes: {len(all_tracks)} ===")
        self.logger.debug(f"Pistes visibles: {len(visible_tracks)}")
        self.logger.debug(f"Pistes return: {len(return_tracks)}")

        self.logger.debug("\n=== Sending Initial Structure ===")

        # Pour chaque piste, envoyer ses informations individuellement
        for track in all_tracks:
            track_index = self._get_track_index(track)
            self.logger.debug(f"\n=== Configuration de la piste: {track.name} (index: {track_index}) ===")

            # Collecter les valeurs des sends pour cette piste
            sends_values = []
            if track != self.track_handler.song.master_track:
                try:
                    sends = track.mixer_device.sends
                    self.logger.debug(f"Nombre de sends trouvés: {len(sends)}")
                    sends_values = [send.value for send in sends]
                    self.logger.debug(f"Valeurs des sends: {sends_values}")
                except Exception as e:
                    self.logger.error(f"Erreur lors de la récupération des sends pour {track.name}: {str(e)}")

            # Déterminer si c'est une piste normale pour arm
            is_normal_track = track not in return_tracks and track != self.track_handler.song.master_track and not track.is_foldable

            # Définir l'état d'armement :
            # 0 = non armé, 1 = armé, 2 = non applicable (return/master/group)
            arm_value = 2  # Par défaut, non applicable
            if is_normal_track:
                try:
                    arm_value = 1 if track.arm else 0
                except Exception as e:
                    self.logger.debug(f"Could not get arm state for track {track.name}: {str(e)}")
                    arm_value = 2

            # Calculer le mute_state
            normal_tracks_soloed = any(t.solo for t in visible_tracks)
            return_tracks_soloed = any(t.solo for t in return_tracks)

            # Déterminer la valeur de muted_via_solo
            muted_via_solo = False
            if track != self.track_handler.song.master_track:
                if (track in return_tracks and return_tracks_soloed) or (track not in return_tracks and normal_tracks_soloed):
                    muted_via_solo = track.muted_via_solo

            # Obtenir les valeurs actuelles
            solo = track.solo if track != self.track_handler.song.master_track else False
            mute = track.mute if track != self.track_handler.song.master_track else False

            # Calculer le code d'état
            mute_state = self._get_mute_state_code(track, solo, mute, muted_via_solo)

            # Envoyer les données de base de la piste et les valeurs des sends
            message_data = (
                track_index,  # index
                "master" if track == self.track_handler.song.master_track else "return" if track in return_tracks else "track",  # type
                track.name,  # name
                track.is_foldable,  # isFoldable
                track.fold_state if track.is_foldable else False,  # foldState
                track.is_grouped,  # isGrouped
                mute_state,  # mute_state (remplace mute, solo et muted_via_solo)
                arm_value,  # isArm
                track.color,  # color
                track.mixer_device.volume.value,  # volume
                track.mixer_device.panning.value,  # pan
                sends_values  # liste des valeurs de sends
            )

            self.logger.debug(f"=== Track Data for {track.name} ===")
            self.logger.debug(f"Index: {track_index}")
            self.logger.debug(f"Type: {'master' if track == self.track_handler.song.master_track else 'return' if track in return_tracks else 'track'}")
            self.logger.debug(f"Mute State: {mute_state}")
            self.logger.debug(f"Arm value: {arm_value}")
            self.logger.debug(f"Message length: {len(message_data)}")
            self.logger.debug(f"Full message: {message_data}")

            self.track_handler.osc_server.send("/live/volumeMode/structure/track", message_data)
            self.logger.info(f"Track {track_index}: {track.name}")

        # Envoyer un message de fin de structure
        self.track_handler.osc_server.send("/live/volumeMode/structure/end", (len(all_tracks),))
        self.logger.info("=== End Initial Structure ===\n")

        # Configurer les listeners pour chaque piste
        for track in all_tracks:
            try:
                self.logger.debug(f"\n=== Configuration des listeners pour la piste: {track.name} ===")
                self._setup_track_listeners(track)
            except Exception as e:
                self.logger.error(f"Erreur lors de la configuration des listeners pour {track.name}: {str(e)}")

    def stop(self, _):
        """Arrête le volumeMode et nettoie les listeners"""
        self.logger.info("Stopping volumeMode")
        self._remove_all_listeners()
        if self.track_handler.song.tracks_has_listener(self._update_listeners):
            self.track_handler.song.remove_tracks_listener(self._update_listeners)

    def _setup_track_listeners(self, track):
        """Configure les listeners pour une piste spécifique"""
        if track in self.tracked_tracks:
            self.logger.debug(f"La piste {track.name} est déjà suivie, ignorée")
            return

        self.logger.debug(f"\n=== Début configuration des listeners pour {track.name} ===")
        self.tracked_tracks.add(track)
        self.volumeMode_listeners[track] = {}

        # Propriétés communes à toutes les pistes (y compris master)
        common_properties = {
            "volume": lambda t: t.mixer_device.volume.value,
            "pan": lambda t: t.mixer_device.panning.value,
            "color": lambda t: t.color,
            "name": lambda t: t.name
        }

        # Ajouter arm uniquement pour les pistes normales (pas return, master, ni groupe)
        if track not in self.track_handler.song.return_tracks and track != self.track_handler.song.master_track and not track.is_foldable:
            common_properties["arm"] = lambda t: t.arm

        def create_property_callback(property_name, value_getter):
            def callback():
                try:
                    value = value_getter(track)
                    track_index = self._get_track_index(track)
                    if track_index is not None:
                        if not getattr(self, '_updating_structure', False):
                            param_id = f"volumeMode_{track.name}_{property_name}"

                            # Traitement spécial pour la propriété arm
                            if property_name == "arm":
                                # Pour les pistes return ou master, envoyer 2
                                if track in self.track_handler.song.return_tracks or track == self.track_handler.song.master_track or track.is_foldable:
                                    arm_value = 2
                                else:
                                    # Pour les autres pistes, envoyer 0 ou 1 en fonction de l'état
                                    arm_value = 1 if value else 0

                                def send_value(val):
                                    self.logger.debug(f"Envoi valeur pour {property_name}: {track_index}, {arm_value}")
                                    self.track_handler.osc_server.send(f"/live/volumeMode/get/{property_name}", (track_index, arm_value))

                                self.parameter_throttler.update_parameter(param_id, value, send_value)
                            else:
                                # Comportement normal pour les autres propriétés
                                def send_value(val):
                                    self.logger.debug(f"Envoi valeur pour {property_name}: {track_index}, {val}")
                                    self.track_handler.osc_server.send(f"/live/volumeMode/get/{property_name}", (track_index, val))

                                self.parameter_throttler.update_parameter(param_id, value, send_value)
                except Exception as e:
                    self.logger.error(f"Error in {property_name} callback: {str(e)}")
            return callback

        # Configurer les listeners pour les propriétés communes
        for prop, getter in common_properties.items():
            try:
                self.logger.debug(f"Configuration du listener pour {prop}")
                callback = create_property_callback(prop, getter)
                if prop == "volume":
                    track.mixer_device.volume.add_value_listener(callback)
                elif prop == "pan":
                    track.mixer_device.panning.add_value_listener(callback)
                else:
                    getattr(track, f"add_{prop}_listener")(callback)
                self.volumeMode_listeners[track][prop] = callback
                self.logger.debug(f"Listener pour {prop} configuré avec succès")
            except Exception as e:
                self.logger.error(f"Erreur lors de la configuration du listener {prop}: {str(e)}")

        # Ajouter le listener pour mute_state si ce n'est pas la piste master
        if track != self.track_handler.song.master_track:
            try:
                self.logger.debug("Configuration du listener pour mute_state")
                def create_mute_state_callback():
                    def callback():
                        try:
                            track_index = self._get_track_index(track)
                            if track_index is not None and not getattr(self, '_updating_structure', False):
                                self._update_mute_state(track, track_index)
                        except Exception as e:
                            self.logger.error(f"Error in mute state callback: {str(e)}")
                    return callback

                mute_state_callback = create_mute_state_callback()
                track.add_mute_listener(mute_state_callback)
                track.add_solo_listener(mute_state_callback)
                track.add_muted_via_solo_listener(mute_state_callback)

                self.volumeMode_listeners[track]["mute_state"] = mute_state_callback
                self.logger.debug("Listener mute_state configuré avec succès")
            except Exception as e:
                self.logger.error(f"Erreur lors de la configuration du listener mute_state: {str(e)}")

        # Configurer les listeners pour les sends si ce n'est pas la piste master
        if track != self.track_handler.song.master_track:
            try:
                self.logger.debug("Tentative de configuration des sends")
                self._setup_send_listeners(track)
                self.logger.debug("Configuration des sends terminée avec succès")
            except Exception as e:
                self.logger.error(f"Erreur lors de la configuration des sends: {str(e)}")

        self.logger.debug(f"=== Fin configuration des listeners pour {track.name} ===\n")

    def _setup_send_listeners(self, track):
        """Configure les listeners pour les sends d'une piste"""
        # Ne pas configurer les sends pour la master
        if track == self.track_handler.song.master_track:
            return

        # Récupérer tous les sends disponibles sur la piste
        all_sends = list(track.mixer_device.sends)
        self.logger.debug(f"=== Configuration des listeners pour {len(all_sends)} sends sur la piste {track.name} ===")

        # Pour chaque send, configurer un listener
        for send_index in range(len(all_sends)):
            send = all_sends[send_index]
            self.logger.debug(f"Configuration du send {send_index} avec valeur initiale: {send.value}")

            # IMPORTANT: Utiliser des valeurs par défaut pour capturer correctement les variables
            def callback(t=track, idx=send_index, s=send):
                try:
                    track_index = self._get_track_index(t)
                    self.logger.debug(f"=== Callback send déclenché pour piste {t.name}, send {idx} ===")
                    self.logger.debug(f"Track index: {track_index}, Send value: {s.value}")

                    if track_index is not None and not getattr(self, '_updating_structure', False):
                        # Créer un identifiant unique pour ce send
                        param_id = f"volumeMode_{t.name}_send_{idx}"

                        def send_value(val):
                            # Envoyer uniquement l'index de la piste, l'index du send et sa valeur
                            self.logger.debug(f"Envoi message OSC send: track={track_index}, send={idx}, value={val}")
                            self.track_handler.osc_server.send("/live/volumeMode/get/send", (track_index, idx, val))

                        self.parameter_throttler.update_parameter(param_id, s.value, send_value)
                    else:
                        self.logger.debug(f"Message non envoyé - track_index: {track_index}, updating_structure: {getattr(self, '_updating_structure', False)}")
                except Exception as e:
                    self.logger.error(f"Error in send callback for track {t.name}, send {idx}: {str(e)}")

            # Ajouter le listener et stocker le callback
            send.add_value_listener(callback)
            send_key = f"send_{send_index}"
            self.volumeMode_listeners[track][send_key] = (send, callback)

            self.logger.debug(f"Listener configuré pour send {send_index} sur piste {track.name}")

    def _remove_track_listeners(self, track):
        """Supprime les listeners pour une piste spécifique"""
        if track not in self.volumeMode_listeners:
            return

        try:
            for prop, callback in self.volumeMode_listeners[track].items():
                try:
                    if track in self.track_handler.song.tracks or track in self.track_handler.song.return_tracks or track == self.track_handler.song.master_track:
                        if prop == "mute_state":
                            track.remove_mute_listener(callback)
                            track.remove_solo_listener(callback)
                            track.remove_muted_via_solo_listener(callback)
                        # Traitement spécial pour les sends
                        if prop.startswith("send_"):
                            send, callback_func = callback
                            send.remove_value_listener(callback_func)
                        elif prop == "volume":
                            track.mixer_device.volume.remove_value_listener(callback)
                        elif prop == "pan":
                            track.mixer_device.panning.remove_value_listener(callback)
                        else:
                            getattr(track, f"remove_{prop}_listener")(callback)
                except Exception as e:
                    self.logger.debug(f"Error removing {prop} listener: {str(e)}")
        finally:
            # Toujours nettoyer les références
            if track in self.volumeMode_listeners:
                del self.volumeMode_listeners[track]
            if track in self.tracked_tracks:
                self.tracked_tracks.remove(track)

    def _get_track_index(self, track):
        """Obtient l'index actuel d'une piste"""
        visible_tracks = [t for t in self.track_handler.song.tracks if t.is_visible]
        return_tracks = list(self.track_handler.song.return_tracks)

        if track in visible_tracks:
            return visible_tracks.index(track)
        elif track in return_tracks:
            return len(visible_tracks) + return_tracks.index(track)
        elif track == self.track_handler.song.master_track:
            return len(visible_tracks) + len(return_tracks)
        return None

    def _get_mute_state_code(self, track, solo, mute, muted_via_solo):
        """Calcule le code d'état de mute en fonction des paramètres"""
        # Si c'est la master track, retourner toujours 9
        if track == self.track_handler.song.master_track:
            return 9

        if not solo:
            if not mute:
                if not muted_via_solo:
                    return 0  # solo false, mute false, muted_via_solo false
                return 1  # solo false, mute false, muted_via_solo true
            else:
                if muted_via_solo:
                    return 2  # solo false, mute true, muted_via_solo true
                return 3  # solo false, mute true, muted_via_solo false
        else:
            if not mute:
                if not muted_via_solo:
                    return 4  # solo true, mute false, muted_via_solo false
                return 5  # solo true, mute false, muted_via_solo true
            else:
                if muted_via_solo:
                    return 6  # solo true, mute true, muted_via_solo true
                return 7  # solo true, mute true, muted_via_solo false

    def _update_mute_state(self, track, track_index):
        """Met à jour l'état de mute et envoie le code approprié"""
        # Si c'est la master track, envoyer directement 9
        if track == self.track_handler.song.master_track:
            self.track_handler.osc_server.send("/live/volumeMode/get/mute_state", (track_index, 9))
            return

        # Vérifier si c'est une piste return
        is_return_track = track in self.track_handler.song.return_tracks

        # Vérifier si des pistes sont en solo
        normal_tracks_soloed = any(t.solo for t in self.track_handler.song.tracks if t.is_visible)
        return_tracks_soloed = any(t.solo for t in self.track_handler.song.return_tracks)

        # Déterminer la valeur de muted_via_solo
        muted_via_solo = False
        if track != self.track_handler.song.master_track:
            if (is_return_track and return_tracks_soloed) or (not is_return_track and normal_tracks_soloed):
                muted_via_solo = track.muted_via_solo

        # Obtenir les valeurs actuelles
        solo = track.solo if track != self.track_handler.song.master_track else False
        mute = track.mute if track != self.track_handler.song.master_track else False

        # Calculer le code d'état
        state_code = self._get_mute_state_code(track, solo, mute, muted_via_solo)

        # Envoyer le code d'état
        self.track_handler.osc_server.send("/live/volumeMode/get/mute_state", (track_index, state_code))

    def _update_listeners(self, *args):
        """Met à jour les listeners et notifie des changements de structure"""
        try:
            self.logger.debug("\n=== Début _update_listeners ===")
            self._updating_structure = True

            visible_tracks = [t for t in self.track_handler.song.tracks if t.is_visible]
            return_tracks = list(self.track_handler.song.return_tracks)
            all_tracks = visible_tracks + return_tracks + [self.track_handler.song.master_track]

            self.logger.debug(f"Nombre de pistes visibles: {len(visible_tracks)}")
            self.logger.debug(f"Nombre de pistes return: {len(return_tracks)}")
            self.logger.debug(f"Nombre total de pistes: {len(all_tracks)}")

            self.track_handler.osc_server.send("/live/volumeMode/structure/begin", (len(all_tracks),))

            for track in all_tracks:
                track_index = self._get_track_index(track)
                if track_index is not None:
                    self.logger.debug(f"\n=== Traitement de la piste: {track.name} (index: {track_index}) ===")
                    sends_values = []
                    if track != self.track_handler.song.master_track:
                        try:
                            sends = track.mixer_device.sends
                            self.logger.debug(f"Nombre de sends trouvés: {len(sends)}")
                            sends_values = [send.value for send in sends]
                            self.logger.debug(f"Valeurs des sends: {sends_values}")
                        except Exception as e:
                            self.logger.error(f"Erreur lors de la récupération des sends pour {track.name}: {str(e)}")

                    is_normal_track = track not in return_tracks and track != self.track_handler.song.master_track and not track.is_foldable

                    # Définir l'état d'armement
                    arm_value = 2  # Par défaut, non applicable
                    if is_normal_track:
                        try:
                            arm_value = 1 if track.arm else 0
                        except Exception as e:
                            self.logger.debug(f"Could not get arm state for track {track.name}: {str(e)}")
                            arm_value = 2

                    # Calculer le mute_state
                    normal_tracks_soloed = any(t.solo for t in visible_tracks)
                    return_tracks_soloed = any(t.solo for t in return_tracks)

                    muted_via_solo = False
                    if track != self.track_handler.song.master_track:
                        if (track in return_tracks and return_tracks_soloed) or (track not in return_tracks and normal_tracks_soloed):
                            muted_via_solo = track.muted_via_solo

                    solo = track.solo if track != self.track_handler.song.master_track else False
                    mute = track.mute if track != self.track_handler.song.master_track else False

                    mute_state = self._get_mute_state_code(track, solo, mute, muted_via_solo)

                    message_data = (
                        track_index,
                        "master" if track == self.track_handler.song.master_track else "return" if track in return_tracks else "track",
                        track.name,
                        track.is_foldable,
                        track.fold_state if track.is_foldable else False,
                        track.is_grouped,
                        mute_state,
                        arm_value,
                        track.color,
                        track.mixer_device.volume.value,
                        track.mixer_device.panning.value,
                        sends_values
                    )

                    self.track_handler.osc_server.send("/live/volumeMode/structure/track", message_data)

                    if track not in self.tracked_tracks:
                        self.logger.debug(f"Configuration des listeners pour la piste {track.name}")
                        self._setup_track_listeners(track)
                    else:
                        self.logger.debug(f"La piste {track.name} est déjà suivie")

            tracks_to_remove = set(self.tracked_tracks) - set(all_tracks)
            if tracks_to_remove:
                self.logger.debug(f"Suppression des listeners pour {len(tracks_to_remove)} pistes")
                for track in tracks_to_remove:
                    self._remove_track_listeners(track)

            self.track_handler.osc_server.send("/live/volumeMode/structure/end", (len(all_tracks),))
            self.logger.debug("=== Fin _update_listeners ===\n")

        except Exception as e:
            self.logger.error(f"Error in _update_volumeMode_listeners: {str(e)}")
        finally:
            self._updating_structure = False

    def _remove_all_listeners(self):
        """Supprime tous les listeners du volumeMode"""
        for track in list(self.tracked_tracks):
            self._remove_track_listeners(track)