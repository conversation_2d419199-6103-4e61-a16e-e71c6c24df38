from typing import <PERSON><PERSON>, Any, Callable, Optional
from .handler import <PERSON>bletonOSCHand<PERSON>
from .volume_mode import VolumeMode
from .track_mode import TrackMode
from .track_learn import TrackLearn


class TrackHandler(AbletonOSCHandler):
    def __init__(self, manager):
        super().__init__(manager)
        self.class_identifier = "track"
        self.names_return_track_listeners = {}
        self.tracked_tracks = set()
        self.track_learn = TrackLearn(self)

        self.logger.warning(f"TrackHandler instance created with id: {id(self)}")
        self.add_names_return_tracks_listener()



    def init_api(self):
        # Initialiser le VolumeMode
        self.volume_mode = VolumeMode(self)
        # Initialiser le TrackMode
        self.track_mode = TrackMode(self)

        # Ajouter les handlers pour le volumeMode
        self.osc_server.add_handler("/live/start_listen_volumeMode", self.volume_mode.start)
        self.osc_server.add_handler("/live/update_volumeMode", self.volume_mode._update_listeners)
        self.osc_server.add_handler("/live/stop_volumeMode", self.volume_mode.stop)

        # Ajouter les handlers pour le trackMode
        self.osc_server.add_handler("/live/start_listen_trackMode", self.track_mode.start_selected_track_listeners)
        self.osc_server.add_handler("/live/stop_trackMode", self.track_mode.stop_selected_track_listeners)

        # Ajouter les handlers pour le track learning
        self.osc_server.add_handler("/live/learn/setup_track",
            lambda x: self.track_learn.setup_tracks_listeners_for_learning(x))
        self.osc_server.add_handler("/live/learn/stop_track",
            lambda x: self.track_learn.stop_tracks_listeners_for_learning(x))





        def create_track_callback(func: Callable,
                                *args,
                                include_track_id: bool = False):
            def track_callback(params: Tuple[Any]):
                visible_tracks = [track for track in self.song.tracks if track.is_visible]
                total_tracks = len(visible_tracks) + len(self.song.return_tracks) + 1  # +1 for master track

                if params[0] == "*":
                    track_indices = list(range(total_tracks))
                else:
                    track_indices = [int(params[0])]

                for track_index in track_indices:
                    if track_index < len(visible_tracks):
                        track = visible_tracks[track_index]
                    elif track_index < len(visible_tracks) + len(self.song.return_tracks):
                        track = self.song.return_tracks[track_index - len(visible_tracks)]
                    else:
                        track = self.song.master_track

                    if include_track_id:
                        rv = func(track, *args, tuple([track_index] + params[1:]))
                    else:
                        rv = func(track, *args, tuple(params[1:]))

                    if rv is not None:
                        return (track_index, *rv)

            return track_callback


        methods = [
            "delete_device",
            "stop_all_clips"
        ]
        properties_r = [
            "can_be_armed",
            "fired_slot_index",
            "has_audio_input",
            "has_audio_output",
            "has_midi_input",
            "has_midi_output",
            #"is_foldable",
            "is_grouped",
            "is_visible",
            "output_meter_level",
            "output_meter_left",
            "output_meter_right",
            "playing_slot_index",
            "muted_via_solo",
        ]
        properties_rw = [
            "arm",
            "color",
            "color_index",
            "current_monitoring_state",
            "fold_state",
            "mute",
            "solo",
            "name",
        ]


        def track_get_is_foldable_and_fold_state(track, _):
            is_foldable = track.is_foldable
            fold_state = track.fold_state if is_foldable else False
            is_grouped = track.is_grouped
            return is_foldable, fold_state, is_grouped

        self.osc_server.add_handler("/live/volumeMode/get/is_foldable", create_track_callback(track_get_is_foldable_and_fold_state))

        for method in methods:
            self.osc_server.add_handler("/live/track/%s" % method,
                                        create_track_callback(self._call_method, method))

        for prop in properties_r + properties_rw:
            self.osc_server.add_handler("/live/track/get/%s" % prop,
                                        create_track_callback(self._get_property, prop))
            self.osc_server.add_handler("/live/track/start_listen/%s" % prop,
                                        create_track_callback(self._start_listen, prop, include_track_id=True))
            self.osc_server.add_handler("/live/track/stop_listen/%s" % prop,
                                        create_track_callback(self._stop_listen, prop, include_track_id=True))
        for prop in properties_rw:
            self.osc_server.add_handler("/live/track/set/%s" % prop,
                                        create_track_callback(self._set_property, prop))

        #--------------------------------------------------------------------------------
        # Volume, panning and send are properties of the track's mixer_device so
        # can't be formulated as normal callbacks that reference properties of track.
        #--------------------------------------------------------------------------------
        mixer_properties_rw = ["volume", "panning", "sends"]

        for prop in mixer_properties_rw:
            self.osc_server.add_handler("/live/track/get/%s" % prop,
                                        create_track_callback(self._get_mixer_property, prop))
            self.osc_server.add_handler("/live/track/set/%s" % prop,
                                        create_track_callback(self._set_mixer_property, prop))
            self.osc_server.add_handler("/live/track/start_listen/%s" % prop,
                                        create_track_callback(self._start_mixer_listen, prop, include_track_id=True))
            self.osc_server.add_handler("/live/track/stop_listen/%s" % prop,
                                        create_track_callback(self._stop_mixer_listen, prop, include_track_id=True))

        # Handlers pour les ajustements relatifs (delta) des paramètres du mixer
        self.osc_server.add_handler("/live/track/adjust/volume",
                                    create_track_callback(self._adjust_mixer_property, "volume"))
        self.osc_server.add_handler("/live/track/adjust/panning",
                                    create_track_callback(self._adjust_mixer_property, "panning"))
        self.osc_server.add_handler("/live/track/adjust/send",
                                    create_track_callback(self._adjust_mixer_send, None))



        def track_delete_clip(track, params: Tuple[Any]):
            clip_index, = params
            track.clip_slots[clip_index].delete_clip()

        self.osc_server.add_handler("/live/track/delete_clip", create_track_callback(track_delete_clip))

        def track_get_clip_names(track, _):
            return tuple(clip_slot.clip.name if clip_slot.clip else None for clip_slot in track.clip_slots)

        def track_get_clip_lengths(track, _):
            return tuple(clip_slot.clip.length if clip_slot.clip else None for clip_slot in track.clip_slots)

        def track_get_clip_colors(track, _):
            return tuple(clip_slot.clip.color if clip_slot.clip else None for clip_slot in track.clip_slots)

        def track_get_arrangement_clip_names(track, _):
            return tuple(clip.name for clip in track.arrangement_clips)

        def track_get_arrangement_clip_lengths(track, _):
            return tuple(clip.length for clip in track.arrangement_clips)

        def track_get_arrangement_clip_start_times(track, _):
            return tuple(clip.start_time for clip in track.arrangement_clips)

        """
        Returns a list of clip properties, or Nil if clip is empty
        """
        self.osc_server.add_handler("/live/track/get/clips/name", create_track_callback(track_get_clip_names))
        self.osc_server.add_handler("/live/track/get/clips/length", create_track_callback(track_get_clip_lengths))
        self.osc_server.add_handler("/live/track/get/clips/color", create_track_callback(track_get_clip_colors))
        self.osc_server.add_handler("/live/track/get/arrangement_clips/name", create_track_callback(track_get_arrangement_clip_names))
        self.osc_server.add_handler("/live/track/get/arrangement_clips/length", create_track_callback(track_get_arrangement_clip_lengths))
        self.osc_server.add_handler("/live/track/get/arrangement_clips/start_time", create_track_callback(track_get_arrangement_clip_start_times))

        def track_get_num_devices(track, _):
            return len(track.devices),

        def track_get_device_names(track, _):
            self.logger.info("track_get_device_names called")
            all_devices = self.manager.get_visible_devices(track)
            self.logger.info(device.name for device in all_devices)
            return tuple(device.name for device in all_devices)

        def track_get_device_types(track, _):
            return tuple(device.type for device in track.devices)

        def track_get_device_class_names(track, _):
            return tuple(device.class_name for device in track.devices)

        def track_get_device_can_have_chains(track, _):
            return tuple(device.can_have_chains for device in track.devices)

        """
         - name: the device's human-readable name
         - type: 0 = audio_effect, 1 = instrument, 2 = midi_effect
         - class_name: e.g. Operator, Reverb, AuPluginDevice, PluginDevice, InstrumentGroupDevice
        """
        self.osc_server.add_handler("/live/track/get/num_devices", create_track_callback(track_get_num_devices))
        self.osc_server.add_handler("/live/track/get/devices/name", create_track_callback(track_get_device_names))
        self.osc_server.add_handler("/live/track/get/devices/type", create_track_callback(track_get_device_types))
        self.osc_server.add_handler("/live/track/get/devices/class_name", create_track_callback(track_get_device_class_names))
        self.osc_server.add_handler("/live/track/get/devices/can_have_chains", create_track_callback(track_get_device_can_have_chains))

        #--------------------------------------------------------------------------------
        # Track: Output routing.
        # An output route has a type (e.g. "Ext. Out") and a channel (e.g. "1/2").
        # Since Live 10, both of these need to be set by reference to the appropriate
        # item in the available_output_routing_types vector.
        #--------------------------------------------------------------------------------
        def track_get_available_output_routing_types(track, _):
            return tuple([routing_type.display_name for routing_type in track.available_output_routing_types])

        def track_get_available_output_routing_channels(track, _):
            return tuple([routing_channel.display_name for routing_channel in track.available_output_routing_channels])

        def track_get_output_routing_type(track, _):
            return track.output_routing_type.display_name,

        def track_set_output_routing_type(track, params):
            type_name = str(params[0])
            for routing_type in track.available_output_routing_types:
                if routing_type.display_name == type_name:
                    track.output_routing_type = routing_type
                    return
            self.logger.warning("Couldn't find output routing type: %s" % type_name)

        def track_get_output_routing_channel(track, _):
            return track.output_routing_channel.display_name,

        def track_set_output_routing_channel(track, params):
            channel_name = str(params[0])
            for channel in track.available_output_routing_channels:
                if channel.display_name == channel_name:
                    track.output_routing_channel = channel
                    return
            self.logger.warning("Couldn't find output routing channel: %s" % channel_name)

        self.osc_server.add_handler("/live/track/get/available_output_routing_types", create_track_callback(track_get_available_output_routing_types))
        self.osc_server.add_handler("/live/track/get/available_output_routing_channels", create_track_callback(track_get_available_output_routing_channels))
        self.osc_server.add_handler("/live/track/get/output_routing_type", create_track_callback(track_get_output_routing_type))
        self.osc_server.add_handler("/live/track/set/output_routing_type", create_track_callback(track_set_output_routing_type))
        self.osc_server.add_handler("/live/track/get/output_routing_channel", create_track_callback(track_get_output_routing_channel))
        self.osc_server.add_handler("/live/track/set/output_routing_channel", create_track_callback(track_set_output_routing_channel))

        #--------------------------------------------------------------------------------
        # Track: Input routing.
        #--------------------------------------------------------------------------------
        def track_get_available_input_routing_types(track, _):
            return tuple([routing_type.display_name for routing_type in track.available_input_routing_types])

        def track_get_available_input_routing_channels(track, _):
            return tuple([routing_channel.display_name for routing_channel in track.available_input_routing_channels])

        def track_get_input_routing_type(track, _):
            return track.input_routing_type.display_name,

        def track_set_input_routing_type(track, params):
            type_name = str(params[0])
            for routing_type in track.available_input_routing_types:
                if routing_type.display_name == type_name:
                    track.input_routing_type = routing_type
                    return
            self.logger.warning("Couldn't find input routing type: %s" % type_name)

        def track_get_input_routing_channel(track, _):
            return track.input_routing_channel.display_name,

        def track_set_input_routing_channel(track, params):
            channel_name = str(params[0])
            for channel in track.available_input_routing_channels:
                if channel.display_name == channel_name:
                    track.input_routing_channel = channel
                    return
            self.logger.warning("Couldn't find input routing channel: %s" % channel_name)

        self.osc_server.add_handler("/live/track/get/available_input_routing_types", create_track_callback(track_get_available_input_routing_types))
        self.osc_server.add_handler("/live/track/get/available_input_routing_channels", create_track_callback(track_get_available_input_routing_channels))
        self.osc_server.add_handler("/live/track/get/input_routing_type", create_track_callback(track_get_input_routing_type))
        self.osc_server.add_handler("/live/track/set/input_routing_type", create_track_callback(track_set_input_routing_type))
        self.osc_server.add_handler("/live/track/get/input_routing_channel", create_track_callback(track_get_input_routing_channel))
        self.osc_server.add_handler("/live/track/set/input_routing_channel", create_track_callback(track_set_input_routing_channel))

        self.osc_server.add_handler("/live/track_lock", lambda x: self.track_mode.trackLock_handler(x))
        self.osc_server.add_handler("/live/trackMode/select/parent", lambda x: self.track_mode.trackParent_handler(x))
        self.osc_server.add_handler("/live/trackMode/select/daughter", lambda x: self.track_mode.trackDaughter_handler(x))
        self.osc_server.add_handler("/live/trackMode/select/sister", lambda x: self.track_mode.trackSister_handler(x))



        def track_toggle_solo(track, _):
            # Déterminer si c'est une piste return ou normale
            visible_tracks = [t for t in self.song.tracks if t.is_visible]
            return_tracks = list(self.song.return_tracks)  # Convertir en liste pour pouvoir utiliser index()
            is_return_track = track in return_tracks

            # Calculer l'index de la piste
            if track in visible_tracks:
                track_index = visible_tracks.index(track)
            elif is_return_track:
                track_index = len(visible_tracks) + return_tracks.index(track)
            else:  # master track
                track_index = len(visible_tracks) + len(return_tracks)

            if track.solo:
                # Si déjà en solo, simplement désactiver
                track.solo = False
            else:
                # Si pas en solo, activer le solo et désactiver les autres
                if is_return_track:
                    # Pour les pistes return, désactiver le solo des autres pistes return
                    for other_track in return_tracks:
                        if other_track != track:
                            other_track.solo = False
                else:
                    # Pour les pistes normales, désactiver le solo des autres pistes normales
                    for other_track in visible_tracks:
                        if other_track != track:
                            other_track.solo = False

                # Activer le solo de la piste courante
                track.solo = True

        def track_toggle_fold_state(track, _):
            # Vérifier si la piste est pliable
            if track.is_foldable:
                # Basculer l'état de pliage
                track.fold_state = not track.fold_state

        def track_toggle_mute(track, _):
            track.mute = not track.mute

        def track_toggle_arm(track, _):
            track.arm = not track.arm

        self.osc_server.add_handler("/live/track/set/foldtoggle", create_track_callback(track_toggle_fold_state))
        self.osc_server.add_handler("/live/track/set/solotoggle", create_track_callback(track_toggle_solo))
        self.osc_server.add_handler("/live/track/set/mutetoggle", create_track_callback(track_toggle_mute))
        self.osc_server.add_handler("/live/track/set/armtoggle", create_track_callback(track_toggle_arm))
        self.osc_server.add_handler("/live/start_listen_tracks", self._start_listen_tracks)

        # Ajout du handler pour clear_listeners
        #self.osc_server.add_handler("/live/track/clear_listeners", lambda _: self.clear_track_listeners())

        self.logger.info("Registering /live/learn/stop_track handler")
        self.osc_server.add_handler("/live/learn/stop_track",
            lambda x: self.track_learn.stop_tracks_listeners_for_learning(x))
        self.logger.info("Handler registered successfully")
        self.osc_server.add_handler("/live/return_tracks_name", self.add_names_return_tracks_listener)



    def add_names_return_tracks_listener(self, *args):
        #le *args n'est pas utilisé mais c'est juste pour ne pas renvoyer d'erreur quand cette fonction est utilisée en callback
        return_tracks = self.song.return_tracks
        names = [track.name for track in return_tracks]
        self.logger.info(f"Return tracks names changed: {names}")
        self.osc_server.send("/live/return_tracks_name", names)

        # Suppression de tous les listeners existants
        #self.logger.info("Removing old listeners")
        for track_name, listener in list(self.names_return_track_listeners.items()):
            track_index = int(track_name.split('_')[-1])
            if track_index < len(return_tracks):
                track = return_tracks[track_index]
                if track.name_has_listener(listener):
                    track.remove_name_listener(listener)
                    #self.logger.info(f"Removed listener from {track_name}")
            del self.names_return_track_listeners[track_name]

        # Ajout des nouveaux listeners pour chaque return track
        for i, track in enumerate(return_tracks):
            track_name = f"return_track_{i}"

            # Vérification avant l'ajout du nouveau listener
            if not track.name_has_listener(self.add_names_return_tracks_listener):
                try:
                    track.add_name_listener(self.add_names_return_tracks_listener)
                    self.names_return_track_listeners[track_name] = self.add_names_return_tracks_listener
                    #self.logger.info(f"Added listener to {track_name}")
                except Exception as e:
                    self.logger.error(f"Error adding listener to {track_name}: {str(e)}")

    def _get_mixer_property(self, target, prop, params: Optional[Tuple] = ()):
        if prop == "sends":
            send_id = params[0]
            return target.mixer_device.sends[send_id].value,
        else:
            parameter_object = getattr(target.mixer_device, prop)
            return parameter_object.value,



    def _set_mixer_property(self, target, prop, params: Tuple):
        self.logger.info(f"_set_mixer_property called")

        # Vérification si le verrou de piste est activé
        if self.manager.trackLock and self.manager.lockedTrack:
            # Si oui, on utilise la piste verrouillée
            target = self.manager.lockedTrack

        if prop == "sends":
            send_id, value = params
            # Forcer send_id en int
            send_id = int(send_id)
            target.mixer_device.sends[send_id].value = value
        else:
            parameter_object = getattr(target.mixer_device, prop)
            parameter_object.value = params[0]

    def _adjust_mixer_property(self, target, prop, params: Tuple):
        """Ajuste un paramètre du mixer en appliquant un delta à la valeur actuelle"""
        self.logger.info(f"_adjust_mixer_property called for {prop} with delta {params[0]}")

        # Vérification si le verrou de piste est activé
        if self.manager.trackLock and self.manager.lockedTrack:
            # Si oui, on utilise la piste verrouillée
            target = self.manager.lockedTrack

        # Récupérer l'objet de paramètre et sa valeur actuelle
        parameter_object = getattr(target.mixer_device, prop)
        current_value = parameter_object.value

        # Calculer la nouvelle valeur en ajoutant le delta
        delta = float(params[0])

        # Réduire l'impact du delta pour éviter les sauts trop importants
        # Diviser par 5 pour rendre les changements plus progressifs
        scaled_delta = delta / 5.0

        self.logger.info(f"Original delta: {delta}, Scaled delta: {scaled_delta}")

        new_value = current_value + scaled_delta

        # Limiter la valeur dans la plage appropriée
        if prop == "volume":
            # Volume est entre 0.0 et 1.0
            new_value = max(0.0, min(1.0, new_value))
        elif prop == "panning":
            # Panning est entre -1.0 et 1.0
            new_value = max(-1.0, min(1.0, new_value))

        # Vérifier si la valeur a réellement changé
        if abs(new_value - current_value) < 0.0001:
            self.logger.info(f"Change too small, skipping update for {prop}")
            return

        # Appliquer la nouvelle valeur
        self.logger.info(f"Setting {prop} from {current_value} to {new_value} (scaled delta: {scaled_delta})")
        parameter_object.value = new_value
        self.logger.info(f"After setting, {prop} value is now: {parameter_object.value}")

    def _adjust_mixer_send(self, target, _, params: Tuple):
        """Ajuste la valeur d'un send en appliquant un delta à la valeur actuelle"""
        self.logger.info(f"_adjust_mixer_send called with params {params}")

        # Vérification si le verrou de piste est activé
        if self.manager.trackLock and self.manager.lockedTrack:
            # Si oui, on utilise la piste verrouillée
            target = self.manager.lockedTrack

        # Extraire les paramètres
        send_id, delta = params
        send_id = int(send_id)
        delta = float(delta)

        # Vérifier que le send existe
        if not hasattr(target.mixer_device, 'sends') or send_id >= len(target.mixer_device.sends):
            self.logger.warning(f"Send {send_id} does not exist for track {target.name}")
            return

        # Récupérer l'objet send et sa valeur actuelle
        send_object = target.mixer_device.sends[send_id]
        current_value = send_object.value

        # Réduire l'impact du delta pour éviter les sauts trop importants
        # Diviser par 5 pour rendre les changements plus progressifs
        scaled_delta = delta / 5.0

        self.logger.info(f"Original delta: {delta}, Scaled delta: {scaled_delta}")

        # Calculer la nouvelle valeur en ajoutant le delta
        new_value = current_value + scaled_delta

        # Limiter la valeur entre 0.0 et 1.0
        new_value = max(0.0, min(1.0, new_value))

        # Vérifier si la valeur a réellement changé
        if abs(new_value - current_value) < 0.0001:
            self.logger.info(f"Change too small, skipping update for send {send_id}")
            return

        # Appliquer la nouvelle valeur
        self.logger.info(f"Setting send {send_id} from {current_value} to {new_value} (scaled delta: {scaled_delta})")
        send_object.value = new_value
        self.logger.info(f"After setting, send {send_id} value is now: {send_object.value}")


    def _start_listen(self, target, prop, params: Optional[Tuple] = (), getter = None) -> None:
            self.logger.info(f"_start_listen called for {prop}")

            def property_changed_callback():
                if getter is None:
                    value = getattr(target, prop)
                else:
                    value = getter(params)
                if type(value) is not tuple:
                    value = (value,)

                # Gestion spéciale pour muted_via_solo
                if prop == "muted_via_solo":
                    # Déterminer si la piste actuelle est une piste return
                    current_track = target
                    is_return = current_track in self.song.return_tracks

                    # Vérifier quelles pistes sont en solo
                    normal_tracks_soloed = any(t.solo for t in self.song.tracks if t not in self.song.return_tracks)
                    return_tracks_soloed = any(rt.solo for rt in self.song.return_tracks)

                    # Si une piste return est en solo, seules les autres pistes return doivent être muted_via_solo
                    if return_tracks_soloed and not is_return:
                        return

                    # Si une piste normale est en solo, seules les autres pistes normales doivent être muted_via_solo
                    if normal_tracks_soloed and is_return:
                        return

                self.logger.info(f"Property {prop} changed of {self.class_identifier} {str(params)}: {value}")
                osc_address = f"/live/{self.class_identifier}/get/{prop}"
                self.osc_server.send(osc_address, (*params, *value,))

            listener_key = (prop, tuple(params))
            if listener_key in self.listener_functions:
                self._stop_listen(target, prop, params)

            self.logger.info("Adding listener for %s %s, property: %s" % (self.class_identifier, str(params), prop))
            add_listener_function_name = "add_%s_listener" % prop
            add_listener_function = getattr(target, add_listener_function_name)
            add_listener_function(property_changed_callback)
            self.listener_functions[listener_key] = property_changed_callback
            self.listener_objects[listener_key] = target

            #property_changed_callback()


    def _start_mixer_listen(self, target, prop, params: Optional[Tuple] = ()):
        self.logger.info(f"_start_mixer_listen called")



        if prop == "sends":
            send_id = int(params[1])  # Utilisez params[1] au lieu de params[0]
            # Vérifier si c'est la piste master et s'il y a des sends avant d'y accéder
            if hasattr(target.mixer_device, 'sends') and len(target.mixer_device.sends) > send_id:
                parameter_object = target.mixer_device.sends[send_id]
            else:
                # Ignorer silencieusement si pas de sends ou index invalide
                return
        else:
            parameter_object = getattr(target.mixer_device, prop)

        def property_changed_callback():
            value = parameter_object.value
            self.logger.info(f"Property {prop} changed of {self.class_identifier} {str(params)}: {value}")
            if prop == "sends":
                osc_address = f"/live/{self.class_identifier}/get/{prop}"
                self.osc_server.send(osc_address, (params[0], send_id, value))  # Inclure l'ID de la piste et l'ID du send
            else:
                osc_address = f"/live/{self.class_identifier}/get/{prop}"
                self.osc_server.send(osc_address, (*params, value))

        listener_key = (prop, tuple(params))
        if listener_key in self.listener_functions:
            self._stop_mixer_listen(target, prop, params)

        self.logger.info(f"Adding mixer listener for {self.class_identifier} {str(params)}, property: {prop}")

        parameter_object.add_value_listener(property_changed_callback)
        self.listener_functions[listener_key] = property_changed_callback
        self.listener_objects[listener_key] = target

        # Appeler property_changed_callback() uniquement pour panning et sends
        if prop in ["panning", "sends"]:
            property_changed_callback()


    def _stop_mixer_listen(self, target, prop, params: Optional[Tuple[Any]] = ()):
        listener_key = (prop, tuple(params))
        if listener_key in self.listener_functions:
            try:
                if prop == "sends":
                    send_id = int(params[1])
                    if hasattr(target.mixer_device, 'sends') and len(target.mixer_device.sends) > send_id:
                        target.mixer_device.sends[send_id].remove_value_listener(self.listener_functions[listener_key])
                else:
                    parameter_object = getattr(target.mixer_device, prop)
                    parameter_object.remove_value_listener(self.listener_functions[listener_key])
                self.logger.info(f"Successfully removed mixer listener for {prop}")
            except Exception as e:
                self.logger.error(f"Error removing mixer listener for {prop}: {e}")
            finally:
                # Toujours supprimer les références, même en cas d'erreur
                del self.listener_functions[listener_key]
                if listener_key in self.listener_objects:
                    del self.listener_objects[listener_key]
        else:
            self.logger.warning(f"No mixer listener function found for property: {prop} ({str(params)})")


    def clear_api(self):
        super().clear_api()
        self.logger.info("Clearing all listeners, including mixer listeners")

        # Nettoyer tous les listeners, y compris ceux du mixer
        for listener_key in list(self.listener_functions.keys()):
            prop, params = listener_key
            try:
                if prop in ["volume", "panning", "sends"]:
                    if listener_key in self.listener_objects:
                        track = self.listener_objects[listener_key]
                        self._stop_mixer_listen(track, prop, params)
                    else:
                        self.logger.warning(f"No track found for mixer listener: {prop} {params}")
                else:
                    if listener_key in self.listener_objects:
                        target = self.listener_objects[listener_key]
                        self._stop_listen(target, prop, params)
                    else:
                        self.logger.warning(f"No target found for listener: {prop} {params}")
            except Exception as e:
                self.logger.error(f"Error clearing listener for {prop}: {e}")

        # S'assurer que tous les dictionnaires sont vides
        self.listener_functions.clear()
        self.listener_objects.clear()

    def _start_listen_tracks(self, params):
        start_index, end_index = map(int, params)
        visible_tracks = [track for track in self.song.tracks if track.is_visible]
        total_tracks = len(visible_tracks) + len(self.song.return_tracks) + 1  # +1 pour master track

        # Assurer que les index sont dans les limites valides
        start_index = max(0, min(start_index, total_tracks - 1))
        end_index = max(0, min(end_index, total_tracks - 1))

        for track_index in range(start_index, end_index + 1):
            # Déterminer la piste actuelle
            if track_index < len(visible_tracks):
                track = visible_tracks[track_index]
            elif track_index < len(visible_tracks) + len(self.song.return_tracks):
                track = self.song.return_tracks[track_index - len(visible_tracks)]
            else:
                track = self.song.master_track

            # Démarrer les listeners pour les propriétés communes
            self._start_mixer_listen(track, "volume", (track_index,))
            self._start_listen(track, "color", (track_index,))
            self._start_listen(track, "name", (track_index,))

            # Pour les pistes non-master
            if track != self.song.master_track:
                # Ajouter les listeners pour mute, solo, et muted_via_solo
                self._start_listen(track, "mute", (track_index,))
                self._start_listen(track, "muted_via_solo", (track_index,))
                self._start_listen(track, "solo", (track_index,))

            # Obtenir les valeurs actuelles
            is_foldable = track.is_foldable
            fold_state = track.fold_state if is_foldable else False
            is_grouped = track.is_grouped
            mute = track.mute if track != self.song.master_track else False
            muted_via_solo = track.muted_via_solo if track != self.song.master_track else False
            solo = track.solo if track != self.song.master_track else False
            color = track.color
            name = track.name
            volume = track.mixer_device.volume.value

            # Envoyer le message OSC
            self.osc_server.send("/live/start_listen_tracks", (
                track_index,
                is_foldable,
                fold_state,
                is_grouped,
                mute,
                muted_via_solo,
                solo,
                color,
                name,
                volume
            ))