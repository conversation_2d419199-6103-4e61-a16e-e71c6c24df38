import logging

logger = logging.getLogger("abletonosc")

class StructureListeners:
    def __init__(self, manager):
        self.manager = manager
        self.song = manager.song
        
    def setup_listeners(self):
        """Initialize all structure listeners"""
        self._add_tracks_listener()        
        self._add_return_tracks_listener()
        self._add_selected_device_listener()
        self._add_selected_track_listener()
        self._add_track_folding_listeners()        
        # self._add_rack_collapse_listeners()
        # Call callbacks directly to get initial values
        self.manager.on_tracks_changed()
        self.manager.on_selected_track_or_device_changed()
        self.manager.on_return_tracks_changed()
        
    def _add_tracks_listener(self):
        """Add listener for track changes"""
        try:
            self.song.add_tracks_listener(self.manager.on_tracks_changed)
            logger.info("Added tracks listener")
        except Exception as e:
            logger.error(f"Failed to add tracks listener: {e}")
  
    def _add_selected_device_listener(self):
        """Add listeners for selected track and device changes"""
        try:            
            self.song.view.selected_track.view.add_selected_device_listener(self.manager.on_selected_track_or_device_changed)
            logger.info("Added selected device listeners")
        except Exception as e:
            logger.error(f"Failed to add selected track and device listeners: {e}")

    def _add_selected_track_listener(self):
        """Add listeners for selected track and device changes"""
        try:
            self.song.view.add_selected_track_listener(self.manager.on_selected_track_or_device_changed)
            logger.info("Added selected track listeners")
        except Exception as e:
            logger.error(f"Failed to add selected track and device listeners: {e}")
               
    def _add_return_tracks_listener(self):
        """Add listener for return track changes"""
        try:
            self.song.add_return_tracks_listener(self.manager.on_return_tracks_changed)
            logger.info("Added return tracks listener")
        except Exception as e:
            logger.error(f"Failed to add return tracks listener: {e}")
            
    def _add_track_folding_listeners(self):
        """Add listeners for track folding state changes"""
        self._remove_track_folding_listeners()  # Remove existing listeners first
        try:
            for track in self.song.tracks:
                if track.is_foldable:
                    track.view.add_is_collapsed_listener(self.manager.on_tracks_changed)
            logger.info("Added track folding listeners")
        except Exception as e:
            logger.error(f"Failed to add track folding listeners: {e}")

    def _remove_track_folding_listeners(self):
        """Remove all track folding listeners"""
        for track in self.song.tracks:
            if track.is_foldable:
                try:
                    track.view.remove_is_collapsed_listener(self.manager.on_tracks_changed)
                except:  # Assuming ValueError is raised when the listener doesn't exist
                    pass  # Ignore the error for this specific track
        logger.info("Removed track folding listeners")

    ############################################################
    # def _add_rack_collapse_listeners(self):
    #     """Add listeners for rack collapse state changes"""
    #     self._remove_rack_collapse_listeners()
    #     selected_track = self.song.view.selected_track
    #     for device in self.manager.get_visible_devices(selected_track):
    #         if device.can_have_chains:
    #             device.view.add_is_collapsed_listener(self.manager.on_selected_track_or_device_changed)
    #     logger.info(f"Added rack collapse listeners to track: {selected_track.name}")
    #
    #
    # def _remove_rack_collapse_listeners(self):
    #     """Remove all rack collapse listeners"""
    #     logger.info("remove rack collapse listeners called")
    #     all_tracks = tuple(self.song.tracks) + tuple(self.song.return_tracks) + (self.song.master_track,)
    #     for track in all_tracks:
    #         for device in self.manager.get_visible_devices(track):
    #             if device.can_have_chains:
    #                 try:
    #                     device.view.remove_is_collapsed_listener(self.manager.on_selected_track_or_device_changed)
    #                 except:
    #                     pass  # In case the listener didn't exist
    #     logger.info("Removed all rack collapse listeners")
    ############################################################

    
    def _remove_selected_chain_listener(self):
        """Remove the selected chain listener from the current device if it exists"""
        try:
            selected_track = self.song.view.selected_track
            selected_device = selected_track.view.selected_device
            if selected_device and selected_device.can_have_chains:
                selected_device.view.remove_selected_chain_listener(
                    self.manager.on_selected_track_or_device_changed
                )
                logger.info("Removed selected chain listener")
        except Exception as e:
            logger.error(f"Failed to remove selected chain listener: {e}")


    