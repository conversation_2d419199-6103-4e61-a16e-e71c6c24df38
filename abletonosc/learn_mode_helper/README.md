# Learn Mode Helper

Ce dossier contient tous les modules pour la gestion du mode learn, organisés de manière modulaire pour une meilleure lisibilité et maintenance.

## Structure des fichiers

### `learn_mode_encoder_handlers.py`
Gère les handlers d'encoders pour le mode learn. Contient la logique pour ajuster les paramètres via les encoders avec support des paramètres quantifiés et non-quantifiés.

### `learn_mode_listeners.py`
Contient toutes les fonctions de configuration des listeners pour les différents types de paramètres :
- Listeners de piste (volume, pan, send, mute, solo)
- Listeners de device (paramètres de device)
- Listeners de chaîne (volume, pan, mute, solo de chaîne)
- Listeners de base (nom et couleur de piste)

### `learn_mode_utils.py`
Fonctions utilitaires pour :
- Gestion des chemins de chaînes (`_get_chain_path`, `_get_chain_by_path`)
- Recherche d'éléments dans la hiérarchie (`_find_device_or_chain_track`)
- Mise à jour des listeners lors de changements

### `learn_mode_slot_management.py`
Gestion des slots d'apprentissage :
- Vérification et suppression des doublons (`_check_duplicates`)
- Suppression de slots individuels (`delete_single_slot`)
- Validation de la validité des slots (`verify_learn_slots_validity`)
- Envoi des références de slots (`send_learn_slot_reference`)

### `learn_mode_osc_actions.py`
Actions déclenchées par les messages OSC :
- `start_learn_listen` : Démarre l'écoute d'un paramètre
- `select_learn_slot_element` : Sélectionne l'élément associé à un slot
- `stop_learn_listen` : Arrête l'écoute de tous les slots

### `learn_mode_value_actions.py`
Gestion des modifications de valeurs :
- `set_learn_slot_value` : Définit la valeur d'un paramètre assigné à un slot

## Avantages de cette organisation

1. **Lisibilité** : Chaque fichier a une responsabilité claire et spécifique
2. **Maintenance** : Plus facile de trouver et modifier du code spécifique
3. **Réutilisabilité** : Les fonctions peuvent être facilement réutilisées
4. **Tests** : Chaque module peut être testé indépendamment
5. **Collaboration** : Plusieurs développeurs peuvent travailler sur différents aspects

## Utilisation

Le fichier principal `learn.py` importe toutes les fonctions nécessaires et les assigne comme méthodes de la classe `LearnModeHandler`. Cette approche maintient la compatibilité avec le code existant tout en bénéficiant de l'organisation modulaire.

## Types de paramètres supportés

- **Type 1** : Volume de piste
- **Type 2** : Pan de piste  
- **Type 3** : Send de piste
- **Type 4** : Paramètre de device
- **Type 5** : Volume de chaîne
- **Type 6** : Pan de chaîne
- **Type 7** : Mute de piste
- **Type 8** : Solo de piste
- **Type 9** : Mute de chaîne
- **Type 10** : Solo de chaîne
