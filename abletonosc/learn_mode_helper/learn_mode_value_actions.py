"""
Actions de valeur pour le mode learn
Gère les modifications de valeurs des paramètres assignés aux slots
"""


def set_learn_slot_value(self, params):
    """Définit la valeur d'un paramètre assigné à un slot d'apprentissage"""
    # Le handler OSC /live/set prend maintenant 2 arguments: slot et value
    if len(params) < 2:
        self.logger.error(f"Nombre de paramètres insuffisant pour /live/set/learn: {params}")
        return

    try:
        slot = int(params[0])
        value = float(params[1])
    except (ValueError, TypeError) as e:
        self.logger.error(f"Erreur de conversion des paramètres OSC: {e}")
        return

    self.logger.debug(f"Traitement /live/set: slot={slot}, value={value}")

    if slot not in self.learn_slots:
        self.logger.error(f"Index de slot d'apprentissage invalide: {slot}")
        return

    slot_data = self.learn_slots[slot]
    track = slot_data.get("track")
    device = slot_data.get("device")
    chain = slot_data.get("chain")
    param_type = slot_data.get("param_type")

    if param_type == 1:  # Volume Track
        if track:
            track.mixer_device.volume.value = value
            self.logger.debug(f"Volume piste (slot {slot}) réglé sur {value}")
        else:
            self.logger.warning(f"Aucune piste trouvée pour le slot {slot} (type {param_type})")
            self.osc_server.send("/live/learn/slot/cleared", (slot,))
            
    elif param_type == 2:  # Pan Track
        if track:
            track.mixer_device.panning.value = value
            self.logger.debug(f"Panoramique piste (slot {slot}) réglé sur {value}")
        else:
            self.logger.warning(f"Aucune piste trouvée pour le slot {slot} (type {param_type})")
            self.osc_server.send("/live/learn/slot/cleared", (slot,))
            
    elif param_type == 3:  # Send Track
        send_index = slot_data.get("send_index")
        if track and send_index is not None and 0 <= send_index < len(track.mixer_device.sends):
            track.mixer_device.sends[send_index].value = value
            self.logger.debug(f"Send {send_index} piste (slot {slot}) réglé sur {value}")
        else:
            if not track:
                self.logger.warning(f"Aucune piste trouvée pour le slot {slot} (type {param_type})")
            elif send_index is None:
                self.logger.warning(f"Index de Send non défini pour le slot {slot}")
            elif not (0 <= send_index < len(track.mixer_device.sends)):
                self.logger.warning(f"Index de Send invalide ({send_index}) pour la piste du slot {slot}")
            self.osc_server.send("/live/learn/slot/cleared", (slot,))
            
    elif param_type == 4:  # Device Parameter
        param_index = slot_data.get("param_index")
        if device and param_index is not None and 0 <= param_index < len(device.parameters):
            device.parameters[param_index].value = value
            self.logger.debug(f"Paramètre device {param_index} (slot {slot}) réglé sur {value}")
        else:
            if not device:
                self.logger.warning(f"Aucun device trouvé pour le slot {slot} (type {param_type})")
            elif param_index is None:
                self.logger.warning(f"Index de paramètre non défini pour le slot {slot}")
            elif not (0 <= param_index < len(device.parameters)):
                self.logger.warning(f"Index de paramètre invalide ({param_index}) pour le device du slot {slot}")
            self.osc_server.send("/live/learn/slot/cleared", (slot,))
            
    elif param_type == 5:  # Chain Volume
        if chain:
            chain.mixer_device.volume.value = value
            chain_path = self._get_chain_path(chain)
            self.osc_server.send("/live/chain/learning/volume", (-2, chain_path, value))
            self.logger.debug(f"Volume chaîne (slot {slot}) réglé sur {value}")
        else:
            self.logger.warning(f"Aucune chaîne trouvée pour le slot {slot} (type {param_type})")
            self.osc_server.send("/live/learn/slot/cleared", (slot,))
            
    elif param_type == 6:  # Chain Pan
        if chain:
            chain.mixer_device.panning.value = value
            chain_path = self._get_chain_path(chain)
            self.osc_server.send("/live/chain/learning/panning", (-2, chain_path, value))
            self.logger.debug(f"Panoramique chaîne (slot {slot}) réglé sur {value}")
        else:
            self.logger.warning(f"Aucune chaîne trouvée pour le slot {slot} (type {param_type})")
            self.osc_server.send("/live/learn/slot/cleared", (slot,))
            
    elif param_type == 7:  # Mute Track
        if track and track != self.song.master_track:
            track.mute = not track.mute  # Toggle du mute
            new_mute_state = 1 if track.mute else 0
            self.logger.debug(f"Mute piste (slot {slot}) basculé à {bool(new_mute_state)}")
        else:
            if not track:
                self.logger.warning(f"Aucune piste trouvée pour le slot {slot} (type {param_type})")
            elif track == self.song.master_track:
                self.logger.warning(f"Impossible de muter la piste Master via le slot {slot}")
            self.osc_server.send("/live/learn/slot/cleared", (slot,))
            
    elif param_type == 8:  # Solo Track
        if track and track != self.song.master_track:
            track.solo = not track.solo  # Toggle du solo
            new_solo_state = 1 if track.solo else 0
            self.logger.debug(f"Solo piste (slot {slot}) basculé à {bool(new_solo_state)}")
        else:
            if not track:
                self.logger.warning(f"Aucune piste trouvée pour le slot {slot} (type {param_type})")
            elif track == self.song.master_track:
                self.logger.warning(f"Impossible de mettre en solo la piste Master via le slot {slot}")
            self.osc_server.send("/live/learn/slot/cleared", (slot,))
            
    elif param_type == 9:  # Chain Mute (Toggle)
        if chain:
            chain.mute = not chain.mute  # Toggle du mute de la chaîne
            new_mute_state = 1 if chain.mute else 0
            chain_path = self._get_chain_path(chain)
            self.osc_server.send("/live/chain/learning/mute", (-2, chain_path, new_mute_state))
            self.logger.debug(f"Mute chaîne (slot {slot}) basculé à {bool(new_mute_state)}")
        else:
            self.logger.warning(f"Aucune chaîne trouvée pour le slot {slot} (type {param_type})")
            self.osc_server.send("/live/learn/slot/cleared", (slot,))
            
    elif param_type == 10:  # Chain Solo (Toggle)
        if chain:
            chain.solo = not chain.solo  # Toggle du solo de la chaîne
            new_solo_state = 1 if chain.solo else 0
            chain_path = self._get_chain_path(chain)
            self.osc_server.send("/live/chain/learning/solo", (-2, chain_path, new_solo_state))
            self.logger.debug(f"Solo chaîne (slot {slot}) basculé à {bool(new_solo_state)}")
        else:
            self.logger.warning(f"Aucune chaîne trouvée pour le slot {slot} (type {param_type})")
            self.osc_server.send("/live/learn/slot/cleared", (slot,))
            
    else:
        self.logger.error(f"Type de paramètre inconnu: {param_type}")
