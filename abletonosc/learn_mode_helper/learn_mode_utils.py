"""
Utilitaires pour le mode learn
Fonctions d'aide pour la gestion des chaînes et la recherche d'éléments
"""


def _get_chain_path(self, chain):
    """
    Récupère le chemin complet d'une chaîne sous forme de liste d'indices.
    Similaire à _get_chain_path_indices dans device_mode_environment.py
    """
    try:
        self.logger.debug(f"Récupération du chemin pour la chaîne: {chain.name}")

        def find_chain_path(devices, target_chain):
            for rack_index, device in enumerate(devices):
                if hasattr(device, 'chains'):
                    # Vérifier si la chaîne cible est directement dans ce rack
                    for chain_index, current_chain in enumerate(device.chains):
                        if current_chain == target_chain:
                            return [rack_index, chain_index]

                        # Chercher dans les devices de cette chaîne
                        if current_chain.devices:
                            deeper_path = find_chain_path(current_chain.devices, target_chain)
                            if deeper_path:
                                return [rack_index, chain_index] + deeper_path
            return None

        # Commencer la recherche depuis les devices de la piste
        selected_track = self.song.view.selected_track
        if not selected_track:
            return []

        path = find_chain_path(selected_track.devices, chain) or []

        self.logger.debug(f"Chemin trouvé pour la chaîne: {path}")
        return path

    except Exception as e:
        self.logger.error(f"Erreur dans _get_chain_path: {str(e)}")
        return []


def _get_chain_by_path(self, chain_path):
    """
    Récupère une chaîne à partir de son chemin.
    Le chemin est une liste d'indices [rack_idx, chain_idx, ...]
    """
    try:
        if not chain_path or len(chain_path) < 2:
            self.logger.warning("Chemin de chaîne invalide ou trop court")
            return None

        # Convertir tous les indices en entiers si ce n'est pas déjà le cas
        if not all(isinstance(idx, int) for idx in chain_path):
            try:
                chain_path = [int(idx) for idx in chain_path]
            except (ValueError, TypeError) as e:
                self.logger.error(f"Erreur de conversion des indices: {e}")
                return None

        selected_track = self.song.view.selected_track
        if not selected_track:
            self.logger.warning("Aucune piste sélectionnée")
            return None

        current_container = selected_track
        current_path = []

        # Parcourir le chemin
        for i in range(0, len(chain_path), 2):
            if i + 1 >= len(chain_path):
                self.logger.warning(f"Chemin incomplet, besoin d'au moins un indice rack et un indice chaîne: {chain_path}")
                return None

            rack_idx = chain_path[i]
            chain_idx = chain_path[i + 1]

            # Vérifier si nous avons accès aux devices
            if not hasattr(current_container, 'devices'):
                self.logger.warning(f"Le conteneur n'a pas de devices à la position {current_path}")
                return None

            # Vérifier si l'index du rack est valide
            if rack_idx < 0 or rack_idx >= len(current_container.devices):
                self.logger.warning(f"Index de rack invalide: {rack_idx}")
                return None

            rack = current_container.devices[rack_idx]
            current_path.append(rack_idx)

            # Vérifier si le device est un rack
            if not hasattr(rack, 'chains'):
                self.logger.warning(f"Le device n'est pas un rack à la position {current_path}")
                return None

            # Vérifier si l'index de la chaîne est valide
            if chain_idx < 0 or chain_idx >= len(rack.chains):
                self.logger.warning(f"Index de chaîne invalide: {chain_idx}")
                return None

            chain = rack.chains[chain_idx]
            current_path.append(chain_idx)

            # Si nous avons plus de chemin à parcourir, la chaîne devient le nouveau conteneur
            if i + 2 < len(chain_path):
                current_container = chain
            else:
                return chain

        return None

    except Exception as e:
        self.logger.error(f"Erreur dans _get_chain_by_path: {str(e)}")
        return None


def _find_device_or_chain_track(self, device_or_chain):
    """
    Trouve la piste qui contient un device ou une chaîne donné
    """
    try:
        # Pour un device, remonter jusqu'à la piste
        if hasattr(device_or_chain, 'canonical_parent'):
            current = device_or_chain.canonical_parent
            
            # Remonter la hiérarchie jusqu'à trouver une piste
            while current and not hasattr(current, 'mixer_device'):
                if hasattr(current, 'canonical_parent'):
                    current = current.canonical_parent
                else:
                    break
            
            # Si on a trouvé quelque chose avec un mixer_device, c'est probablement une piste
            if current and hasattr(current, 'mixer_device'):
                return current
        
        # Fallback: chercher dans toutes les pistes
        all_tracks = list(self.song.tracks) + [self.song.master_track]
        for track in all_tracks:
            if hasattr(track, 'devices'):
                for device in track.devices:
                    if device == device_or_chain:
                        return track
                    
                    # Chercher dans les chaînes des racks
                    if hasattr(device, 'chains'):
                        for chain in device.chains:
                            if chain == device_or_chain:
                                return track
                            
                            # Chercher récursivement dans les devices des chaînes
                            if hasattr(chain, 'devices'):
                                for chain_device in chain.devices:
                                    if chain_device == device_or_chain:
                                        return track
        
        return None
        
    except Exception as e:
        self.logger.error(f"Erreur dans _find_device_or_chain_track: {str(e)}")
        return None


def _update_device_track_listeners(self, learn_slot: int, track_name: str, device_param, new_track) -> None:
    """Met à jour les listeners de piste quand un device ou une chaîne change de piste"""
    old_track = self.learn_slots[learn_slot]["track"]
    param_type = self.learn_slots[learn_slot]["param_type"]

    if param_type == 4:
        callbacks = self.learn_listeners.get(f"learn_{track_name}_device_callbacks", {})
    elif param_type in [5, 6, 9, 10]:  # Traiter tous les paramètres de chaîne de la même façon
        callbacks = self.learn_listeners.get(f"learn_{track_name}_chain_callbacks", {})

    # Supprimer les anciens listeners
    if old_track and callbacks:
        old_track.remove_name_listener(callbacks["track_name"])
        old_track.remove_color_listener(callbacks["track_color"])

    # Ajouter les listeners à la nouvelle piste
    new_track.add_name_listener(callbacks["track_name"])
    new_track.add_color_listener(callbacks["track_color"])

    # Mettre à jour la référence de la piste dans le slot
    self.learn_slots[learn_slot]["track"] = new_track

    self.send_learn_slot_reference(learn_slot)
