---
dfhsfgfgn: vbnbv
---

# Documentation du Mode Learn

## Introduction

Le Mode Learn est un système sophistiqué qui permet d'assigner des paramètres d'Ableton Live (volumes, panoramiques, effets, etc.) à des contrôleurs physiques ou virtuels. Cette documentation explique en détail son fonctionnement, son architecture et les flux de données impliqués.

## Architecture Générale

Le système fonctionne sur deux couches principales:

1. **Couche Go** - Gère l'interface utilisateur, la communication avec le matériel et le traitement des messages OSC
2. **Couche Python** - S'intègre à Ableton Live via l'API et communique les changements via OSC

### Composants Principaux

#### Côté Go

- **LiveLearnMode** : Structure principale qui gère le mode learn
- **LiveLearnDisplayManager** : Gère l'affichage et les mises à jour visuelles
- **LiveLearnModeState** : Stocke l'état actuel des slots, les assignations et les valeurs
- **Handlers spécialisés** : Traitent les différents types de messages OSC

#### Côté Python (AbletonOSC)

- **LearnModeHandler** : Gère globalement le mode learn dans Ableton
- **TrackLearn** : Configure les listeners pour les pistes
- **DeviceLearn** : Configure les listeners pour les devices et chaînes

## Fonctionnement des Slots

Le système est organisé autour du concept de "slots" (emplacements). Chaque slot peut contenir un paramètre assigné d'Ableton Live.

### Types de Paramètres

| Type | Description           | Constante Go           |
| ---- | --------------------- | ---------------------- |
| 1    | Volume de piste       | `ParamTypeVolume`      |
| 2    | Panoramique de piste  | `ParamTypePan`         |
| 3    | Envoi (Send)          | `ParamTypeSend`        |
| 4    | Paramètre de device   | `ParamTypeDevice`      |
| 5    | Volume de chaîne      | `ParamTypeChainVolume` |
| 6    | Panoramique de chaîne | `ParamTypeChainPan`    |
| 7    | Mute de piste         | `ParamTypeMute`        |
| 8    | Solo de piste         | `ParamTypeSolo`        |
| 9    | Mute de chaîne        | `ParamTypeChainMute`   |
| 10   | Solo de chaîne        | `ParamTypeChainSolo`   |

### Organisation des Slots

- Les slots sont numérotés de 0 à 31 (32 slots au total)
- Ils sont organisés en 4 pages de 8 slots chacune
- L'affichage est mis à jour via des messages formatés envoyés par le [`CommunicationManager`](LiveLearnMode/liveLearn_displayManager.go:1)

## Flux de Données

Il existe deux flux principaux pour l'assignation des paramètres:

### 1. Flux Manuel (via Go)

Ce flux permet d'assigner directement un paramètre à un slot sans interaction avec l'interface d'Ableton.

```mermaid
sequenceDiagram
participant U as Utilisateur
participant M as Mode (Volume/Device/etc.)
participant G as LiveModeManager
participant L as LiveLearnMode
U->>M: Sélectionne un paramètre
M->>G: Remplit learnData
G->>L: learnData disponible
U->>L: Touche un slot (handleTouchPressed)
L->>L: Simule la réponse OSC
L->>L: Met à jour le slot
```

**Étapes clés:**

1. Dans un autre mode, un paramètre est sélectionné et stocké dans [`types.LearnData`](LiveLearnMode/liveLearnMode.go:601)
2. L'utilisateur touche un slot en mode learn
3. Le système simule la réponse OSC pour l'assignation via [`SetLearnData()`](LiveLearnMode/liveLearnMode.go:601)
4. Le slot est mis à jour avec les informations du paramètre

### 2. Flux Automatique (via Python Listeners)

Ce flux capture les actions dans Ableton Live via les listeners Python.

```mermaid
sequenceDiagram
participant U as Utilisateur
participant A as Ableton Live
participant P as Python Listeners
participant O as OSC Server
participant G as Go (LiveLearnMode)
U->>G: Active le mode learn
G->>O: /live/learn/setup_track
G->>O: /live/learn/setup_device
O->>P: Configure listeners
U->>G: Sélectionne un slot (StartLearning)
U->>A: Manipule un paramètre
A->>P: Notification de changement
P->>O: Envoie message OSC
O->>G: Reçoit message OSC
G->>G: handleOscMessage
G->>G: Met à jour le slot
```

**Étapes clés:**

1. Le mode learn est activé, initialisant les listeners dans Ableton
2. L'utilisateur sélectionne un slot à assigner
3. L'utilisateur manipule un paramètre dans Ableton
4. Les listeners Python détectent ce changement
5. Un message OSC est envoyé à Go
6. Le slot est mis à jour avec les informations du paramètre

## Détail des Structures Principales

### LiveLearnMode (Go)

La structure principale qui gère le mode learn côté Go.

**Responsabilités:**

- Initialise et maintient l'état des slots
- Gère les handlers pour les entrées utilisateur
- Traite les messages OSC
- Met à jour l'interface utilisateur

**Méthodes clés:**

- [`Activate()`](LiveLearnMode/liveLearnMode.go:107) - Active le mode learn et tous les handlers
- [`StartLearning(slotIndex)`](LiveLearnMode/liveLearnMode.go:226) - Commence le processus d'apprentissage pour un slot
- [`sendParameterUpdate()`](LiveLearnMode/liveLearn_hardwareHandlers.go:210) - Envoie les mises à jour de paramètres
- [`handleEncoderChange()`](LiveLearnMode/liveLearn_hardwareHandlers.go:40) - Gère les changements d'encodeurs
- [`SetPage(page)`](LiveLearnMode/liveLearnMode.go:198) - Change la page de slots affichée

### LiveLearnDisplayManager (Go)

Gère l'affichage et les mises à jour visuelles du mode learn.

**Responsabilités:**

- Met à jour l'affichage des slots
- Gère les messages de communication vers l'interface
- Formate les valeurs pour l'affichage

**Méthodes clés:**

- [`UpdateSlot()`](LiveLearnMode/liveLearn_displayManager.go:58) - Met à jour l'affichage d'un slot
- [`UpdateAllSlots()`](LiveLearnMode/liveLearn_displayManager.go:198) - Met à jour tous les slots visibles
- [`UpdatePage()`](LiveLearnMode/liveLearn_displayManager.go:39) - Met à jour l'affichage de la page
- [`UpdateLearningStatus()`](LiveLearnMode/liveLearn_displayManager.go:249) - Met à jour le statut d'apprentissage

### Handlers spécialisés (Go)

Le système utilise plusieurs handlers spécialisés pour traiter différents types de messages :

- [`liveLearn_learningHandlers.go`](LiveLearnMode/liveLearn_learningHandlers.go:1) - Handlers pour l'apprentissage des paramètres
- [`liveLearn_trackParamHandlers.go`](LiveLearnMode/liveLearn_trackParamHandlers.go:1) - Handlers pour les paramètres de piste
- [`liveLearn_deviceParamHandlers.go`](LiveLearnMode/liveLearn_deviceParamHandlers.go:1) - Handlers pour les paramètres de device
- [`liveLearn_chainParamHandlers.go`](LiveLearnMode/liveLearn_chainParamHandlers.go:1) - Handlers pour les paramètres de chaîne
- [`liveLearn_hardwareHandlers.go`](LiveLearnMode/liveLearn_hardwareHandlers.go:1) - Handlers pour les événements hardware

### TrackLearn (Python)

Configure les listeners pour les pistes dans Ableton Live.

**Responsabilités:**

- Configure des listeners pour les propriétés des pistes (volume, pan, sends)
- Envoie les notifications de changements via OSC

**Méthodes clés:**

- `setup_tracks_listeners_for_learning()` - Configure tous les listeners de pistes
- `_setup_track_learn_listeners(track, learning_index)` - Configure les listeners pour une piste
- `_setup_mixer_property_listener(track, learning_index, prop)` - Configure un listener pour une propriété du mixer
- `_setup_send_listener(track, learning_index, send_index)` - Configure un listener pour un send

### DeviceLearn (Python)

Configure les listeners pour les devices et chaînes dans Ableton Live.

**Responsabilités:**

- Configure des listeners pour les paramètres des devices
- Configure des listeners pour les chaînes dans les racks
- Envoie les notifications de changements via OSC

**Méthodes clés:**

- `setup_learn_listeners()` - Configure tous les listeners de devices
- `_setup_device_learn_listeners(device, device_index)` - Configure les listeners pour un device
- `_setup_chains_learn_listeners(rack_device, rack_path)` - Configure les listeners pour les chaînes
- `handle_device_learning(params)` - Traite les demandes d'apprentissage de paramètre

## Interactions utilisateur

### Assignation d'un paramètre à un slot

1. L'utilisateur active le mode learn
2. L'utilisateur sélectionne un slot à assigner (pression sur un bouton)
3. L'utilisateur manipule un paramètre dans Ableton Live
4. Le paramètre est automatiquement assigné au slot

### Manipulation d'un paramètre assigné

1. L'utilisateur tourne l'encodeur correspondant au slot
2. La valeur du paramètre est mise à jour selon la direction et la vitesse
3. La mise à jour est envoyée à Ableton Live
4. L'affichage est actualisé avec la nouvelle valeur

### Effacement d'un slot

L'utilisateur peut effacer un slot en utilisant le message `/live/learn/del_slot`.

## Cas spécial: types.LearnData

Le système permet l'assignation directe via [`types.LearnData`](LiveLearnMode/liveLearnMode.go:601). Cette fonctionnalité permet d'assigner un paramètre préalablement sélectionné dans un autre mode.

**Structure de LearnData:**

```go
type LearnData struct {
    ParamType    int     // Type de paramètre (1-10)
    TrackIndex   int     // Index de la piste
    SendIndex    *int    // Index du send (pour type 3)
    DeviceIndex  *int    // Index du device (pour type 4)
    ParamIndex   *int    // Index du paramètre (pour type 4)
    ChainPath    []int   // Chemin de la chaîne (pour types 5-10)
    IsQuantized  *bool   // Paramètre quantifié ou non
    MinValue     *float64 // Valeur minimale
    MaxValue     *float64 // Valeur maximale
}
```

**Processus:**

1. Un autre mode (Volume, Device, etc.) remplit `LearnData` avec les informations du paramètre
2. L'utilisateur passe au mode learn et touche un slot
3. Le système détecte `learnData` via [`SetLearnData()`](LiveLearnMode/liveLearnMode.go:601) et simule une réponse OSC
4. Le paramètre est assigné sans interaction directe avec Ableton

## Notes importantes

1. Les pages de slots sont numérotées à partir de 1 (4 pages au total)
2. Les indices de slots sont basés sur 0 et convertis selon la page actuelle
3. Les valeurs sont normalisées entre 0 et 100 pour l'affichage
4. Certains paramètres (comme le volume) sont convertis en dB pour l'affichage
5. Le système utilise des goroutines et des mutex pour la gestion concurrente

## Dépannage

### Problèmes courants

1. **Les paramètres ne sont pas détectés:**

   - Vérifier que le mode learn est correctement activé
   - Vérifier que les messages OSC circulent correctement

2. **Les valeurs ne sont pas mises à jour:**

   - Vérifier que les listeners sont correctement configurés
   - S'assurer que le canal de communication est fonctionnel

3. **Assignation erronée:**

   - Vérifier les indices des pistes et des paramètres
   - S'assurer que le bon type de paramètre est assigné

## Spécificités techniques

### Système d'indexation

Le système utilise plusieurs conventions d'indexation à comprendre:

1. **Indices de slots**:

   - 0 à 31 en interne (stockage)
   - Organisés en pages de 8 slots (0-7 pour page 1, 8-15 pour page 2, etc.)
   - Affichés de 0 à 7 sur l'interface (relatifs à la page courante)
   - Constante [`SlotsPerPage = 8`](LiveLearnMode/liveLearn_types.go:16)

2. **Indices de pistes**:

   - En mode apprentissage: valeurs négatives (-1, -2, etc.) pour différencier du mode normal
   - L'indice -1 est souvent utilisé pour la piste Master
   - Conversion interne: `realTrackIndex = Math.abs(args[0]) - 1`

3. **Indices de devices**:

   - Valeur spéciale [`LockedDeviceIndex = -4`](LiveLearnMode/liveLearn_types.go:35) pour le device verrouillé

4. **Indices de chaînes**:

   - Stocké dans [`ChainPath []int`](LiveLearnMode/liveLearn_types.go:107) pour référence ultérieure
   - Valeur spéciale [`ChainTrackIndex = -2`](LiveLearnMode/liveLearn_types.go:36) pour les paramètres de chaîne

### Format des messages OSC

Les messages OSC suivent des formats spécifiques selon le type de paramètre:

- `/live/track/learning/volume`: `[trackIndex, value]`
- `/live/track/learning/panning`: `[trackIndex, value]`
- `/live/track/learning/sends`: `[trackIndex, sendIndex, value]`
- `/live/device/learning/parameter/value`: `[trackIndex, deviceIndex, paramIndex, value, valueString]`
- `/live/chain/learning/volume`: `[trackIndex, chainId, value]`
- `/live/chain/learning/panning`: `[trackIndex, chainId, value]`

## Détail des flux de données

### Flux manuel détaillé

Dans ce flux, l'assignation se fait sans interaction directe avec l'interface d'Ableton Live.

1. **Sélection du paramètre dans un autre mode:**

```go
learnData := &types.LearnData{
    ParamType:   types.ParamTypeVolume, // Ex: 1 pour volume
    TrackIndex:  2,                     // Ex: piste #3 (index 2)
}
modeManager.SetLearnData(learnData)
```

2. **Passage au mode Learn:**

   - L'utilisateur change de mode pour aller en mode Learn
   - [`LiveLearnMode`](LiveLearnMode/liveLearnMode.go:15) est activé mais `learnData` reste disponible

3. **Sélection d'un slot:**

   - L'utilisateur touche un slot (via [`handleTouchPressed`](LiveLearnMode/liveLearn_hardwareHandlers.go:245))
   - Le système vérifie si `learnData` contient des données

4. **Simulation des messages OSC:**

   - Le système crée un message OSC simulé selon le type de paramètre
   - Exemple pour un volume:

```go
// Dans handleTouchPressed
switch learnData.ParamType {
case types.ParamTypeVolume:
    m.handleTrackLearningVolume([]interface{}{
        learnData.TrackIndex + 1,
        0.0,
    })
}
```

5. **Finalisation:**
   - Le slot est mis à jour avec les informations du paramètre
   - `learnData` est réinitialisé
   - L'UI est mise à jour pour refléter l'assignation

### Flux automatique détaillé

Ce flux capture les manipulations réelles dans Ableton Live.

1. **Configuration des listeners:**

```go
// Côté Go
func (m *LiveLearnMode) sendStartListen() {
    m.BaseMode.Send(OscAddressLearnSetupTrack, []interface{}{})
    m.BaseMode.Send(OscAddressLearnSetupDevice, []interface{}{})
}
```

```python
# Côté Python
def setup_tracks_listeners_for_learning(self, _):
    # Configuration des listeners pour toutes les pistes
    for track_index, track in enumerate(all_tracks):
        learning_index = -(track_index + 1)
        self._setup_track_learn_listeners(track, learning_index)
```

2. **Déclenchement du mode apprentissage:**

```go
// L'utilisateur touche un slot et l'application passe en mode apprentissage
func (m *LiveLearnMode) StartLearning(slotIndex int) {
    m.state.Mutex.Lock()
    m.state.IsLearning = true
    m.state.ActiveSlot = &slotIndex
    m.state.Mutex.Unlock()

    m.displayManager.UpdateLearningStatus(true, slotIndex)
}
```

3. **Détection des changements dans Ableton:**

```python
# Exemples de callback Python
def callback():
    value = parameter.value
    self.osc_server.send("/live/track/learning/volume", (learning_index, value))
parameter.add_value_listener(callback)
```

4. **Transfert via OSC:**

   - Les changements détectés sont envoyés via OSC
   - Le serveur Go reçoit ces messages

5. **Traitement en Go:**

```go
// Dans handleTrackLearningVolume
func (m *LiveLearnMode) handleTrackLearningVolume(args []interface{}) {
    // Extraire les arguments
    value, _ := ParseOscFloat(args[1])
    slotIndex := m.getActiveSlotIndex()

    // Mettre à jour le slot
    m.UpdateSlotInfoOnly(slotIndex, map[string]interface{}{
        "type":          ParamTypeVolume,
        "value":         value,
        "min":           float64(0),
        "max":           float64(1),
        "parameterName": "volume",
    })

    // Envoyer confirmation à Live
    m.BaseMode.Send(OscAddressLearnSlot, []interface{}{slotIndex, ParamTypeVolume, realTrackIndex})

    // Mettre à jour l'affichage
    if isOnCurrentPage {
        volumePercent := int(math.Round(value * 100))
        volumeDb := m.volumeConverter.ToDb(value)
        m.commManager.SendMessage(
            fmt.Sprintf("ls,%d,volume,%d,%s dB", displaySlot, volumePercent, volumeDb),
            m.isActive,
        )
    }

    // Désactiver le mode apprentissage
    m.disableLearningMode()
}
```

## Interaction entre les modes

Les différents modes de l'application peuvent interagir avec le mode Learn via [`types.LearnData`](LiveLearnMode/liveLearnMode.go:601):

1. **Mode Volume:**
   - Sélection d'un fader de volume → remplit `learnData` avec `ParamTypeVolume`
2. **Mode Pan:**
   - Sélection d'un contrôle de panoramique → remplit `learnData` avec `ParamTypePan`
3. **Mode Device:**
   - Sélection d'un paramètre → remplit `learnData` avec `ParamTypeDevice`
4. **Mode Send:**
   - Sélection d'un envoi → remplit `learnData` avec `ParamTypeSend`
5. **Mode Chain:**
   - Sélection d'un paramètre de chaîne → remplit `learnData` avec `ParamTypeChain*`

Après avoir préparé `learnData`, un passage au mode Learn permet d'assigner ce paramètre à un slot rapidement, sans avoir à le manipuler dans Ableton.

## Spécificités de l'implémentation Go

### Gestion de la concurrence

Le système utilise des [`sync.RWMutex`](LiveLearnMode/liveLearn_types.go:132) pour protéger l'accès concurrent à l'état :

```go
// Lecture sécurisée
m.state.Mutex.RLock()
slot := m.state.Slots[slotIndex]
m.state.Mutex.RUnlock()

// Écriture sécurisée
m.state.Mutex.Lock()
m.state.IsLearning = true
m.state.Mutex.Unlock()
```

### Gestion des types OSC

Le système utilise des fonctions utilitaires pour parser les messages OSC :

```go
func ParseOscFloat(val interface{}) (float64, bool) {
    switch v := val.(type) {
    case float32:
        return float64(v), true
    case float64:
        return v, true
    case int:
        return float64(v), true
    default:
        return 0.0, false
    }
}
```

### Architecture modulaire

Le code est organisé en modules spécialisés :

- [`liveLearn_types.go`](LiveLearnMode/liveLearn_types.go:1) - Définitions des types et constantes
- [`liveLearn_registration.go`](LiveLearnMode/liveLearn_registration.go:1) - Enregistrement des handlers OSC
- [`liveLearn_displayManager.go`](LiveLearnMode/liveLearn_displayManager.go:1) - Gestion de l'affichage
- Handlers spécialisés par type de paramètre

Cette architecture modulaire améliore la maintenabilité et les performances par rapport à l'ancienne implémentation JavaScript.
