import logging
from .utils.parameter_throttler import ParameterThrottler

class TrackLearn:
    def __init__(self, track_handler):
        self.track_handler = track_handler
        self.manager = track_handler.manager
        self.osc_server = track_handler.osc_server
        self.song = track_handler.song
        self.logger = logging.getLogger("abletonosc")
        
        self.learn_listeners = {}
        self.parameter_throttler = ParameterThrottler()

    def setup_tracks_listeners_for_learning(self, _):
        """Configure les listeners pour toutes les pistes en mode apprentissage"""
        # Arrêter les listeners existants d'abord
        self.stop_tracks_listeners_for_learning(None)
        
        visible_tracks = [track for track in self.song.tracks if track.is_visible]
        return_tracks = list(self.song.return_tracks)
        all_tracks = visible_tracks + return_tracks + [self.song.master_track]

        for track_index, track in enumerate(all_tracks):
            # Utiliser l'opposé de l'index pour le learning mode
            learning_index = -(track_index + 1)
            self._setup_track_learn_listeners(track, learning_index)

        self.osc_server.send("/live/readyToListen")

    def _setup_track_learn_listeners(self, track, learning_index):
        """Configure les listeners pour une piste spécifique en mode apprentissage"""
        # Propriétés du mixer device
        self._setup_mixer_property_listener(track, learning_index, "volume")
        self._setup_mixer_property_listener(track, learning_index, "panning")

        # Listener pour les sends (sauf pour la master track)
        if track != self.song.master_track:
            send_count = len(track.mixer_device.sends)
            for send_index in range(send_count):
                self._setup_send_listener(track, learning_index, send_index)

        # Propriétés de base pour toutes les pistes
        track_properties = ["color", "name"]
        
        # Ajouter mute et solo seulement pour les pistes non-master
        if track != self.song.master_track:
            track_properties.extend(["mute", "solo"])
        
        for prop in track_properties:
            self._setup_track_property_listener(track, learning_index, prop)

    def _setup_mixer_property_listener(self, track, learning_index, prop):
        """Configure un listener pour une propriété du mixer"""
        parameter = getattr(track.mixer_device, prop)
        
        def callback():
            if not callback.is_active:  # Ajout du contrôle d'activité
                return
                
            param_id = f"learn_{track.name}_{prop}"
            value = parameter.value
            
            def send_value(val):
                self.osc_server.send(f"/live/track/learning/{prop}", (learning_index, val))
                
            self.parameter_throttler.update_parameter(param_id, value, send_value)
        
        callback.is_active = True  # Initialisation de l'état actif
        callback.parameter = parameter
        
        listener_key = f"track_{learning_index}_{prop}"
        if listener_key in self.learn_listeners:
            old_callback = self.learn_listeners[listener_key]
            old_callback.is_active = False
            try:
                if hasattr(parameter, 'value_has_listener') and parameter.value_has_listener(old_callback):
                    parameter.remove_value_listener(old_callback)
            except:
                self.logger.debug(f"Impossible de supprimer l'ancien listener pour {listener_key}")
        
        self.learn_listeners[listener_key] = callback
        parameter.add_value_listener(callback)

    def _setup_send_listener(self, track, learning_index, send_index):
        """Configure un listener pour un send"""
        send = track.mixer_device.sends[send_index]
        
        def callback():
            param_id = f"learn_{track.name}_send_{send_index}"
            value = send.value
            
            def send_value(val):
                self.osc_server.send("/live/track/learning/sends", (learning_index, send_index, val))
                
            self.parameter_throttler.update_parameter(param_id, value, send_value)
            
        callback.track = track
        callback.parameter = send
        callback.send_index = send_index  # Ajout d'une référence à l'index du send
        
        listener_key = f"track_{learning_index}_send_{send_index}"
        self.learn_listeners[listener_key] = callback
        send.add_value_listener(callback)

    def _setup_track_property_listener(self, track, learning_index, prop):
        """Configure un listener pour une propriété de piste"""
        def callback():
            param_id = f"learn_{track.name}_{prop}"
            value = getattr(track, prop)
            
            def send_value(val):
                # Conversion des booléens en entiers pour solo et mute
                if prop in ['solo', 'mute']:
                    val = 1 if val else 0
                self.osc_server.send(f"/live/track/learning/{prop}", (learning_index, val))
                
            self.parameter_throttler.update_parameter(param_id, value, send_value)
            
        callback.track = track  # Stocker une référence à la track
        callback.property = prop  # Stocker le nom de la propriété
        
        listener_key = f"track_{learning_index}_{prop}"
        self.learn_listeners[listener_key] = callback
        getattr(track, f"add_{prop}_listener")(callback)

    def stop_tracks_listeners_for_learning(self, _):
        """Arrête tous les listeners configurés pour le mode apprentissage"""
        initial_count = len(self.learn_listeners)
        self.logger.info(f"Début de stop_tracks_listeners_for_learning avec {initial_count} listeners")
        
        for key, callback in list(self.learn_listeners.items()):
            try:
                callback.is_active = False  # Désactivation du callback
                if hasattr(callback, 'parameter'):
                    param = callback.parameter
                    if hasattr(param, 'value_has_listener') and param.value_has_listener(callback):
                        param.remove_value_listener(callback)
                elif hasattr(callback, 'property'):
                    track = callback.track
                    prop = callback.property
                    if track is not None:
                        remove_method = getattr(track, f"remove_{prop}_listener")
                        remove_method(callback)
            except Exception as e:
                self.logger.debug(f"Erreur lors de la suppression du listener {key}: {str(e)}")
            
            del self.learn_listeners[key]
        
        self.logger.info(f"Nettoyage terminé. {initial_count} listeners supprimés")

    