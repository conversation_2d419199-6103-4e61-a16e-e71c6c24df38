from typing import <PERSON><PERSON>, Any
from .handler import AbletonOSCHandler
import logging
import Live

class BrowserHandler(AbletonOSCHandler):
    def __init__(self, manager):
        super().__init__(manager)
        self.class_identifier = "browser"
        self.logger = logging.getLogger("abletonosc")
        self.current_page = 0
        self.is_hotswap_mode = False
        try:
            self.browser = self.song.view.browse_mode
            self._init_browser_sections()
            self.logger.info("Browser initialized")
        except:
            self.logger.warning("Could not initialize browser")
            self.browser = None

    def _init_browser_sections(self):
        """Initialise les sections du browser de manière sécurisée"""
        self.sections = {}
        try:
            import Live
            application = Live.Application.get_application()
            # Nouvel ordre des sections
            self.sections = {
                'instruments': ('instruments', lambda: application.browser.instruments),
                'audio_effects': ('audio_effects', lambda: application.browser.audio_effects),
                'plugins': ('plugins', lambda: application.browser.plugins),
                'midi_effects': ('midi_effects', lambda: application.browser.midi_effects),
                'sounds': ('sounds', lambda: application.browser.sounds),
                'samples': ('samples', lambda: application.browser.samples),
                'drums': ('drums', lambda: application.browser.drums),
                'clips': ('clips', lambda: application.browser.clips),
                'user_library': ('user_library', lambda: application.browser.user_library),
            }
            self.logger.info("Browser sections initialized")
        except Exception as e:
            self.logger.warning(f"Could not initialize browser sections: {e}")

    def init_api(self):
        """Initialise tous les handlers OSC pour le browser"""
        
      
            
        self.osc_server.add_handler("/live/browser/load",
            lambda params: self._load_item(*[int(p) for p in params]))
                   
        self.osc_server.add_handler("/live/browser/hotswap/enable",
            lambda params: self._enable_hotswap(*[int(p) for p in params]))
            
        self.osc_server.add_handler("/live/browser/hotswap/disable",
            lambda _: self._disable_hotswap())
            
        self.osc_server.add_handler("/live/browser/hotswap/is_enabled",
            lambda _: self._is_hotswap_enabled())
        
            # Handler pour get_items_page avec gestion correcte des paramètres multiples
        self.osc_server.add_handler("/live/browser/get/items",
            lambda params: self._get_items(*[int(p) for p in params]))
  
        # Ajout du handler pour la prévisualisation avec chemin
        self.osc_server.add_handler("/live/browser/preview",
            lambda params: self._preview_item(*[int(p) for p in params]))

        # Ajout du handler pour inspecter le browser
        self.osc_server.add_handler("/live/browser/inspect",
            lambda _: self._inspect_browser())
     
            
        def create_browser_callback(func):
            def browser_callback(params: Tuple[Any]):
                try:
                    self.logger.info(f"Paramètres reçus dans browser_callback: {params}")
                    self.logger.info(f"Type des paramètres: {type(params)}")
                    # Les paramètres arrivent comme une tuple, on les passe directement
                    result = func(*[int(p) for p in params])
                    self.logger.info(f"Résultat du callback: {result}")
                    return result
                except Exception as e:
                    self.logger.error(f"Erreur dans browser_callback: {e}")
                    import traceback
                    self.logger.error(f"Traceback: {traceback.format_exc()}")
                    return ([], [], [], [], [], 0)
            return browser_callback

    

    def _get_items(self, page: int = 1, section_index: int = 0, *path_indices):
        """Retourne les éléments d'une section spécifique du browser avec pagination"""
        self.logger.info(f"Arguments reçus dans _get_items:")
        self.logger.info(f"page: {page}, section_index: {section_index}, path_indices: {path_indices}")
        
        try:
            if not self.browser:
                try:
                    import Live
                    self.browser = Live.Application.get_application().browser
                    self._init_browser_sections()
                except Exception as e:
                    self.logger.error(f"Échec de l'initialisation du browser: {e}")
                    return (1, 1, [], [], [], [], [], 0, [])

            # Obtenir l'élément courant et construire le chemin
            current_item = self._navigate_to_item(section_index, *path_indices)
            if not current_item:
                return (1, 1, [], [], [], [], [], 0, [])

            # Construire le chemin avec les noms
            path_names = []
            try:
                # Utiliser les sections définies dans _init_browser_sections
                section_name = list(self.sections.keys())[section_index]
                section_item = self.sections[section_name][1]()  # Appel de la lambda
                path_names.append(section_name)
                
                temp_item = section_item
                for idx in path_indices:
                    temp_item = list(temp_item.children)[idx]
                    path_names.append(temp_item.name if hasattr(temp_item, 'name') else str(temp_item))
            except Exception as e:
                self.logger.error(f"Erreur lors de la construction du chemin: {e}")

            # Récupérer tous les enfants
            all_children = []
            if hasattr(current_item, 'children'):
                all_children = list(current_item.children)

            # Calculer la pagination
            items_per_page = 16
            total_items = len(all_children)
            total_pages = max(1, (total_items + items_per_page - 1) // items_per_page)
            
            # Valider et ajuster la page demandée
            page = max(1, min(page, total_pages))
            
            # Calculer les indices de début et fin pour la page courante
            start_idx = (page - 1) * items_per_page
            end_idx = min(start_idx + items_per_page, total_items)
            
            # Initialiser les listes pour la page courante
            indices = []
            names = []
            types = []
            loadables = []
            sections_list = []

            # Remplir les listes avec les items de la page courante
            for idx, item in enumerate(all_children[start_idx:end_idx]):
                indices.append(idx)
                item_name = item.name if hasattr(item, 'name') else str(item)
                names.append(item_name)
                types.append(str(type(item).__name__))
                
                # Vérification loadable
                is_loadable = (hasattr(item, 'is_loadable') and item.is_loadable) or \
                             (hasattr(item, 'load') or hasattr(item, 'load_into_active_view'))
                loadables.append(1 if is_loadable else 0)
                
                # Un item est considéré comme une section si:
                # - c'est un dossier (is_folder)
                # - OU si c'est un device qui contient des presets (is_device et has children)
                is_folder = item.is_folder if hasattr(item, 'is_folder') else False
                is_device_with_presets = (hasattr(item, 'is_device') and item.is_device and 
                                         hasattr(item, 'children') and len(item.children) > 0)
                
                sections_list.append(1 if (is_folder or is_device_with_presets) else 0)

            return (page, total_pages, indices, names, types, loadables, sections_list, total_items, path_names)
                
        except Exception as e:
            self.logger.error(f"Erreur dans _get_items: {e}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            return (1, 1, [], [], [], [], [], 0, [])

    
    def _load_item(self, page: int, section_index: int = 0, *path_indices):
        """Charge l'élément spécifié en tenant compte de la pagination"""
        try:
            # Flag pour forcer l'insertion dans la chaîne sélectionnée
            force_selected_chain = True  # Pour tester
            
            # Envoyer message de début de chargement
            self.osc_server.send("/live/browser/loading/start", (True,))
            
            # Le dernier élément de path_indices est l'index relatif à la page
            relative_index = path_indices[-1]
            navigation_indices = path_indices[:-1]

            # On navigue d'abord jusqu'au dossier parent
            current_item = self._navigate_to_item(section_index, *navigation_indices)
            if not current_item or not hasattr(current_item, 'children'):
                self.osc_server.send("/live/browser/loading/error", ("Navigation impossible",))
                return (False,)

            # Configuration du mode d'insertion des devices
            selected_track = self.song.view.selected_track
            selected_device = selected_track.view.selected_device if hasattr(selected_track.view, 'selected_device') else None

            if hasattr(selected_track.view, 'device_insert_mode'):
                # Sauvegarder le mode actuel
                original_mode = selected_track.view.device_insert_mode
                # On utilise toujours selected_right pour le chargement initial
                selected_track.view.device_insert_mode = Live.Track.DeviceInsertMode.selected_right

            try:
                # Charger l'item...
                all_children = list(current_item.children)
                items_per_page = 16
                absolute_index = ((page - 1) * items_per_page) + relative_index
                
                if absolute_index >= len(all_children):
                    return (False,)
                    
                item_to_load = all_children[absolute_index]
                
                # Charger l'item selon le mode (hotswap ou normal)
                is_hotswap = self._is_hotswap_enabled()[0]
                
                if is_hotswap:
                    # Code du hotswap existant...
                    return (True,)
                
                else:
                    # Charger l'item normalement
                    if hasattr(item_to_load, 'load_into_active_view'):
                        item_to_load.load_into_active_view()
                    elif hasattr(item_to_load, 'browse_for_load'):
                        item_to_load.browse_for_load()
                    else:
                        self.browser.load_item(item_to_load)

                    # Si on force l'insertion dans la chaîne sélectionnée
                    if force_selected_chain and selected_device:
                        def move_to_selected_chain():
                            device = selected_track.view.selected_device
                            if device:
                                # Trouver la chaîne sélectionnée dans l'selected_device
                                for chain in selected_device.chains:
                                    # Si c'est la chaîne actuellement sélectionnée
                                    if chain == selected_device.view.selected_chain:
                                        # Déplacer le device dans cette chaîne
                                        # à la position 0 (début de la chaîne)
                                        self.song.move_device(device, chain, 0)
                                        break

                        # Attendre que le device soit prêt
                        device = selected_track.view.selected_device
                        if device and hasattr(device, "is_loading_chain"):
                            def on_parameters_ready():
                                if not device.is_loading_chain and device.parameters_are_ready:
                                    device.remove_parameters_are_ready_listener(on_parameters_ready)
                                    move_to_selected_chain()
                                    #self.manager.on_selected_track_or_device_changed()
                            
                            device.add_parameters_are_ready_listener(on_parameters_ready)
                            if not device.is_loading_chain and device.parameters_are_ready:
                                move_to_selected_chain()
                                #self.manager.on_selected_track_or_device_changed()
                        else:
                            move_to_selected_chain()
                            #self.manager.on_selected_track_or_device_changed()

                    return (True, item_to_load.name if hasattr(item_to_load, 'name') else str(item_to_load))

            finally:
                # Restaurer le mode d'insertion original
                if hasattr(selected_track.view, 'device_insert_mode'):
                    selected_track.view.device_insert_mode = original_mode
            
                self._disable_hotswap()
                
        except Exception as e:
            self.logger.error(f"Erreur lors du chargement: {e}")
            return (False, str(e))

    def _navigate_to_item(self, section_index: int = 0, *path_indices):
        """Méthode utilitaire pour naviguer jusqu'à un item spécifique"""
        try:
            if not self.browser:
                return None

            # Nouvel ordre des sections
            sections = [
                ('instruments', lambda: self.browser.instruments),
                ('audio_effects', lambda: self.browser.audio_effects),
                ('plugins', lambda: self.browser.plugins),
                ('midi_effects', lambda: self.browser.midi_effects),
                ('sounds', lambda: self.browser.sounds),
                ('samples', lambda: self.browser.samples),
                ('drums', lambda: self.browser.drums),
                ('clips', lambda: self.browser.clips),
                ('user_library', lambda: self.browser.user_library)
            ]

            if section_index < 0 or section_index >= len(sections):
                self.logger.error(f"Index de section invalide: {section_index}")
                return None

            section_name, section_getter = sections[section_index]
            self.logger.info(f"Navigation vers la section: {section_name}")
            
            try:
                current_item = section_getter()
                if not current_item:
                    self.logger.error(f"Section {section_name} non accessible")
                    return None
                    
                # Navigation dans l'arborescence selon les indices fournis
                for index in path_indices:
                    if hasattr(current_item, 'children'):
                        children = list(current_item.children)
                        if 0 <= index < len(children):
                            current_item = children[index]
                            self.logger.info(f"Navigation vers: {current_item.name if hasattr(current_item, 'name') else 'Sans nom'}")
                        else:
                            self.logger.error(f"Index invalide {index} pour {current_item.name if hasattr(current_item, 'name') else 'item'}")
                            return None
                    else:
                        self.logger.error("Navigation impossible: l'item n'a pas d'enfants")
                        return None

                return current_item
                
            except Exception as e:
                self.logger.error(f"Erreur lors de la navigation dans {section_name}: {e}")
                return None
                
        except Exception as e:
            self.logger.error(f"Erreur générale de navigation: {e}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            return None

    def _preview_item(self, page: int, section_index: int = 0, *path_indices):
        """Prévisualise l'élément spécifié en tenant compte de la pagination"""
        try:
            # Le dernier élément de path_indices est l'index relatif à la page
            relative_index = path_indices[-1]
            navigation_indices = path_indices[:-1]  # Tous les indices sauf le dernier

            # On navigue d'abord jusqu'au dossier parent
            current_item = self._navigate_to_item(section_index, *navigation_indices)
            if not current_item or not hasattr(current_item, 'children'):
                return (False, "Navigation impossible")

            all_children = list(current_item.children)
            items_per_page = 16
            
            # La page commence à 1, donc on soustrait 1 pour le calcul
            absolute_index = ((page - 1) * items_per_page) + relative_index
            
            if absolute_index >= len(all_children):
                return (False, "Index hors limites")
                
            item_to_preview = all_children[absolute_index]
            
            # Vérifier si l'item est prévisualisable
            if hasattr(item_to_preview, 'is_loadable') and item_to_preview.is_loadable:
                is_hotswap = self._is_hotswap_enabled()[0]
                
                try:
                    if is_hotswap:
                        hotswap_target = self.song.view.browse_mode.hotswap_target
                        if hotswap_target:
                            self.browser.preview_item(item_to_preview, hotswap_target)
                    else:
                        self.browser.preview_item(item_to_preview)
                    
                    return (True, f"Prévisualisation de {item_to_preview.name if hasattr(item_to_preview, 'name') else str(item_to_preview)}")
                    
                except Exception as e:
                    return (False, f"Erreur lors de la prévisualisation: {str(e)}")
            else:
                return (False, "Item non prévisualisable")

        except Exception as e:
            self.logger.error(f"Erreur lors de la prévisualisation: {e}")
            return (False, str(e))

    def _enable_hotswap(self, *path_indices):
        """Active le mode hotswap pour un device spécifié par son chemin
        path_indices: [rack_index, chain_index, ..., device_index]
        """
        try:
            # Obtenir le browser de l'application
            import Live
            browser = Live.Application.get_application().browser
            
            selected_track = self.song.view.selected_track
            if not selected_track:
                self.logger.error("Pas de piste sélectionnée")
                return (False,)

            # Navigation jusqu'au device cible
            current_container = selected_track.devices
            target_device = None

            # Si le chemin a une longueur impaire, le dernier élément est l'index du device
            if len(path_indices) % 2 == 1:
                for i in range(0, len(path_indices) - 1, 2):
                    rack_index = int(path_indices[i])
                    chain_index = int(path_indices[i + 1])

                    if rack_index >= len(current_container):
                        self.logger.error(f"Index de rack invalide: {rack_index}")
                        return (False,)

                    rack = current_container[rack_index]
                    if not hasattr(rack, 'chains'):
                        self.logger.error(f"Le device {rack.name} n'est pas un rack")
                        return (False,)

                    if chain_index >= len(rack.chains):
                        self.logger.error(f"Index de chaîne invalide: {chain_index}")
                        return (False,)

                    chain = rack.chains[chain_index]
                    current_container = chain.devices

                # Sélection du device final
                final_device_index = int(path_indices[-1])
                if final_device_index < len(current_container):
                    target_device = current_container[final_device_index]

            # Cas simple : device directement sur la piste
            elif len(path_indices) == 1:
                device_index = int(path_indices[0])
                if device_index < len(selected_track.devices):
                    target_device = selected_track.devices[device_index]

            if not target_device:
                self.logger.error("Device cible non trouvé")
                return (False,)

            # Vérifier si le device est déjà en mode hotswap
            if hasattr(browser, 'hotswap_target') and browser.hotswap_target == target_device:
                self.logger.info(f"Le device {target_device.name} est déjà en mode hotswap")
                return (True,)

            # Définir le hotswap target
            if hasattr(browser, 'hotswap_target'):
                try:
                    browser.hotswap_target = target_device
                    self.logger.info(f"Hotswap enabled pour {target_device.name}")
                    return (True,)
                except RuntimeError as e:
                    if "Couldn't set hotswap target" in str(e):
                        # Si le device est déjà en hotswap, on considère que c'est un succès
                        self.logger.info(f"Le device {target_device.name} semble déjà être en mode hotswap")
                        return (True,)
                    raise
            
        except Exception as e:
            self.logger.error(f"Error enabling hotswap: {e}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")
        return (False,)

    def _disable_hotswap(self):
        """Désactive le mode hotswap"""
        try:
            # On doit d'abord obtenir le browser de l'application
            import Live
            browser = Live.Application.get_application().browser
            
            # Désactiver le hotswap en mettant le target à None
            if hasattr(browser, 'hotswap_target'):
                browser.hotswap_target = None
                self.logger.info("Hotswap disabled")
                return (True,)
            
        except Exception as e:
            self.logger.error(f"Error disabling hotswap: {e}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")
        return (False,)


    def _is_hotswap_enabled(self):
        """Vérifie si le mode hotswap est actif"""
        try:
            if hasattr(self.song.view, 'browse_mode'):
                mode = self.song.view.browse_mode
                if hasattr(mode, 'is_hotswap_target'):
                    return (mode.is_hotswap_target,)
        except Exception as e:
            self.logger.error(f"Error checking hotswap status: {e}")
        return (False,)
