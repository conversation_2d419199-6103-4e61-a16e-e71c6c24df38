from functools import partial
from typing import Optional, Tuple
from .handler import AbletonOSCHandler

class ViewHandler(AbletonOSCHandler):
    def __init__(self, manager):
        super().__init__(manager)
        self.class_identifier = "view"

    def init_api(self):
        def get_selected_scene(params: Optional[Tuple] = ()):
            return (list(self.song.scenes).index(self.song.view.selected_scene),)

        def get_selected_track(params: Optional[Tuple] = ()):
            selected_track = self.song.view.selected_track
            
            visible_tracks = [track for track in self.song.tracks if track.is_visible]
            all_tracks = visible_tracks + list(self.song.return_tracks) + [self.song.master_track]
            total_tracks = len(all_tracks)

            if selected_track in visible_tracks:
                selected_track_index = visible_tracks.index(selected_track)
            elif selected_track in self.song.return_tracks:
                selected_track_index = len(visible_tracks) + list(self.song.return_tracks).index(selected_track)
            elif selected_track == self.song.master_track:
                selected_track_index = total_tracks - 1
            else:
                self.logger.warning("Selected track not found in any track list")
                return (-1,)

            return (selected_track_index,)

        def get_selected_clip(params: Optional[Tuple] = ()):
            return (get_selected_track()[0], get_selected_scene()[0])
        
        def get_selected_device(params: Optional[Tuple] = ()):
            selected_track = self.song.view.selected_track
            selected_device = selected_track.view.selected_device
            track_index = get_selected_track()[0]
            
            visible_devices = self.manager.get_visible_devices(selected_track)
            num_visible_devices = len(visible_devices)
            
            if selected_device is None:
                return (track_index, -1, num_visible_devices, 0)
            else:
                device_index = visible_devices.index(selected_device)
                num_parameters = len(selected_device.parameters) if selected_device else 0
                return (track_index, device_index, num_visible_devices, num_parameters)

        def set_selected_scene(params: Optional[Tuple] = ()):
            self.song.view.selected_scene = self.song.scenes[params[0]]

        def set_selected_track(params: Optional[Tuple] = ()):
            track_index = int(params[0])
            visible_tracks = [track for track in self.song.tracks if track.is_visible]
            all_tracks = visible_tracks + list(self.song.return_tracks) + [self.song.master_track]
            
            if track_index < len(all_tracks):
                self.song.view.selected_track = all_tracks[track_index]
                self.manager.selected_track_device_update()

        def set_selected_clip(params: Optional[Tuple] = ()):
            set_selected_track((params[0],))
            set_selected_scene((params[1],))

        def set_selected_device(params: Optional[Tuple] = ()):
            track_index = int(params[0])
            device_index = int(params[1])
            
            visible_tracks = [track for track in self.song.tracks if track.is_visible]
            all_tracks = visible_tracks + list(self.song.return_tracks) + [self.song.master_track]
            
            if track_index < len(all_tracks):
                selected_track = all_tracks[track_index]
                visible_devices = self.manager.get_visible_devices(selected_track)
                
                if device_index < len(visible_devices):
                    device = visible_devices[device_index]
                    self.song.view.select_device(device)
                    self.manager.on_selected_track_or_device_changed()
                else:
                    self.logger.warning(f"Invalid device index: {device_index}")
            else:
                self.logger.warning(f"Invalid track index: {track_index}")

        # Ajout des handlers OSC
        self.osc_server.add_handler("/live/view/get/selected_scene", get_selected_scene)
        self.osc_server.add_handler("/live/view/get/selected_track", get_selected_track)
        self.osc_server.add_handler("/live/view/get/selected_clip", get_selected_clip)
        self.osc_server.add_handler("/live/view/get/selected_device", get_selected_device)
        self.osc_server.add_handler("/live/view/set/selected_scene", set_selected_scene)
        self.osc_server.add_handler("/live/view/set/selected_track", set_selected_track)
        self.osc_server.add_handler("/live/view/set/selected_clip", set_selected_clip)
        self.osc_server.add_handler("/live/view/set/selected_device", set_selected_device)
        
        # Ajout des listeners
        self.osc_server.add_handler('/live/view/start_listen/selected_scene', 
            partial(self._start_listen, self.song.view, "selected_scene", getter=get_selected_scene))
        self.osc_server.add_handler('/live/view/start_listen/selected_track', 
            partial(self._start_listen, self.song.view, "selected_track", getter=get_selected_track))
        self.osc_server.add_handler('/live/view/stop_listen/selected_scene', 
            partial(self._stop_listen, self.song.view, "selected_scene"))
        self.osc_server.add_handler('/live/view/stop_listen/selected_track', 
            partial(self._stop_listen, self.song.view, "selected_track"))








        

