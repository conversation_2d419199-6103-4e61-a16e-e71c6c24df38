import time

class ParameterThrottler:
    def __init__(self, throttle_time_ms=20):
        """
        Initialise le throttler avec un temps de throttling en millisecondes
        
        Args:
            throttle_time_ms: Temps de throttling en millisecondes (par défaut: 20) il faudra passer à 50 en BLE
        """
        self.throttle_time_ms = throttle_time_ms
        self.last_update_times = {}
        self.pending_values = {}
        self.pending_callbacks = {}
        
    def should_send_update(self, param_id, current_time_ms):
        """Vérifie si une mise à jour devrait être envoyée en fonction du temps écoulé"""
        last_time = self.last_update_times.get(param_id, 0)
        return (current_time_ms - last_time) >= self.throttle_time_ms
        
    def update_parameter(self, param_id, value, send_callback):
        """
        Gère la mise à jour d'un paramètre avec throttling
        
        Args:
            param_id: Identifiant unique du paramètre
            value: Nouvelle valeur du paramètre
            send_callback: Fonction à appeler pour envoyer la valeur
        """
        current_time_ms = int(time.time() * 1000)
        
        # Stocker la dernière valeur reçue et son callback
        self.pending_values[param_id] = value
        self.pending_callbacks[param_id] = send_callback
        
        if self.should_send_update(param_id, current_time_ms):
            # Si assez de temps s'est écoulé, envoyer la valeur immédiatement
            send_callback(value)
            self.last_update_times[param_id] = current_time_ms
            del self.pending_values[param_id]
            del self.pending_callbacks[param_id]
            return True
            
        return False
        
    def send_pending_updates(self):
        """Envoie toutes les mises à jour en attente"""
        current_time_ms = int(time.time() * 1000)
        
        for param_id in list(self.pending_values.keys()):
            if self.should_send_update(param_id, current_time_ms):
                if param_id in self.pending_values and param_id in self.pending_callbacks:
                    value = self.pending_values[param_id]
                    callback = self.pending_callbacks[param_id]
                    callback(value)
                    self.last_update_times[param_id] = current_time_ms
                    del self.pending_values[param_id]
                    del self.pending_callbacks[param_id] 