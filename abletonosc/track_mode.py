from typing import Dict, Any, Callable
import logging
from .utils.parameter_throttler import ParameterThrottler

class TrackMode:
    def __init__(self, track_handler):
        self.track_handler = track_handler
        self.manager = track_handler.manager
        self.osc_server = track_handler.osc_server
        self.song = track_handler.song
        self.logger = logging.getLogger("abletonosc")

        self.trackMode_listeners = {}
        self.trackLockMode_listeners = {}
        self.parameter_throttler = ParameterThrottler()

    def send_relatives(self, track, mode_prefix: str):
        """
        Envoie les messages OSC concernant les relations entre pistes
        """
        try:
            self.logger.info(f"Analyse des relations pour la piste: {track.name}")
            self.logger.info(f"is_grouped: {track.is_grouped}, is_foldable: {track.is_foldable}")

            if track.is_grouped:
                # Obtenir la piste parente
                parent_track = track.group_track
                self.osc_server.send(f"/live/{mode_prefix}/parent", [parent_track.name])

                # Obtenir les pistes soeurs (même groupe)
                sister_tracks = [t.name for t in self.song.tracks if t.is_grouped and t.group_track == parent_track and t != track]
                self.osc_server.send(f"/live/{mode_prefix}/sisters", sister_tracks)

            if track.is_foldable:
                self.logger.info(f"Recherche des pistes filles pour: {track.name}")
                # Obtenir les pistes filles
                daughter_tracks = []
                for t in self.song.tracks:
                    self.logger.info(f"Analyse piste: {t.name}, is_grouped: {t.is_grouped}")
                    if t.is_grouped:
                        self.logger.info(f"Group track de {t.name}: {t.group_track.name}")
                        if t.group_track == track:
                            daughter_tracks.append(t.name)

                self.logger.info(f"Pistes filles trouvées: {daughter_tracks}")
                if daughter_tracks:
                    self.logger.info(f"Envoi des pistes filles: {daughter_tracks}")
                    self.osc_server.send(f"/live/{mode_prefix}/daughters", daughter_tracks)
                else:
                    self.logger.info("Aucune piste fille trouvée")

        except Exception as e:
            self.logger.error(f"Erreur dans send_relatives: {str(e)}")

    def _setup_track_listeners(self, track, mode_prefix: str, listeners_dict: Dict):
        """
        Configuration générique des listeners pour une piste
        mode_prefix: 'trackMode' ou 'trackLockMode'
        listeners_dict: self.trackMode_listeners ou self.trackLockMode_listeners
        """
        # Calculer l'index de la piste
        visible_tracks = [t for t in self.song.tracks if t.is_visible]
        return_tracks = list(self.song.return_tracks)

        if track in visible_tracks:
            track_index = visible_tracks.index(track)
        elif track in return_tracks:
            track_index = len(visible_tracks) + return_tracks.index(track)
        else:  # master track
            track_index = len(visible_tracks) + len(return_tracks)

        # Vérifier si la piste peut être armée (uniquement les pistes MIDI et audio normales)
        can_be_armed = track not in return_tracks and track != self.song.master_track

        # Envoyer le message initial groupé
        try:
            initial_values = {
                'is_foldable': track.is_foldable,
                'fold_state': track.fold_state if track.is_foldable else False,
                'is_grouped': track.is_grouped,
                'mute': track.mute if track != self.song.master_track else False,
                'muted_via_solo': track.muted_via_solo if track != self.song.master_track else False,
                'solo': track.solo if track != self.song.master_track else False,
                'color': track.color,
                'name': track.name,
                'volume': track.mixer_device.volume.value,
                'panning': track.mixer_device.panning.value,
                'arm': 2  # Par défaut, non applicable
            }

            # Définir l'état d'armement :
            # 0 = non armé, 1 = armé, 2 = non applicable (return/master/group)
            if can_be_armed and not track.is_foldable and track not in return_tracks:
                try:
                    initial_values['arm'] = 1 if track.arm else 0
                except Exception as e:
                    self.logger.debug(f"Could not get arm state for track {track.name}: {str(e)}")
                    initial_values['arm'] = 2

            self.osc_server.send(f"/live/start_listen_{mode_prefix}", (
                track_index,
                initial_values['is_foldable'],
                initial_values['fold_state'],
                initial_values['is_grouped'],
                initial_values['mute'],
                initial_values['muted_via_solo'],
                initial_values['solo'],
                initial_values['arm'],
                initial_values['color'],
                initial_values['name'],
                initial_values['volume'],
                initial_values['panning']
            ))
            self.osc_server.send(f"/live/{mode_prefix}/get/panning", (track_index, initial_values['panning']))

            # Envoyer les informations sur les relations entre pistes
            self.send_relatives(track, mode_prefix)

        except Exception as e:
            self.logger.error(f"Error sending initial values: {str(e)}")

        # Créer le callback générique
        def create_callback(property_name: str, value_getter: Callable) -> Callable:
            def callback():
                value = value_getter()
                param_id = f"{mode_prefix}_{track.name}_{property_name}"

                # Traitement spécial pour la propriété arm
                if property_name == "arm":
                    # Pour les pistes return ou master, envoyer 2
                    if track in self.song.return_tracks or track == self.song.master_track or track.is_foldable:
                        arm_value = 2
                    else:
                        # Pour les autres pistes, envoyer 0 ou 1 en fonction de l'état
                        arm_value = 1 if value else 0

                    def send_value(val):
                        self.logger.info(f"{property_name} changed for track {track.name}: {arm_value}")
                        self.osc_server.send(f"/live/{mode_prefix}/get/{property_name}", (track_index, arm_value))

                    self.parameter_throttler.update_parameter(param_id, value, send_value)
                elif property_name in ["mute", "muted_via_solo", "solo"]:
                    # Convertir le booléen en entier (0 ou 1)
                    int_value = 1 if value else 0
                    def send_value(val):
                        self.logger.info(f"{property_name} changed for track {track.name}: {int_value}")
                        self.osc_server.send(f"/live/{mode_prefix}/get/{property_name}", (track_index, int_value))
                    self.parameter_throttler.update_parameter(param_id, value, send_value)
                else:
                    # Comportement normal pour les autres propriétés
                    def send_value(val):
                        self.logger.info(f"{property_name} changed for track {track.name}: {val}")
                        self.osc_server.send(f"/live/{mode_prefix}/get/{property_name}", (track_index, val))

                    self.parameter_throttler.update_parameter(param_id, value, send_value)

            callback.track = track
            return callback

        # Propriétés vraiment communes à toutes les pistes (y compris master)
        properties = {
            "volume": lambda: track.mixer_device.volume.value,
            "panning": lambda: track.mixer_device.panning.value,
            "name": lambda: track.name,
            "color": lambda: track.color
        }

        # Ajouter les propriétés spécifiques selon le type de piste
        if track == self.song.master_track:
            # La piste master n'a que les propriétés de base
            pass
        else:
            # Toutes les pistes sauf master ont mute, solo et muted_via_solo
            properties.update({
                "mute": lambda: track.mute,
                "solo": lambda: track.solo,
                "muted_via_solo": lambda: track.muted_via_solo
            })

            # Ajouter l'armement uniquement pour les pistes MIDI et Audio (pas pour les returns ni les groupes)
            if can_be_armed and not track.is_foldable and track not in return_tracks:
                properties["arm"] = lambda: track.arm

        # Configurer les listeners pour chaque propriété
        for prop, getter in properties.items():
            callback = create_callback(prop, getter)
            listeners_dict[prop] = callback

            if prop in ["volume", "panning"]:
                getattr(track.mixer_device, prop).add_value_listener(callback)
            else:
                try:
                    getattr(track, f"add_{prop}_listener")(callback)
                except Exception as e:
                    self.logger.error(f"Erreur lors de l'ajout du listener {prop} pour la piste {track.name}: {str(e)}")

        # Configurer les sends si ce n'est pas la master track
        if track != self.song.master_track:
            self._setup_send_listeners(track, track_index, mode_prefix, listeners_dict)

    def _setup_send_listeners(self, track, track_index: int, mode_prefix: str, listeners_dict: Dict):
        for i, send in enumerate(track.mixer_device.sends):
            # Envoyer la valeur initiale des sends
            initial_send_value = send.value
            try:
                self.osc_server.send(f"/live/{mode_prefix}/get/sends", (track_index, i, initial_send_value))
            except Exception as e:
                self.logger.error(f"Failed to send send {i} message: {str(e)}")

            def create_send_callback(send_index):
                def callback():
                    value = track.mixer_device.sends[send_index].value
                    param_id = f"{mode_prefix}_{track.name}_send_{send_index}"

                    def send_value(val):
                        self.logger.info(f"Send {send_index} changed for track {track.name}: {val}")
                        self.osc_server.send(f"/live/{mode_prefix}/get/sends", (track_index, send_index, val))

                    self.parameter_throttler.update_parameter(param_id, value, send_value)

                callback.track = track
                return callback

            send_callback = create_send_callback(i)
            listeners_dict[f"send_{i}"] = send_callback
            send.add_value_listener(send_callback)

    def _stop_track_listeners(self, listeners_dict: Dict):
        """Méthode générique pour arrêter les listeners"""
        if not listeners_dict:
            return

        # Obtenir la liste actuelle des pistes valides
        all_current_tracks = set(self.song.tracks) | set(self.song.return_tracks) | {self.song.master_track}

        items_to_process = list(listeners_dict.items())
        listeners_dict.clear()  # Vider le dictionnaire immédiatement pour éviter des états incohérents

        for key, callback in items_to_process:
            if not hasattr(callback, 'track'):
                self.logger.debug(f"Callback for key '{key}' has no track attribute, skipping.")
                continue

            track = callback.track

            # Vérifier si la piste associée au callback est toujours valide
            if track not in all_current_tracks:
                # Ne pas tenter d'accéder à track.name ici car track est invalide
                self.logger.debug(f"Track associated with listener '{key}' no longer exists, skipping removal.")
                continue

            # Si la piste est valide, tenter de supprimer le listener
            try:
                if isinstance(key, str):
                    if key == "volume":
                        track.mixer_device.volume.remove_value_listener(callback)
                    elif key == "panning":
                        track.mixer_device.panning.remove_value_listener(callback)
                    elif key.startswith("send_"):
                        send_index_str = key.split("_")[-1]
                        if send_index_str.isdigit():
                             send_index = int(send_index_str)
                             if 0 <= send_index < len(track.mixer_device.sends):
                                 send_device = track.mixer_device.sends[send_index]
                                 # Vérifier si l'objet send est toujours valide
                                 try:
                                     _ = send_device.value # Tenter une opération simple
                                     send_device.remove_value_listener(callback)
                                 except Exception as send_e:
                                     self.logger.debug(f"Send object for {key} on track {track.name} seems invalid, skipping removal: {send_e}")
                             else:
                                 self.logger.debug(f"Send index {send_index} out of range for track {track.name}")
                        else:
                            self.logger.debug(f"Could not parse send index from key: {key}")
                    else:
                        # Pour les autres propriétés (name, color, mute, solo, muted_via_solo, arm)
                        remove_listener_func = getattr(track, f"remove_{key}_listener", None)
                        if remove_listener_func:
                            remove_listener_func(callback)
                        else:
                            self.logger.debug(f"Could not find remove listener function for '{key}' on track {track.name}")
            except Exception as e:
                self.logger.debug(f"Error removing listener '{key}' for track '{track.name}': {str(e)}")

    def start_selected_track_listeners(self, *args):
        # D'abord arrêter les listeners existants
        self.stop_selected_track_listeners()

        self.trackMode_listeners.clear()
        selected_track = self.song.view.selected_track
        self._setup_track_listeners(selected_track, "trackMode", self.trackMode_listeners)

    def start_locked_track_listeners(self, *args):
        # D'abord arrêter les listeners existants
        self.stop_locked_track_listeners()

        if not self.manager.trackLock or not self.manager.lockedTrack:
            return
        self.trackLockMode_listeners = {}
        self._setup_track_listeners(self.manager.lockedTrack, "trackLockMode", self.trackLockMode_listeners)

    def stop_selected_track_listeners(self, *args):
        self._stop_track_listeners(self.trackMode_listeners)

    def stop_locked_track_listeners(self, *args):
        self._stop_track_listeners(self.trackLockMode_listeners)

    def trackParent_handler(self, value):
        """
        Si la piste actuelle est dans un groupe, sélectionne la piste parente.
        Args:
            value[0]: 0 pour utiliser selected_track, 1 pour utiliser locked_track
        """
        try:
            use_locked = int(value[0]) == 1
            track_to_check = self.manager.lockedTrack if use_locked else self.song.view.selected_track

            if not track_to_check:
                self.logger.warning("No track to process.")
                return

            self.logger.info(f"Processing track: {track_to_check.name}, is_grouped: {track_to_check.is_grouped}")
            if track_to_check.is_grouped:
                parent_track = track_to_check.group_track
                if parent_track:
                    self.logger.info(f"Selecting parent track: {parent_track.name}")
                    self.song.view.selected_track = parent_track
                    
                    if use_locked:
                        self.logger.info("Using locked track, deactivating lock.")
                        self.trackLock_handler(['0'])
                else:
                    self.logger.warning(f"Track {track_to_check.name} is grouped but no parent track found.")
            else:
                self.logger.info(f"Track {track_to_check.name} is not grouped. No action taken.")

        except Exception as e:
            self.logger.error(f"Error in trackParent_handler: {str(e)}")

    def trackDaughter_handler(self, value):
        """
        Si la piste actuelle est dépliable et a des pistes filles,
        sélectionne la piste fille correspondant à l'index fourni.
        Args:
            value[0]: 0 pour utiliser selected_track, 1 pour utiliser locked_track
            value[1]: index de la piste fille à sélectionner
        """
        try:
            use_locked = int(value[0]) == 1
            track_to_check = self.manager.lockedTrack if use_locked else self.song.view.selected_track

            if not track_to_check:
                self.logger.warning("No track to process for daughter navigation.")
                return

            self.logger.info(f"Processing track for daughters: {track_to_check.name}, is_foldable: {track_to_check.is_foldable}")

            if track_to_check.is_foldable:
                daughter_tracks = []
                for t in self.song.tracks:
                    if t.is_grouped and t.group_track == track_to_check:
                        daughter_tracks.append(t)

                if not daughter_tracks:
                    self.logger.info(f"Track {track_to_check.name} is foldable but has no daughter tracks.")
                    return

                try:
                    daughter_index = int(value[1])
                except (ValueError, IndexError):
                    self.logger.error(f"Invalid daughter index format or missing index in value: {value}")
                    return

                if 0 <= daughter_index < len(daughter_tracks):
                    daughter_to_select = daughter_tracks[daughter_index]
                    self.logger.info(f"Selecting daughter track: {daughter_to_select.name} at index {daughter_index}")
                    self.song.view.selected_track = daughter_to_select

                    if use_locked:
                        self.logger.info("Using locked track, deactivating lock after daughter selection.")
                        self.trackLock_handler(['0'])
                else:
                    self.logger.warning(f"Daughter track index {daughter_index} is out of range for track {track_to_check.name}. Available daughters: {len(daughter_tracks)}")
            else:
                self.logger.info(f"Track {track_to_check.name} is not foldable. No action taken for daughter navigation.")

        except Exception as e:
            self.logger.error(f"Error in trackDaughter_handler: {str(e)}")

    def trackSister_handler(self, value):
        """
        Si la piste actuelle est groupée, sélectionne une piste sœur
        (même groupe, excluant la piste actuelle) basée sur l'index fourni.
        Args:
            value[0]: 0 pour utiliser selected_track, 1 pour utiliser locked_track
            value[1]: index de la piste sœur à sélectionner
        """
        try:
            use_locked = int(value[0]) == 1
            track_to_check = self.manager.lockedTrack if use_locked else self.song.view.selected_track

            if not track_to_check:
                self.logger.warning("No track to process for sister navigation.")
                return

            self.logger.info(f"Processing track for sisters: {track_to_check.name}, is_grouped: {track_to_check.is_grouped}")

            if track_to_check.is_grouped:
                parent_track = track_to_check.group_track
                if not parent_track:
                    self.logger.warning(f"Track {track_to_check.name} is grouped but no parent track found.")
                    return

                sister_tracks = []
                for t in self.song.tracks:
                    if t.is_grouped and t.group_track == parent_track and t != track_to_check:
                        sister_tracks.append(t)

                if not sister_tracks:
                    self.logger.info(f"Track {track_to_check.name} is in group '{parent_track.name}' but has no sister tracks.")
                    return

                try:
                    sister_index = int(value[1])
                except (ValueError, IndexError):
                    self.logger.error(f"Invalid sister index format or missing index in value: {value}")
                    return

                if 0 <= sister_index < len(sister_tracks):
                    sister_to_select = sister_tracks[sister_index]
                    self.logger.info(f"Selecting sister track: {sister_to_select.name} at index {sister_index} from group '{parent_track.name}'")
                    self.song.view.selected_track = sister_to_select

                    if use_locked:
                        self.logger.info("Using locked track, deactivating lock after sister selection.")
                        self.trackLock_handler(['0'])
                else:
                    self.logger.warning(f"Sister track index {sister_index} is out of range for track {track_to_check.name} in group '{parent_track.name}'. Available sisters: {len(sister_tracks)}")
            else:
                self.logger.info(f"Track {track_to_check.name} is not grouped. No action taken for sister navigation.")

        except Exception as e:
            self.logger.error(f"Error in trackSister_handler: {str(e)}")

    def trackLock_handler(self, value):
        """
        Gère le verrouillage/déverrouillage d'une piste
        """
        try:
            if value[0] == '1':
                self.manager.trackLock = True
                self.manager.lockedTrack = self.song.view.selected_track

                if self.manager.lockedTrack:
                    self.osc_server.send("/live/track_lock")
                    self.logger.info(f"Locked track found: {self.manager.lockedTrack.name}")
                    self.start_locked_track_listeners()
                    self.stop_selected_track_listeners()
                else:
                    self.osc_server.send("/live/track_unlock")
                    self.logger.info("No locked track found")
                    self.manager.trackLock = False
                    self.start_selected_track_listeners()
                    self.stop_locked_track_listeners()
                    
                    self.manager.num_tracks_update()

            else:
                self.osc_server.send("/live/track_unlock")
                self.manager.trackLock = False
                self.manager.lockedTrack = None
                self.start_selected_track_listeners()
                self.stop_locked_track_listeners()
                self.manager.num_tracks_update()

        except Exception as e:
            self.logger.error(f"Erreur dans trackLock_handler: {str(e)}")
            self.manager.trackLock = False
            self.osc_server.send("/live/track_unlock")

    def verify_lock_track_validity(self):
        """
        Vérifie si la piste verrouillée existe toujours et est valide.
        Si non, déverrouille la piste.
        """
        if self.manager.trackLock:
            all_tracks = list(self.song.tracks) + list(self.song.return_tracks) + [self.song.master_track]
            if self.manager.lockedTrack in all_tracks:
                self.logger.info(f"lockedTrack is {self.manager.lockedTrack.name}")
            else:
                self.logger.info("lockedTrack is None or no longer valid")
                self.manager.lockedTrack = None
                self.manager.trackLock = False
                self.logger.info("Track unlocked due to deletion or invalidity")
                self.stop_locked_track_listeners()
                self.osc_server.send("/live/track_unlock")