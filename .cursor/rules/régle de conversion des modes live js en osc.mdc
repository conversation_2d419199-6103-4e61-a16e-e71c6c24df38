---
description: 
globs: 
alwaysApply: false
---
L'objectif de toute cette repo est de passer un projet js en go


- Définir des structures claires pour l'état :
  - `ModeState` : champs typés, éviter `interface{}` sauf pour OSC.
  - Structures spécifiques pour les items.

- Constructeur :
  - C<PERSON>er une fonction `NewLiveXXXMode(trackManager, commManager)` qui initialise :
    - `BaseMode`
    - `trackManager`, `commManager`
    - `state`
  - Enregistrer les handlers OSC directement dans le constructeur avec `RegisterHandler`.

- Initialize :
  - Utiliser `Initialize(service communication.OscService)` pour :
    - Appeler `BaseMode.Initialize`
    - Faire les initialisations post-service
    - Envoyer les messages initiaux si besoin.

- Gestion OSC :
  - Définir toutes les adresses OSC en `const`.
  - Créer des handlers :
    - Vérifier le nombre d'arguments.
    - Faire une conversion de type sûre (`int32`, `float64`, etc.).
    - Mettre à jour l'état et envoyer les réponses série.

- Communication série :
  - Utiliser `fmt.Sprintf` pour créer des messages courts et clairs.
  - G<PERSON>rer les erreurs d'envoi proprement (`if err != nil` avec log).

- Gestion des événements matériels :
  - Implémenter `HandleHardwareEvent` avec switch sur `event.Type` (`touch`, `button`, `encoder`).
  - Déléguer à des handlers spécifiques (`handleTouchEvent`, `handleButtonEvent`, etc.).
  - Utiliser `Send` pour envoyer des messages OSC après action.

- Conversion JS → Go :
  - `Array` → `[]interface{}` ou `[]Type`
  - `Object` → `struct`
  - `boolean` → `bool`
  - `number` → `int`, `int32`, `float64`
  - `null/undefined` → `nil`

- Conversion OSC :
  - Utiliser `int32` pour les entiers.
  - Utiliser `[]interface{}` pour les tableaux.
  - Utiliser `string` pour les chaînes.

- Gestion de l'état :
  - Ajouter `sync.RWMutex` si accès concurrent.
  - Utiliser `mu.Lock()` / `mu.RUnlock()` autour des accès à l'état.

- Logging et validation :
  - Utiliser des logs structurés (`log.Printf` avec formatage clair).
  - Toujours valider les données entrantes avant traitement.

