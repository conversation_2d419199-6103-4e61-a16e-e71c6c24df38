---
description: 
globs: 
alwaysApply: false
---
# Événements Matériels (Hardware)

Le projet utilise un système d'événements typés pour gérer les interactions avec le matériel ESP32.

## Format des Messages Série

Les messages provenant de l'ESP32 suivent ces formats :

- Événements tactiles : `t,<touchType>,<index>`
  - `touchType` : code de deux lettres indiquant le type de toucher (ex: "bs", "pr", "rl", "mv")
  - `index` : identifiant numérique de la zone touchée

- Événements bouton : `b,<index>`
  - `index` : identifiant numérique du bouton

- Événements encodeur : `e,<index>,<value>`
  - `index` : identifiant numérique de l'encodeur
  - `value` : valeur numérique (positive ou négative) de l'encodeur

## Structures d'Événements

Ces messages sont convertis en structures typées dans [Communication/hardwareManager.go](mdc:Communication/hardwareManager.go) :

- `TouchEvent` : Événements tactiles
- `ButtonEvent` : Événements bouton
- `EncoderEvent` : Événements encodeur
- `HardwareEvent` : Structure englobante contenant le type et la référence à l'événement spécifique

## Réception des Événements

Pour qu'un mode reçoive des événements matériels :

1. Il doit implémenter l'interface `HardwareEventReceiver` définie dans [Communication/hardwareManager.go](mdc:Communication/hardwareManager.go)
2. La méthode `HandleHardwareEvent` doit être implémentée comme dans [Live/liveMainMode.go](mdc:Live/liveMainMode.go)
3. Le mode doit être défini comme mode actif auprès du `HardwareManager`
