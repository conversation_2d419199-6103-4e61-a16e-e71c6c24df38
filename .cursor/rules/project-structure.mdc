---
description: 
globs: 
alwaysApply: false
---
# Structure du Projet OSCBridgeGo

Ce projet est une application Go qui sert de pont entre des messages OSC (Open Sound Control) et un contrôleur matériel (ESP32).

## Points d'entrée

- [main.go](mdc:main.go) : Point d'entrée principal de l'application qui initialise les gestionnaires et configure les modes.

## Principaux packages

### Communication

Le package Communication gère tous les aspects de communication OSC et série :

- [Communication/oscService.go](mdc:Communication/oscService.go) : Interface définissant les services OSC
- [Communication/communicationManager.go](mdc:Communication/communicationManager.go) : Gestionnaire principal de la communication
- [Communication/hardwareManager.go](mdc:Communication/hardwareManager.go) : Gestion des événements matériels provenant de l'ESP32
- [Communication/oscMode.go](mdc:Communication/oscMode.go) : Définition de l'interface OscMode et implémentation BaseMode
- [Communication/eventEmitter.go](mdc:Communication/eventEmitter.go) : Système d'événements pour la communication interne

### Live

Le package Live contient tous les modes spécifiques à Ableton Live :

- [Live/liveBrowserMode.go](mdc:Live/liveBrowserMode.go) : Mode pour la navigation dans le browser de Live
- [Live/liveMainMode.go](mdc:Live/liveMainMode.go) : Mode principal pour les fonctions générales de Live
- [Live/liveModeManager.go](mdc:Live/liveModeManager.go) : Gestionnaire des différents modes disponibles

## Flux de données

1. Les messages OSC arrivent via le RawOscBridge
2. Les messages matériels (ESP32) arrivent via le CommunicationManager
3. Le HardwareManager analyse les messages matériels et crée des événements typés
4. Les événements sont transmis au mode actif
5. Le mode actif envoie des commandes OSC à Live en réponse
