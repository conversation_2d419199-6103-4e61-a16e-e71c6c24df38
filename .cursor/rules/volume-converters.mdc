---
description: 
globs: 
alwaysApply: false
---
# Règles pour les Convertisseurs de Volume

## Structure

Les convertisseurs de volume sont situés dans le package `Live/utils` et comprennent deux types principaux :

1. `VolumeConverter` : Pour la conversion générale de volume en dB
2. `VolumeSendConverter` : Pour la conversion spécifique des volumes lors de l'envoi

## Accès aux Convertisseurs

Les convertisseurs sont accessibles via le `LiveModeManager` qui les initialise et les met à disposition :

```go
// Obtenir les convertisseurs
volumeConverter := manager.GetVolumeConverter()
volumeSendConverter := manager.GetVolumeSendConverter()
```

## Utilisation dans les Modes

Dans chaque mode (LiveTrackMode, LiveLearnMode, etc.), les convertisseurs sont accessibles via le manager :

```go
// Exemple d'utilisation dans un mode
func (m *LiveTrackMode) ConvertVolume(volume float64) string {
    // Conversion normale
    dbValue := m.manager.GetVolumeConverter().ToDb(volume)
    
    // Conversion pour l'envoi
    sendDbValue := m.manager.GetVolumeSendConverter().ToDb(volume)
    return dbValue // ou sendDbValue selon le besoin
}
```

## Plages de Valeurs

### VolumeConverter
- Volume d'entrée : 0.0 à 1.0
- Seuils : 
  - Bas : 0.12
  - Haut : 0.4
- Retours spéciaux :
  - Volume ≤ 0 : "- inf"
  - Volume ≥ 1 : "6.0"

### VolumeSendConverter
- Volume d'entrée : 0.0 à 1.0
- Seuil haut : 0.4
- Retours spéciaux :
  - Volume ≤ 0 : "- inf"
  - Volume ≥ 1 : "0.0"

## Formules de Conversion

### VolumeConverter
1. Volume ≤ 0.12 : `180*x - 66.6`
2. Volume ≥ 0.4 : `40*x - 34`
3. Entre les deux : Interpolation linéaire basée sur la table de correspondance

### VolumeSendConverter
1. Volume > 0.4 : `40*x - 40`
2. Volume ≤ 0.4 : Interpolation linéaire basée sur la table de correspondance

## Thread Safety

Les convertisseurs sont thread-safe car :
- Ils sont immutables après leur création
- Les maps sont initialisées une seule fois au démarrage
- Aucune modification d'état n'est effectuée après l'initialisation

## Précision

Les deux convertisseurs :
- Arrondissent les volumes à 3 décimales
- Retournent les valeurs dB avec 1 décimale
- Utilisent l'interpolation linéaire pour les valeurs non exactes

## Maintenance

Pour modifier les tables de conversion :
1. Éditer les maps dans les constructeurs respectifs
2. Les valeurs sont en paires volume:dB
3. Maintenir l'ordre croissant des volumes pour la lisibilité
