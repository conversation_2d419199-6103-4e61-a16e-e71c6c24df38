---
description: 
globs: 
alwaysApply: false
---
# Système de Modes OSC

L'application repose sur un système de modes pour organiser les fonctionnalités et gérer les événements OSC et matériels.

## Structure de Base des Modes

Tous les modes héritent de [Communication/oscMode.go](mdc:Communication/oscMode.go) qui définit :

- L'interface `OscMode` avec les méthodes essentielles (Initialize, Activate, Deactivate, Cleanup)
- La structure `BaseMode` qui fournit une implémentation de base de ces méthodes
- Un système d'enregistrement de gestionnaires pour les adresses OSC

## Gestionnaire de Modes

Le [Live/liveModeManager.go](mdc:Live/liveModeManager.go) gère l'ensemble des modes :

- Enregistrement des modes disponibles
- Activation/désactivation des modes
- Transmission des messages OSC au mode approprié
- Implémentation de l'interface `OscService` pour l'envoi de messages OSC

## Modes Spécifiques

Les modes implémentés incluent :

- Mode Principal ([Live/liveMainMode.go](mdc:Live/liveMainMode.go)) : Fonctions globales de Live
- Mode Browser ([Live/liveBrowserMode.go](mdc:Live/liveBrowserMode.go)) : Navigation dans le navigateur de Live
- Autres modes (Track, Device, Learn)

## Cycle de Vie d'un Mode

1. Création et enregistrement du mode avec `RegisterMode`
2. Initialisation via `Initialize` (appels de services d'arrière-plan)
3. Activation via `Activate` lorsque le mode devient actif
4. Désactivation via `Deactivate` lorsqu'un autre mode est activé
5. Nettoyage via `Cleanup` lors de l'arrêt de l'application

Seul un mode peut être actif à la fois pour recevoir les événements matériels.
