---
description: 
globs: 
alwaysApply: false
---
# Architecture de Gestion des Modes OSC en Go

Ce document décrit l'architecture mise en place pour gérer différents modes d'interaction OSC, principalement avec Ableton Live, mais conçue pour être extensible.

## Composants Clés

1.  **`[oscBridge.go](mdc:oscBridge.go)` (`RawOscBridge`)**:
    *   **Rôle**: Couche la plus basse, gère la communication réseau UDP brute (écoute et envoi).
    *   **Fonctionnalité**: Décode de manière robuste les messages OSC entrants (tolérant aux erreurs) pour extraire l'adresse et les arguments. Encode les messages sortants en OSC standard.
    *   **Cycle de vie**: Instancié dans `[main.go](mdc:main.go)` et injecté dans le `LiveModeManager`. Sa fermeture est gérée par `defer` dans `main.go`.

2.  **`[oscService.go](mdc:oscService.go)` (`OscService`)**:
    *   **Rôle**: Définit une **interface** (un contrat) pour les services OSC requis par les modes.
    *   **Méthodes**: `On(address, handler)`, `Off(address, handler)`, `Send(address, args...)`.
    *   **Objectif**: Découpler les modes de l'implémentation spécifique du manager (`LiveModeManager`). Les modes dépendent uniquement de cette interface.

3.  **`[oscMode.go](mdc:oscMode.go)` (`OscMode`, `BaseMode`)**:
    *   **Rôle**: Fournit la structure pour créer des modules de logique OSC ("Modes").
    *   **`OscMode` (Interface)**: Définit le cycle de vie d'un mode:
        *   `Initialize(OscService)`: Appelée une fois lors de l'enregistrement. Enregistre les handlers, prépare l'état interne. Équivalent conceptuel du `backgroundStart`.
        *   `Activate()`: Appelée lorsque le mode devient le mode courant affiché/interagi.
        *   `Deactivate()`: Appelée lorsque le mode cesse d'être le mode courant.
        *   `Cleanup()`: Appelée lors de l'arrêt complet pour désenregistrer les handlers et nettoyer les ressources.
    *   **`BaseMode` (Struct)**: Implémentation de base de `OscMode`.
        *   Contient le `OscService`.
        *   Implémente la logique de base pour `Initialize`, `Activate`, `Deactivate`, `Cleanup`.
        *   Contient le flag `isActive bool` (géré par `Activate`/`Deactivate`) pour indiquer si le mode est le mode courant.
        *   Fournit `RegisterHandler(address, handler, help)` pour que les modes spécifiques déclarent facilement leurs adresses et handlers.
        *   Fournit `Send(...)` via le `OscService`.
    *   **Les modes spécifiques (ex: `LiveLearnMode`)** doivent embarquer `*BaseMode` et implémenter leur logique métier dans les méthodes handler.

4.  **`[liveModeManager.go](mdc:liveModeManager.go)` (`LiveModeManager`)**:
    *   **Rôle**: Orchestre les modes spécifiques à Ableton Live.
    *   **Implémente `OscService`**: Fournit l'implémentation concrète des services OSC aux modes.
    *   **Gestion des Modes**:
        *   `RegisterMode(name, mode)`: Enregistre un mode et appelle son `Initialize`.
        *   `SwitchMode(name)`: Gère le passage d'un mode actif à un autre en appelant `Deactivate()` sur l'ancien et `Activate()` sur le nouveau. Met à jour `currentActiveModeName`.
        *   `Cleanup()`: Appelle `Cleanup()` sur tous les modes enregistrés lors de l'arrêt.
    *   **Dépendances**: Reçoit le `*RawOscBridge` par injection de dépendance lors de sa création dans `[main.go](mdc:main.go)`.

## Cycle de Vie et Logique Actif/Background

*   **Initialisation (`main.go`)**:
    1.  Crée `RawOscBridge`.
    2.  Crée `LiveModeManager` en lui passant le bridge.
    3.  Crée les instances de tous les modes nécessaires (ex: `NewLiveLearnMode(...)`).
    4.  Appelle `manager.RegisterMode(...)` pour chaque mode. Ceci appelle `mode.Initialize(...)` qui enregistre tous les handlers OSC du mode.
    5.  Appelle `manager.SwitchMode(...)` une fois pour activer le mode initial.
*   **Fonctionnement**:
    *   Tous les handlers OSC enregistrés lors de `Initialize` sont **toujours actifs** et peuvent recevoir des messages (logique "background").
    *   **Dans les handlers des modes spécifiques**: Pour les actions qui ne doivent se produire que lorsque le mode est **le mode courant** (réponse à une interaction utilisateur directe comme tourner un encodeur *dans ce mode*), le handler **doit** commencer par vérifier le flag hérité de `BaseMode`:
        ```go
        if !m.isActive {
            return // Ne rien faire si ce n'est pas le mode actif
        }
        // ... reste de la logique "active" ...
        ```
*   **Changement de Mode**: `manager.SwitchMode("nouveauMode")` appelle `ancienMode.Deactivate()` puis `nouveauMode.Activate()`, mettant à jour le flag `isActive` dans les modes respectifs.
*   **Arrêt (`main.go` via signal)**: Les `defer` assurent l'appel à `manager.Cleanup()` (qui appelle `mode.Cleanup()` pour tous les modes) puis `oscBridge.Close()`.

Cette architecture vise à séparer les responsabilités, à permettre le découplage et à gérer clairement le cycle de vie et l'état actif/inactif des différents modules fonctionnels.
