---
description: 
globs: 
alwaysApply: false
---
# Débogage du Projet

Le projet utilise plusieurs mécanismes de journalisation et débogage pour faciliter le développement et le diagnostic.

## Logs Système

Des logs détaillés sont générés tout au long de l'application :

- Initialisation des composants dans [main.go](mdc:main.go)
- État des modes dans [Live/liveModeManager.go](mdc:Live/liveModeManager.go)
- Messages matériels dans [Communication/hardwareManager.go](mdc:Communication/hardwareManager.go)

## Débogage Matériel

Le `HardwareManager` inclut un mode debug spécifique :

```go
// Activer le mode debug
hardwareManager.SetDebug(true)
```

Ce mode affiche des logs détaillés :
- Réception des messages bruts
- Analyse des messages et conversion en événements
- Transmission des événements au mode actif

## Débogage des Messages OSC

Les messages OSC peuvent être tracés en activant le mode debug :

```go
// Dans main.go, lors de la création du RawOscBridge
oscBridge, err := communication.NewRawOscBridge(localPort, remotePort, true) // true = debug
```

## Visualisation des Flux

Pour comprendre le flux de messages :

1. Les messages série sont préfixés par "Message série reçu:"
2. Les événements matériels sont préfixés par "[HW]"
3. Les logs de mode affichent le nom du mode en préfixe (ex: "Mode principal:")

## Points d'Attention pour le Débogage

Si les événements matériels ne sont pas traités correctement, vérifier :

1. Si le message est reçu correctement par le `CommunicationManager` 
2. Si le `HardwareManager` analyse correctement le format du message
3. Si le mode actif est bien défini comme récepteur d'événements dans `main.go`
4. Si le mode actif implémente correctement `HardwareEventReceiver`
