---
description: 
globs: 
alwaysApply: false
---
# Flux de Communication

Ce projet gère deux flux de communication principaux:

## 1. Communication OSC (Open Sound Control)

La communication OSC est gérée par le `RawOscBridge` et utilisée pour communiquer avec Ableton Live.

- **Entrée OSC**: Messages reçus de Live sur le port local
- **Sortie OSC**: Messages envoyés à Live sur le port distant
- **Flux**: 
  1. Les messages OSC entrants sont reçus dans [Communication/oscService.go](mdc:Communication/oscService.go)
  2. Le [Live/liveModeManager.go](mdc:Live/liveModeManager.go) les distribue aux gestionnaires appropriés
  3. Chaque mode peut envoyer des messages OSC via la méthode `Send`

## 2. Communication Matérielle (ESP32)

La communication série avec l'ESP32 est gérée par le `CommunicationManager`.

- **Entrée Série**: Messages reçus de l'ESP32 (boutons, encodeurs, touches)
- **Sortie <PERSON>**: Messages de configuration envoyés à l'ESP32
- **Flux**:
  1. Les messages série sont reçus dans [Communication/communicationManager.go](mdc:Communication/communicationManager.go)
  2. Le [Communication/hardwareManager.go](mdc:Communication/hardwareManager.go) analyse et convertit ces messages en événements typés
  3. Les événements sont transmis au mode actif via l'interface `HardwareEventReceiver`
  4. Le mode actif peut répondre en envoyant des messages OSC ou série

## 3. Propagation des Événements

Le système utilise un mécanisme d'événements pour la communication interne:

- [Communication/eventEmitter.go](mdc:Communication/eventEmitter.go) fournit un système d'abonnement aux événements
- Les composants peuvent s'abonner à des événements spécifiques via `On` et se désabonner via `Off`
- Les événements sont déclenchés via `Emit` avec leurs arguments

Ce modèle permet un couplage faible entre les composants tout en maintenant un flux de communication efficace.
