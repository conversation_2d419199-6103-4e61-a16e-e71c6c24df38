---
description:
globs:
alwaysApply: false
---
# Événements du LiveTrackManager

Le `LiveTrackManager` en Go utilise un système d'événements pour communiquer les changements d'état aux composants qui s'y abonnent.

## Architecture des événements

Le `LiveTrackManager` intègre un `EventEmitter` par composition :

```go
type LiveTrackManager struct {
    *communication.EventEmitter // Intégration de l'émetteur d'événements
    // Autres champs...
}
```

Cela lui permet d'hériter directement des méthodes `On`, `Off`, et `Emit` pour gérer les abonnements aux événements.

## Événements disponibles

Le `LiveTrackManager` émet les événements suivants :

| Nom d'événement | Description | Format des données |
|-----------------|-------------|-------------------|
| `trackCountUpdate` | Émis lorsque le nombre de pistes change | `[]interface{}{trackCount, returnTrackCount}` |
| `selectedTrackDeviceUpdate` | Émis lorsque la piste ou le device sélectionné change | `[]interface{}{selectedTrack, selectedDevice, devicesCount, parametersCount}` |
| `returnTracksNameChange` | Émis lorsque les noms des pistes de retour changent | `[]interface{}{[]string{name1, name2, ...}}` |
| `liveDisconnected` | Émis lorsque Live se déconnecte | `nil` |

## Abonnement aux événements

Pour s'abonner à ces événements dans un autre composant :

```go
// Exemple d'abonnement à l'événement trackCountUpdate
trackManager.On("trackCountUpdate", func(args []interface{}) {
    trackCount := args[0].(int)
    returnTrackCount := args[1].(int)
    // Traiter la mise à jour...
})
```

## Émission d'événements

Le `LiveTrackManager` émet des événements après avoir reçu et traité des messages OSC :

```go
// Exemple d'émission d'événement
tm.Emit("trackCountUpdate", []interface{}{trackCount, returnTrackCount})
```

## Nettoyage des abonnements

Pour nettoyer tous les abonnements :

```go
// Supprimer tous les gestionnaires d'événements
trackManager.RemoveAllListeners()

// Ou supprimer les gestionnaires pour un événement spécifique
trackManager.RemoveAllListeners("trackCountUpdate")
```
