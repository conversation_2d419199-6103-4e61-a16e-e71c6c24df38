package communication

// OscService définit les méthodes nécessaires pour interagir avec le système OSC
// depuis un mode spécifique.
type OscService interface {
	// On enregistre un gestionnaire pour une adresse OSC spécifique.
	On(address string, handler func([]interface{}))
	// Off supprime un gestionnaire pour une adresse OSC spécifique.
	Off(address string, handler func([]interface{}))
	// Send envoie un message OSC.
	Send(address string, args ...interface{}) error
}
