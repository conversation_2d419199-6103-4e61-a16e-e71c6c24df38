package communication

import (
	"fmt"
	"log"
	"sync"
	"time"
)

// CommunicationManager gère les différentes méthodes de communication
type CommunicationManager struct {
	*EventEmitter
	serialManager   *SerialManager
	hardwareManager *HardwareManager
	activeManager   interface{} // Interface pour supporter d'autres managers à l'avenir
	isInitialized   bool
	messageHandler  func(message string)
	mu              sync.RWMutex

	// Files d'attente pour les messages
	priorityMessageQueue   []string
	backgroundMessageQueue []string
	isProcessingPriority   bool
	isProcessingBackground bool
	messageDelay           time.Duration
	backgroundMessageDelay time.Duration
	priorityCheckDelay     time.Duration
	queueMutex             sync.Mutex
}

// NewCommunicationManager crée une nouvelle instance de CommunicationManager
func NewCommunicationManager() *CommunicationManager {
	return &CommunicationManager{
		EventEmitter:           NewEventEmitter(),
		isInitialized:          false,
		priorityMessageQueue:   make([]string, 0),
		backgroundMessageQueue: make([]string, 0),
		messageDelay:           1 * time.Millisecond,
		backgroundMessageDelay: 10 * time.Millisecond,
		priorityCheckDelay:     10 * time.Millisecond,
	}
}

// Initialize initialise le gestionnaire de communication
func (cm *CommunicationManager) Initialize() error {
	log.Println("Initialisation du gestionnaire de communication...")

	// Créer et initialiser le SerialManager
	cm.serialManager = NewSerialManager(921600)
	success, err := cm.serialManager.InitializeSerialPort()
	if err != nil {
		return fmt.Errorf("erreur lors de l'initialisation du port série : %w", err)
	}
	if !success {
		return fmt.Errorf("aucun port série compatible n'a été trouvé")
	}

	// Créer et initialiser le HardwareManager
	cm.hardwareManager = NewHardwareManager(cm)
	if err := cm.hardwareManager.Initialize(); err != nil {
		return fmt.Errorf("erreur lors de l'initialisation du gestionnaire matériel : %w", err)
	}

	// Configurer les gestionnaires d'événements
	cm.setupEventHandlers()

	cm.isInitialized = true
	cm.activeManager = cm.serialManager
	log.Println("Gestionnaire de communication initialisé avec succès")
	return nil
}

// setupEventHandlers configure les gestionnaires d'événements pour tous les managers
func (cm *CommunicationManager) setupEventHandlers() {
	// Configuration des événements du SerialManager
	cm.serialManager.On("message", func(args []interface{}) {
		if len(args) > 0 {
			if message, ok := args[0].(string); ok {
				cm.handleMessage(message)
			}
		}
	})

	cm.serialManager.On("disconnected", func(args []interface{}) {
		cm.handleDisconnection()
	})
}

// handleMessage traite les messages reçus
func (cm *CommunicationManager) handleMessage(message string) {
	log.Printf("Message reçu : %s", message)

	// Émettre l'événement message pour les abonnés externes
	cm.Emit("message", []interface{}{message})

	// Si un gestionnaire de message personnalisé est défini, l'appeler
	if cm.messageHandler != nil {
		cm.messageHandler(message)
	}
}

// handleDisconnection gère la déconnexion d'un manager
func (cm *CommunicationManager) handleDisconnection() {
	log.Println("Déconnexion détectée")
	cm.Emit("disconnected", nil)
}

// SendMessage envoie un message via le manager actif
func (cm *CommunicationManager) SendMessage(message string, isPriority bool) error {
	if !cm.isInitialized {
		return fmt.Errorf("le gestionnaire de communication n'est pas initialisé")
	}

	cm.queueMutex.Lock()
	if isPriority {
		cm.priorityMessageQueue = append(cm.priorityMessageQueue, message)
		if !cm.isProcessingPriority {
			go cm.processPriorityQueue()
		}
	} else {
		cm.backgroundMessageQueue = append(cm.backgroundMessageQueue, message)
		if !cm.isProcessingBackground {
			go cm.processBackgroundQueue()
		}
	}
	cm.queueMutex.Unlock()

	return nil
}

// processPriorityQueue traite la file d'attente prioritaire
func (cm *CommunicationManager) processPriorityQueue() {
	cm.queueMutex.Lock()
	if cm.isProcessingPriority || len(cm.priorityMessageQueue) == 0 {
		cm.queueMutex.Unlock()
		return
	}
	cm.isProcessingPriority = true
	cm.queueMutex.Unlock()

	for {
		cm.queueMutex.Lock()
		if len(cm.priorityMessageQueue) == 0 {
			cm.isProcessingPriority = false
			cm.queueMutex.Unlock()
			return
		}

		message := cm.priorityMessageQueue[0]
		cm.priorityMessageQueue = cm.priorityMessageQueue[1:]

		// Interrompre le traitement en arrière-plan si nécessaire
		if cm.isProcessingBackground {
			cm.isProcessingBackground = false
			log.Println("Mode prioritaire : interruption du traitement en arrière-plan")
		}
		cm.queueMutex.Unlock()

		if err := cm.serialManager.SendMessage(message); err != nil {
			log.Printf("Erreur lors de l'envoi du message prioritaire : %v", err)
		}

		time.Sleep(cm.messageDelay)
	}
}

// processBackgroundQueue traite la file d'attente en arrière-plan
func (cm *CommunicationManager) processBackgroundQueue() {
	cm.queueMutex.Lock()
	if cm.isProcessingBackground || len(cm.backgroundMessageQueue) == 0 {
		cm.queueMutex.Unlock()
		return
	}
	cm.isProcessingBackground = true
	cm.queueMutex.Unlock()

	lastPriorityCheck := time.Now()

	for {
		// Vérification périodique de la queue prioritaire
		if time.Since(lastPriorityCheck) >= cm.priorityCheckDelay {
			cm.queueMutex.Lock()
			if len(cm.priorityMessageQueue) > 0 {
				log.Println("Messages prioritaires détectés, interruption du traitement en arrière-plan")
				cm.isProcessingBackground = false
				cm.queueMutex.Unlock()
				go cm.processPriorityQueue()
				time.Sleep(cm.backgroundMessageDelay * 2)
				return
			}
			cm.queueMutex.Unlock()
			lastPriorityCheck = time.Now()
		}

		cm.queueMutex.Lock()
		if len(cm.backgroundMessageQueue) == 0 {
			cm.isProcessingBackground = false
			cm.queueMutex.Unlock()
			return
		}

		message := cm.backgroundMessageQueue[0]
		cm.backgroundMessageQueue = cm.backgroundMessageQueue[1:]
		cm.queueMutex.Unlock()

		if err := cm.serialManager.SendMessage(message); err != nil {
			log.Printf("Erreur lors de l'envoi du message en arrière-plan : %v", err)
		}

		time.Sleep(cm.backgroundMessageDelay)
	}
}

// SetMessageHandler définit un gestionnaire de messages personnalisé
func (cm *CommunicationManager) SetMessageHandler(handler func(message string)) {
	cm.messageHandler = handler
}

// IsConnected vérifie si le manager actif est connecté
func (cm *CommunicationManager) IsConnected() bool {
	if !cm.isInitialized {
		return false
	}
	return cm.serialManager.IsConnected()
}

// Close ferme proprement toutes les connexions
func (cm *CommunicationManager) Close() error {
	if !cm.isInitialized {
		return nil
	}

	cm.isInitialized = false

	// Fermer le SerialManager
	if err := cm.serialManager.Close(); err != nil {
		return fmt.Errorf("erreur lors de la fermeture du SerialManager : %w", err)
	}

	return nil
}

// SetActiveMode définit le mode actif qui recevra les événements matériels
func (cm *CommunicationManager) SetActiveMode(mode HardwareEventReceiver) {
	if cm.hardwareManager != nil {
		cm.hardwareManager.SetActiveMode(mode)
	}
}

// GetHardwareManager retourne le gestionnaire matériel
func (cm *CommunicationManager) GetHardwareManager() *HardwareManager {
	return cm.hardwareManager
}

// AddHardwareEventHandler ajoute un gestionnaire d'événements matériels
func (cm *CommunicationManager) AddHardwareEventHandler(handler func(HardwareEvent)) {
	if cm.hardwareManager != nil {
		cm.hardwareManager.SetActiveMode(&hardwareEventReceiverAdapter{handler: handler})
	}
}

// RemoveHardwareEventHandler supprime un gestionnaire d'événements matériels
func (cm *CommunicationManager) RemoveHardwareEventHandler(handler func(HardwareEvent)) {
	if cm.hardwareManager != nil {
		cm.hardwareManager.SetActiveMode(nil)
	}
}

// hardwareEventReceiverAdapter adapte une fonction de gestion d'événements à l'interface HardwareEventReceiver
type hardwareEventReceiverAdapter struct {
	handler func(HardwareEvent)
}

func (a *hardwareEventReceiverAdapter) HandleHardwareEvent(event HardwareEvent) {
	if a.handler != nil {
		a.handler(event)
	}
}
