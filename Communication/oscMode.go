package communication

import (
	"fmt"
)

// OscMode définit l'interface pour un mode OSC
type OscMode interface {
	// Initialize est appelé lors de l'enregistrement du mode auprès du manager
	Initialize(service OscService)
	// Activate est appelé lorsque le mode devient le mode courant
	Activate()
	// Deactivate est appelé lorsque le mode cesse d'être le mode courant
	Deactivate()
	// CleanupForExit est appelé lors de l'arrêt de l'application pour nettoyer toutes les ressources
	CleanupForExit()
	// GetAddresses retourne toutes les adresses OSC gérées par ce mode (principalement utilisé par Initialize)
	GetAddresses() map[string]OscHandler
}

// OscHandler est le type pour les gestionnaires de messages OSC
type OscHandler struct {
	Handler func([]interface{})
	Help    string // Description de ce que fait ce handler
}

// BaseMode fournit une implémentation de base pour les modes OSC
type BaseMode struct {
	service      OscService
	addresses    map[string]<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
	initialized  bool // Indique si le mode est initialisé (true après Initialize, false après Cleanup)
	eventEmitter *EventEmitter
}

// NewBaseMode crée un nouveau mode de base
func NewBaseMode() *BaseMode {
	return &BaseMode{
		addresses:    make(map[string]OscHandler),
		initialized:  false, // Initialement non initialisé
		eventEmitter: NewEventEmitter(),
	}
}

// Initialize initialise le mode et enregistre les handlers OSC.
func (m *BaseMode) Initialize(service OscService) {
	if m.initialized { // Déjà initialisé
		return
	}
	m.service = service
	m.initialized = true

	// Enregistrer tous les handlers via le service
	for addr, handler := range m.GetAddresses() {
		if m.service != nil { // Vérification supplémentaire
			m.service.On(addr, handler.Handler)
		}
	}
	fmt.Printf("Mode Base initialisé (Handlers enregistrés pour %d adresses)\n", len(m.addresses))
}

// Activate fournit la logique de base pour l'activation d'un mode.
// Les modes spécifiques doivent implémenter leur propre logique d'activation.
func (m *BaseMode) Activate() {
	if !m.initialized { // Ne peut pas activer un mode non initialisé
		fmt.Println("Tentative d'activation d'un mode non initialisé.")
		return
	}
	fmt.Println("Mode Base: logique d'activation de base effectuée.")
}

// Deactivate fournit la logique de base pour la désactivation d'un mode.
// Les modes spécifiques doivent implémenter leur propre logique de désactivation.
func (m *BaseMode) Deactivate() {
	fmt.Println("Mode Base: logique de désactivation de base effectuée.")
}

// CleanupForExit nettoie le mode, désenregistre les handlers et le marque comme non initialisé.
func (m *BaseMode) CleanupForExit() {
	if !m.initialized { // Déjà nettoyé ou jamais initialisé
		return
	}

	// Supprimer tous les handlers via le service
	fmt.Printf("Nettoyage Mode Base (Désenregistrement de %d handlers)...\n", len(m.addresses))
	for addr, handler := range m.GetAddresses() {
		if m.service != nil { // Vérification supplémentaire
			m.service.Off(addr, handler.Handler)
		}
	}

	m.initialized = false
	fmt.Println("Mode Base nettoyé.")
}

// IsInitialized retourne si le mode est initialisé
func (m *BaseMode) IsInitialized() bool {
	return m.initialized
}

// GetAddresses retourne les adresses gérées (à surcharger ou utiliser via RegisterHandler)
func (m *BaseMode) GetAddresses() map[string]OscHandler {
	return m.addresses
}

// RegisterHandler enregistre un nouveau handler OSC
func (m *BaseMode) RegisterHandler(address string, handler func([]interface{}), help string) {
	m.addresses[address] = OscHandler{
		Handler: handler,
		Help:    help,
	}
	if m.service != nil && m.initialized {
		m.service.On(address, handler)
	}
}

// UnregisterHandler supprime un handler OSC
func (m *BaseMode) UnregisterHandler(address string) {
	if handler, exists := m.addresses[address]; exists {
		if m.service != nil && m.initialized {
			m.service.Off(address, handler.Handler)
		}
		delete(m.addresses, address)
	}
}

// Send envoie un message OSC via le service
func (m *BaseMode) Send(address string, args ...interface{}) error {
	if m.service != nil {
		// Si args est un slice d'interface{} avec un seul élément qui est lui-même un slice,
		// on décompresse ce slice interne
		if len(args) == 1 {
			if slice, ok := args[0].([]interface{}); ok {
				return m.service.Send(address, slice...)
			}
		}
		return m.service.Send(address, args...)
	}
	return fmt.Errorf("service OSC non initialisé dans BaseMode")
}

// GetService retourne le service OSC associé
func (m *BaseMode) GetService() OscService {
	return m.service
}

// GetEventEmitter retourne l'émetteur d'événements du mode
func (m *BaseMode) GetEventEmitter() *EventEmitter {
	return m.eventEmitter
}

// Emit émet un événement avec les arguments donnés
func (m *BaseMode) Emit(eventName string, args ...interface{}) {
	if m.eventEmitter != nil {
		m.eventEmitter.Emit(eventName, args)
	}
}
