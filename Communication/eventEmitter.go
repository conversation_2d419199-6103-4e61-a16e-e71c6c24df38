package communication

import (
	"sync"
)

// EventHandler est le type pour les fonctions de gestion d'événements
type EventHandler func(args []interface{})

// EventEmitter gère les événements et leurs gestionnaires
type EventEmitter struct {
	handlers map[string][]EventHandler
	mu       sync.RWMutex
}

// NewEventEmitter crée un nouveau gestionnaire d'événements
func NewEventEmitter() *EventEmitter {
	return &EventEmitter{
		handlers: make(map[string][]EventHandler),
	}
}

// On enregistre un nouveau gestionnaire pour un événement
func (e *EventEmitter) On(event string, handler EventHandler) {
	e.mu.Lock()
	defer e.mu.Unlock()

	if _, exists := e.handlers[event]; !exists {
		e.handlers[event] = make([]EventHandler, 0)
	}
	e.handlers[event] = append(e.handlers[event], handler)
}

// Off supprime un gestionnaire pour un événement
func (e *EventEmitter) Off(event string, handler <PERSON>H<PERSON>ler) {
	e.mu.Lock()
	defer e.mu.Unlock()

	if handlers, exists := e.handlers[event]; exists {
		for i, h := range handlers {
			if &h == &handler {
				e.handlers[event] = append(handlers[:i], handlers[i+1:]...)
				break
			}
		}
	}
}

// Emit déclenche un événement avec ses arguments
func (e *EventEmitter) Emit(event string, args []interface{}) {
	e.mu.RLock()
	handlers, exists := e.handlers[event]
	e.mu.RUnlock()

	if exists {
		for _, handler := range handlers {
			go handler(args) // Exécution asynchrone des gestionnaires
		}
	}
}

// ListenerCount retourne le nombre de gestionnaires pour un événement
func (e *EventEmitter) ListenerCount(event string) int {
	e.mu.RLock()
	defer e.mu.RUnlock()

	return len(e.handlers[event])
}

// RemoveAllListeners supprime tous les gestionnaires d'événements
// Si eventName est une chaîne non vide, supprime uniquement les gestionnaires de cet événement
// Si eventName est une chaîne vide, supprime les gestionnaires de tous les événements
func (e *EventEmitter) RemoveAllListeners(eventName ...string) {
	e.mu.Lock()
	defer e.mu.Unlock()

	// Si aucun nom d'événement n'est fourni ou si une liste vide est fournie, supprimer tous les gestionnaires
	if len(eventName) == 0 {
		e.handlers = make(map[string][]EventHandler)
		return
	}

	// Supprimer les gestionnaires pour l'événement spécifié
	event := eventName[0]
	delete(e.handlers, event)
}
