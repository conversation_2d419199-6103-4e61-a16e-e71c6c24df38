package communication

import (
	"bytes"
	"encoding/binary"
	"fmt"
	"log"
	"math"
	"net"
	"sync"
	"time"

	"github.com/crgimenes/go-osc"
)

// ANSI color codes
const (
	ColorReset = "\033[0m"
	ColorCyan  = "\033[36m"
)

// RawOscBridge est une implémentation ultra-robuste pour les communications OSC
// qui ignore complètement les erreurs d'analyse des messages entrants
type RawOscBridge struct {
	localPort  int
	remotePort int
	remoteAddr *net.UDPAddr
	conn       *net.UDPConn
	handlers   map[string]func([]interface{})
	mu         sync.RWMutex
	debug      bool
	stopChan   chan struct{}
}

// NewRawOscBridge crée un nouveau pont OSC ultra-robuste
func NewRawOscBridge(localPort, remotePort int, debug bool) (*RawOscBridge, error) {
	// Résoudre l'adresse distante
	remoteAddr, err := net.ResolveUDPAddr("udp", fmt.Sprintf("127.0.0.1:%d", remotePort))
	if err != nil {
		return nil, fmt.Errorf("erreur lors de la résolution de l'adresse distante: %w", err)
	}

	// Résoudre l'adresse locale
	localAddr, err := net.ResolveUDPAddr("udp", fmt.Sprintf("0.0.0.0:%d", localPort))
	if err != nil {
		return nil, fmt.Errorf("erreur lors de la résolution de l'adresse locale: %w", err)
	}

	// Créer la connexion UDP
	conn, err := net.ListenUDP("udp", localAddr)
	if err != nil {
		return nil, fmt.Errorf("erreur lors de la création de la connexion UDP: %w", err)
	}

	bridge := &RawOscBridge{
		localPort:  localPort,
		remotePort: remotePort,
		remoteAddr: remoteAddr,
		conn:       conn,
		handlers:   make(map[string]func([]interface{})),
		debug:      debug,
		stopChan:   make(chan struct{}),
	}

	// Démarrer la réception
	go bridge.receiveLoop()

	if debug {
		log.Printf("Pont OSC démarré sur le port %d (adresse distante: %s)", localPort, remoteAddr)
	}

	return bridge, nil
}

// receiveLoop traite les messages OSC entrants en mode brut
func (b *RawOscBridge) receiveLoop() {
	buffer := make([]byte, 65536)       // Buffer suffisamment grand pour la plupart des messages OSC
	log.Println("receiveLoop démarrée") // Log ajouté

	for {
		select {
		case <-b.stopChan:
			log.Println("receiveLoop arrêtée par stopChan") // Log ajouté
			return
		default:
			// Fixer un délai d'attente pour permettre la vérification périodique du canal d'arrêt
			err := b.conn.SetReadDeadline(time.Now().Add(time.Second))
			if err != nil && b.debug {
				log.Printf("Erreur lors de la définition du délai d'attente: %v", err)
			}

			// Lire les données du socket
			// if b.debug {
			// 	log.Println("receiveLoop: Attente de lecture sur le socket UDP...") // Commenté pour réduire le bruit
			// }
			n, addr, err := b.conn.ReadFromUDP(buffer)
			if err != nil {
				// Ignorer les erreurs de délai d'attente (c'est normal)
				if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
					// if b.debug {
					// 	log.Println("receiveLoop: Timeout de lecture (normal)") // Commenté pour réduire le bruit
					// }
					continue
				}

				// Loguer les autres erreurs de lecture
				log.Printf("ERREUR: Erreur lors de la lecture des données UDP: %v", err)
				time.Sleep(100 * time.Millisecond)
				continue
			}

			// Données reçues, essayer de décoder
			if b.debug {
				log.Printf("receiveLoop: %d octets reçus de %v. Tentative de décodage...", n, addr)
			}
			b.safeDecode(buffer[:n], addr)
		}
	}
}

func (b *RawOscBridge) safeDecode(data []byte, addr net.Addr) {
	// Utiliser un canal pour capturer les paniques
	done := make(chan bool, 1)

	go func() {
		// Récupérer des paniques éventuelles
		defer func() {
			if r := recover(); r != nil {
				// Toujours loguer la panique, mais ajouter plus de détails si debug est activé
				log.Printf("ERREUR CRITIQUE: Panique récupérée lors du décodage OSC: %v", r)
				if b.debug {
					log.Printf("Données brutes ayant causé la panique: %v", data)
				}
			}
			done <- true
		}()

		// Extraire l'adresse et les arguments de base
		address, arguments := b.extractAddressAndArguments(data)

		if address == "" {
			if b.debug {
				log.Println("Impossible d'extraire l'adresse du message OSC")
			}
			return
		}

		if b.debug {
			log.Printf("Message OSC brut reçu avec l'adresse: %s", address)
		}

		// Créer un message avec l'adresse
		msg := osc.NewMessage(address)

		// Ajouter les arguments extraits au message
		for _, arg := range arguments {
			msg.Arguments = append(msg.Arguments, arg)
		}

		// Gérer le message
		b.handleMessage(msg, addr)
	}()

	// Attendre la fin du traitement avec un délai
	select {
	case <-done:
		// Traitement terminé
	case <-time.After(100 * time.Millisecond):
		if b.debug {
			log.Println("Délai d'attente dépassé lors du décodage OSC")
		}
	}
}

func (b *RawOscBridge) extractAddressAndArguments(data []byte) (string, []interface{}) {
	var arguments []interface{}

	// Vérifier la longueur minimale pour avoir au moins une adresse
	if len(data) < 4 {
		return "", arguments
	}

	// 1. Trouver la fin de l'adresse (premier null byte)
	addrEnd := bytes.IndexByte(data, 0)
	if addrEnd <= 0 {
		return "", arguments
	}

	// Extraire l'adresse
	address := string(data[:addrEnd])

	// 2. Trouver le début des types (aligné sur 4 bytes et commence par ',')
	tagStart := (addrEnd + 4) & ^3
	if tagStart >= len(data) || data[tagStart] != ',' {
		return address, arguments
	}

	// 3. Trouver la fin des types (prochain null byte)
	tagEnd := bytes.IndexByte(data[tagStart:], 0)
	if tagEnd <= 0 {
		return address, arguments
	}
	tagEnd += tagStart

	// Extraire les types (sans la virgule)
	types := string(data[tagStart+1 : tagEnd])

	// 4. Le début des arguments est aligné sur 4 bytes après la fin des types
	argPos := (tagEnd + 4) & ^3

	// 5. Parser chaque argument selon son type
	i := 0
	for i < len(types) {
		if argPos >= len(data) {
			break
		}

		switch types[i] {
		case 'i':
			if argPos+4 <= len(data) {
				val := int32(binary.BigEndian.Uint32(data[argPos:]))
				arguments = append(arguments, val)
				argPos += 4
			}
			i++
		case 'f':
			if argPos+4 <= len(data) {
				bits := binary.BigEndian.Uint32(data[argPos:])
				val := math.Float32frombits(bits)
				arguments = append(arguments, val)
				argPos += 4
			}
			i++
		case 's':
			if argPos >= len(data) {
				i++
				continue
			}
			strEnd := bytes.IndexByte(data[argPos:], 0)
			if strEnd < 0 {
				i++
				continue
			}
			strEnd += argPos
			val := string(data[argPos:strEnd])
			arguments = append(arguments, val)
			argPos = (strEnd + 4) & ^3
			i++
		case 'T':
			arguments = append(arguments, true)
			i++
		case 'F':
			arguments = append(arguments, false)
			i++
		case 'N':
			arguments = append(arguments, nil)
			i++
		case '[': // Début d'un tableau
			// Extraire les tags du tableau
			arrayTags, endIndex := b.extractArrayTags(types, i)

			if len(arrayTags) > 0 {
				// Analyser les éléments du tableau
				arrayElements, newPos := b.parseArrayElements(data, argPos, arrayTags)
				arguments = append(arguments, arrayElements)
				argPos = newPos
			} else {
				arguments = append(arguments, []interface{}{})
			}

			// Passer à l'élément après le tableau
			i = endIndex + 1

		case ']': // Fin d'un tableau
			// Ce cas devrait être géré par extractArrayTags
			i++

		default:
			if b.debug {
				log.Printf("Type d'argument inconnu: %c", types[i])
			}
			i++
		}
	}

	if b.debug {
		log.Printf("Adresse: %s, Arguments: %v", address, arguments)
	}

	return address, arguments
}

// extractArrayTags récupère les tags à l'intérieur d'un tableau OSC
func (b *RawOscBridge) extractArrayTags(tags string, startIndex int) (string, int) {
	// Vérifier qu'on commence bien par un '['
	if startIndex >= len(tags) || tags[startIndex] != '[' {
		return "", startIndex
	}

	// Chercher la fin du tableau
	bracketCount := 1
	endIndex := startIndex

	for i := startIndex + 1; i < len(tags); i++ {
		if tags[i] == '[' {
			bracketCount++
		} else if tags[i] == ']' {
			bracketCount--
			if bracketCount == 0 {
				endIndex = i
				break
			}
		}
	}

	// Si on n'a pas trouvé de ']' correspondant, on renvoie une chaîne vide
	if bracketCount != 0 {
		return "", startIndex
	}

	// Extraire le contenu entre les crochets
	return tags[startIndex+1 : endIndex], endIndex
}

// parseArrayElements analyse les éléments d'un tableau OSC
func (b *RawOscBridge) parseArrayElements(data []byte, startPos int, arrayTags string) ([]interface{}, int) {
	var arrayElements []interface{}
	pos := startPos

	i := 0
	for i < len(arrayTags) {
		// Vérifier si nous sommes encore dans les limites des données
		if pos >= len(data) {
			break
		}

		switch arrayTags[i] {
		case 'i': // int32
			if pos+4 <= len(data) {
				val := int32(binary.BigEndian.Uint32(data[pos:]))
				arrayElements = append(arrayElements, val)
				pos += 4
			}
			i++

		case 'f': // float32
			if pos+4 <= len(data) {
				bits := binary.BigEndian.Uint32(data[pos:])
				val := math.Float32frombits(bits)
				arrayElements = append(arrayElements, val)
				pos += 4
			}
			i++

		case 's': // string
			if pos >= len(data) {
				i++
				continue
			}
			strEnd := bytes.IndexByte(data[pos:], 0)
			if strEnd < 0 {
				i++
				continue
			}
			strEnd += pos
			val := string(data[pos:strEnd])
			arrayElements = append(arrayElements, val)
			pos = (strEnd + 4) & ^3
			i++

		case 'T': // true
			arrayElements = append(arrayElements, true)
			i++

		case 'F': // false
			arrayElements = append(arrayElements, false)
			i++

		case 'N': // nil
			arrayElements = append(arrayElements, nil)
			i++

		case '[': // tableau imbriqué
			// Récupérer tous les tags dans le tableau
			subArrayTags, endIndex := b.extractArrayTags(arrayTags, i)

			if len(subArrayTags) > 0 {
				// Analyser les éléments du sous-tableau
				subArrayArgs, newPos := b.parseArrayElements(data, pos, subArrayTags)
				arrayElements = append(arrayElements, subArrayArgs)
				pos = newPos
			} else {
				// Tableau vide ou mal formé
				arrayElements = append(arrayElements, []interface{}{})
			}

			// Passer à l'élément après le sous-tableau
			i = endIndex + 1

		case ']': // fin d'un tableau
			// Ce cas devrait être géré par extractArrayTags
			i++

		default:
			if b.debug {
				log.Printf("Type d'argument inconnu '%c' ignoré dans un tableau à la position %d", arrayTags[i], i)
			}
			// Ajouter un placeholder pour l'argument ignoré
			arrayElements = append(arrayElements, fmt.Sprintf("<type %c ignoré>", arrayTags[i]))
			i++
		}
	}

	return arrayElements, pos
}

// handleMessage traite un message OSC décodé
func (b *RawOscBridge) handleMessage(msg *osc.Message, addr net.Addr) {
	log.Println("handleMessage atteint")
	if msg == nil {
		return
	}

	address := msg.Address
	var args []interface{}

	// Extraire les arguments
	for i := 0; i < len(msg.Arguments); i++ {
		args = append(args, msg.Arguments[i])
	}

	// Afficher des infos en mode debug
	if b.debug {
		fmt.Printf("Message OSC reçu de %v: %s%s%s\n", addr, ColorCyan, address, ColorReset) // Adresse en cyan
		fmt.Printf("  Nombre d'arguments: %d\n", len(args))
		for i, arg := range args {
			if arg == nil {
				fmt.Printf("  Arg %d: <nil>\n", i)
			} else {
				fmt.Printf("  Arg %d: %T %v\n", i, arg, arg)
			}
		}
	} else {
		fmt.Printf("Message OSC reçu: %s%s%s %v\n", ColorCyan, address, ColorReset, args) // Adresse en cyan
	}

	// Appeler le gestionnaire si enregistré
	b.mu.RLock()
	specificHandler, specificExists := b.handlers[address] // Vérifier handler spécifique
	wildcardHandler, wildcardExists := b.handlers["*"]     // Vérifier handler wildcard
	b.mu.RUnlock()

	if specificExists { // Donner la priorité au handler spécifique
		go specificHandler(args)
	} else if wildcardExists { // Sinon, appeler le wildcard
		// Passer l'adresse comme premier argument, comme attendu par LiveModeManager
		allArgs := append([]interface{}{address}, args...)
		go wildcardHandler(allArgs)
	} else if b.debug { // Seulement si AUCUN handler (spécifique OU wildcard) n'existe ET debug=true
		fmt.Printf("Message OSC non géré (aucun handler spécifique ou wildcard): %s%s%s\n", ColorCyan, address, ColorReset)
	}
}

// On enregistre un gestionnaire pour un type de message
func (b *RawOscBridge) On(address string, handler func([]interface{})) {
	b.mu.Lock()
	defer b.mu.Unlock()
	b.handlers[address] = handler
}

// Send envoie un message OSC
func (b *RawOscBridge) Send(address string, args ...interface{}) error {
	if b.debug {
		fmt.Printf("Envoi du message OSC: %s %v\n", address, args)
	}

	// Créer un nouveau client (plus sûr que de réutiliser une connexion)
	client := osc.NewClient("127.0.0.1", b.remotePort)

	// Créer le message
	msg := osc.NewMessage(address)

	// Ajouter les arguments directement, sans conversion JSON
	for _, arg := range args {
		if err := b.appendSafely(msg, arg); err != nil {
			if b.debug {
				fmt.Printf("AVERTISSEMENT: Erreur lors de l'ajout d'un argument: %v\n", err)
			}
		}
	}

	// Envoyer le message
	return client.Send(msg)
}

// SendRaw envoie des données OSC brutes
func (b *RawOscBridge) SendRaw(data []byte) error {
	_, err := b.conn.WriteToUDP(data, b.remoteAddr)
	return err
}

// appendSafely ajoute un argument au message OSC avec gestion d'erreur
func (b *RawOscBridge) appendSafely(msg *osc.Message, arg interface{}) error {
	if arg == nil {
		return msg.Append(nil)
	}

	// Gérer différents types d'arguments
	switch v := arg.(type) {
	case int:
		return msg.Append(int32(v))
	case int8:
		return msg.Append(int32(v))
	case int16:
		return msg.Append(int32(v))
	case int32:
		return msg.Append(v)
	case int64:
		return msg.Append(v)
	case float32:
		return msg.Append(v)
	case float64:
		return msg.Append(v)
	case string:
		return msg.Append(v)
	case bool:
		return msg.Append(v)
	case []byte:
		return msg.Append(v)
	case []interface{}:
		// Pour les tableaux, nous allons créer un tableau OSC
		// La bibliothèque go-osc ne supporte pas directement les tableaux OSC
		// Nous allons donc créer un tableau OSC manuellement

		// Créer un nouveau message OSC pour le tableau
		arrayMsg := osc.NewMessage(msg.Address)

		// Ajouter chaque élément du tableau
		for _, elem := range v {
			if err := b.appendSafely(arrayMsg, elem); err != nil {
				return err
			}
		}

		// Ajouter le tableau comme un blob
		return msg.Append(v)
	case []int:
		// Convertir []int en []interface{} et utiliser le cas ci-dessus
		interfaceSlice := make([]interface{}, len(v))
		for i, val := range v {
			interfaceSlice[i] = val
		}
		return b.appendSafely(msg, interfaceSlice)
	case []float64:
		// Convertir []float64 en []interface{} et utiliser le cas ci-dessus
		interfaceSlice := make([]interface{}, len(v))
		for i, val := range v {
			interfaceSlice[i] = val
		}
		return b.appendSafely(msg, interfaceSlice)
	case []string:
		// Convertir []string en []interface{} et utiliser le cas ci-dessus
		interfaceSlice := make([]interface{}, len(v))
		for i, val := range v {
			interfaceSlice[i] = val
		}
		return b.appendSafely(msg, interfaceSlice)
	default:
		// Convertir en string pour tout autre type
		return msg.Append(fmt.Sprintf("%v", v))
	}
}

// Close ferme proprement les ressources
func (b *RawOscBridge) Close() error {
	// Signaler l'arrêt
	close(b.stopChan)

	// Fermer la connexion UDP
	if b.conn != nil {
		return b.conn.Close()
	}
	return nil
}
