package communication

import (
	"bufio"
	"fmt"
	"log"
	"strings"
	"sync"
	"time"

	"go.bug.st/serial"
	"go.bug.st/serial/enumerator"
)

// SerialManager gère la communication série
type SerialManager struct {
	*EventEmitter
	baudRate         int
	serialPort       serial.Port
	isInitialized    bool
	messageHandler   func(message string)
	connectionTested bool
	serOKTicker      *time.Ticker
	testTicker       *time.Ticker
	stopTicker       chan struct{}
	lastReceivedMsg  string
	lastMessageTime  time.Time
	scannerCloseChan chan struct{}
	scannerCloseWait sync.WaitGroup
}

// NewSerialManager crée une nouvelle instance de SerialManager
func NewSerialManager(baudRate int) *SerialManager {
	if baudRate == 0 {
		baudRate = 921600 // Valeur par défaut comme dans la version JS
	}

	return &SerialManager{
		EventEmitter:     NewEventEmitter(),
		baudRate:         baudRate,
		isInitialized:    false,
		stopTicker:       make(chan struct{}),
		scannerCloseChan: make(chan struct{}),
	}
}

// InitializeSerialPort initialise le port série
func (sm *SerialManager) InitializeSerialPort() (bool, error) {
	log.Println("Initialisation du port série...")

	// Obtenir la liste des ports disponibles
	ports, err := enumerator.GetDetailedPortsList()
	if err != nil {
		return false, fmt.Errorf("erreur lors de l'énumération des ports série : %w", err)
	}

	log.Printf("Ports série disponibles : %v", getPortNames(ports))

	// Chercher un port compatible
	var targetPort string
	for _, port := range ports {
		if strings.Contains(port.Name, "wchusbserial") || strings.Contains(port.SerialNumber, "wchusbserial") {
			targetPort = port.Name
			break
		}
	}

	if targetPort == "" {
		log.Println("Aucun port série compatible trouvé.")
		return false, nil
	}

	log.Printf("Port série cible trouvé : %s", targetPort)

	// Configurer le port série
	mode := &serial.Mode{
		BaudRate: sm.baudRate,
	}

	port, err := serial.Open(targetPort, mode)
	if err != nil {
		return false, fmt.Errorf("erreur d'ouverture du port série : %w", err)
	}

	sm.serialPort = port
	sm.isInitialized = true

	// Configurer les gestionnaires d'événements
	sm.setupListeners()

	// Démarrer l'envoi périodique de SerOK
	sm.startSerOKInterval()

	log.Println("Port série ouvert avec succès")
	return true, nil
}

// getPortNames extrait les noms des ports d'une liste de ports détaillés
func getPortNames(ports []*enumerator.PortDetails) []string {
	names := make([]string, len(ports))
	for i, port := range ports {
		names[i] = port.Name
	}
	return names
}

// setupListeners configure les écouteurs d'événements pour le port série
func (sm *SerialManager) setupListeners() {
	sm.scannerCloseWait.Add(1)

	// Démarrer une goroutine pour lire les données du port série
	go func() {
		defer sm.scannerCloseWait.Done()

		scanner := bufio.NewScanner(sm.serialPort)

		// Canal pour indiquer la fin du scanner
		done := make(chan struct{})

		// Goroutine pour surveiller le canal de fermeture
		go func() {
			select {
			case <-sm.scannerCloseChan:
				// Fermer le port série pour interrompre le scanner
				sm.serialPort.Close()
				close(done)
			}
		}()

		// Boucle de lecture des données
		for scanner.Scan() {
			select {
			case <-done:
				return
			default:
				message := strings.TrimSpace(scanner.Text())
				sm.lastReceivedMsg = message
				sm.lastMessageTime = time.Now()

				sm.Emit("message", []interface{}{message})

				if sm.isInitialized {
					if message == "SerETest" {
						sm.sendMessage("SerOK")
					} else if sm.messageHandler != nil {
						sm.messageHandler(message)
					}
				} else {
					log.Printf("Message reçu avant l'initialisation complète : %s", message)
				}
			}
		}

		// Gestion des erreurs du scanner
		if err := scanner.Err(); err != nil {
			log.Printf("Erreur du scanner série : %v", err)
		}

		// Émettre un événement de déconnexion
		sm.isInitialized = false
		sm.clearSerOKInterval()
		sm.Emit("disconnected", nil)
	}()
}

// startSerOKInterval démarre l'envoi périodique de SerOK
func (sm *SerialManager) startSerOKInterval() {
	sm.serOKTicker = time.NewTicker(9500 * time.Millisecond)

	go func() {
		for {
			select {
			case <-sm.serOKTicker.C:
				sm.sendMessage("SerOK")
			case <-sm.stopTicker:
				return
			}
		}
	}()

	log.Println("Envoi périodique de 'SerOK' toutes les 9500 ms activé.")
}

// clearSerOKInterval arrête l'envoi périodique de SerOK
func (sm *SerialManager) clearSerOKInterval() {
	if sm.serOKTicker == nil {
		return
	}

	// Arrêter les tickers s'ils existent
	if sm.serOKTicker != nil {
		sm.serOKTicker.Stop()
		sm.serOKTicker = nil
		log.Println("Envoi périodique de 'SerOK' désactivé.")
	}

	// Fermer et recréer le canal d'arrêt
	select {
	case <-sm.stopTicker:
		// Le canal est déjà fermé, ne rien faire
	default:
		close(sm.stopTicker)
	}
	sm.stopTicker = make(chan struct{})
}

// ReadMessage lit un message du port série
func (sm *SerialManager) ReadMessage() (string, error) {
	if !sm.isInitialized {
		return "", fmt.Errorf("port série non initialisé")
	}

	// Créer un buffer pour lire une ligne
	reader := bufio.NewReader(sm.serialPort)
	message, err := reader.ReadString('\n')
	if err != nil {
		return "", err
	}

	return strings.TrimSpace(message), nil
}

// SendMessage envoie un message via le port série (version publique)
func (sm *SerialManager) SendMessage(message string) error {
	return sm.sendMessage(message)
}

// sendMessage envoie un message via le port série (version privée)
func (sm *SerialManager) sendMessage(message string) error {
	if !sm.isInitialized {
		log.Println("Impossible d'envoyer le message : port série non initialisé")
		return fmt.Errorf("port série non initialisé")
	}

	// Ajouter une nouvelle ligne à la fin du message
	messageWithNewline := message + "\n"

	_, err := sm.serialPort.Write([]byte(messageWithNewline))
	if err != nil {
		log.Printf("Erreur d'envoi du message : %v", err)
		return err
	}

	log.Printf("Message envoyé (série) : %s à %s", message, time.Now().Format(time.RFC3339Nano))
	return nil
}

// SetMessageHandler définit le gestionnaire de messages
func (sm *SerialManager) SetMessageHandler(handler func(message string)) {
	sm.messageHandler = handler
}

// IsConnected vérifie si le port série est connecté
func (sm *SerialManager) IsConnected() bool {
	return sm.isInitialized && sm.serialPort != nil
}

// Close ferme proprement le port série
func (sm *SerialManager) Close() error {
	if !sm.isInitialized {
		return nil
	}

	// Arrêter d'abord l'intervalle SerOK pour éviter de nouveaux envois
	sm.clearSerOKInterval()

	// Marquer comme non initialisé avant de fermer pour éviter les doubles appels
	sm.isInitialized = false

	if sm.serialPort != nil {
		// Arrêter la goroutine de lecture
		close(sm.scannerCloseChan)

		// Attendre que la goroutine de lecture se termine
		sm.scannerCloseWait.Wait()

		// Fermer le port série
		err := sm.serialPort.Close()
		sm.serialPort = nil

		// Émettre un événement de déconnexion
		sm.Emit("disconnected", nil)

		log.Println("Port série fermé proprement")
		return err
	}

	return nil
}
