# Guide consolidé de conversion JS → Go : Live Volume Mode

**Date:** 2025-04-24 (<PERSON><PERSON><PERSON> sur les informations fournies)

## 1. Introduction et objectif

Ce document fusionne les informations de deux guides précédents pour détailler la transition de `liveVolumeMode.js` (JavaScript) vers `liveVolumeMode.go` (Go). Il expose les concepts clés, les structures de projet et les bonnes pratiques, servant de modèle pour convertir d'autres modes similaires.

## 2. Structure du projet : JS vs. Go

| JS (Ancien)                                        | Go (Nouveau)                                                                  | Description                                             |
| :------------------------------------------------- | :---------------------------------------------------------------------------- | :------------------------------------------------------ |
| `liveVolumeMode.js`                                | `liveVolumeMode.go`                                                           | Logique principale du mode.                               |
| `VolumeModeClasses/VolumeModeDisplayManager.js`    | `liveVolume_displayManager.go`                                                | Gestion de l'affichage spécifique au mode.               |
| `liveUtilityClasses/liveVolumeConverter.js`        | `oscbridge/Live/utils/volumeConverter.go`                                      | Utilitaire de conversion pour le volume.                |
| `liveUtilityClasses/liveVolumeSendConverter.js`    | `oscbridge/Live/utils/volumeSendConverter.go`                                  | Utilitaire de conversion pour les sends.                  |
| Configuration & Constantes JS (par ex. `CONFIG`) | `liveVolume_types.go`                                                         | Constantes (adresses OSC, limites) et types Go.      |
| (Impliqué)                                         | `liveVolume_oscHandlers.go`, `liveVolume_hardwareHandlers.go`, `liveVolume_pageHandlers.go` | Séparation des gestionnaires d'événements (handlers). |

## 3. Concepts clés de la transition JS → Go

### 3.1 Mapping Classe → Struct + Méthodes

* **JS:** `class LiveVolumeMode extends EventEmitter`
* **Go:** `type LiveVolumeMode struct` qui *embed* (intègre) `*communication.BaseMode`. `BaseMode` fournit l'infrastructure OSC de base.
* **Constructeur:**
    * JS: `new LiveVolumeMode(oscHandler, liveTrackManager, communicationManager, sharedState)`
    * Go: Fonction `NewLiveVolumeMode(trackManager *LiveTrackManager, commManager *CommunicationManager)` qui retourne un pointeur vers la struct initialisée.
* **Méthodes:** Les méthodes de classe JS (camelCase) deviennent des méthodes associées à la struct Go (PascalCase, avec un *receiver* explicite, ex: `func (m *LiveVolumeMode) Start()`).

### 3.2 Gestion de l'état (`state`)

* **JS:** Objet `this.state` souvent non typé ou peu structuré.
    Ex: `{ isActive, currentPage, isLocked, subMode, availableSubmodes }`
* **Go:** Struct typée dédiée, définie dans `liveVolume_types.go`.
    ```go
    // liveVolume_types.go
    type LiveVolumeModeState struct {
        IsLocked          bool
        SubMode           string
        AvailableSubmodes []string
        CurrentPage       int
        // Potentiellement d'autres champs selon le mode
    }
    ```
    L'état (`*LiveVolumeModeState`) est souvent injecté dans les composants qui en ont besoin (comme le `DisplayManager`) pour maintenir une source unique de vérité.

### 3.3 Gestion des événements (OSC, Hardware)

| JS                                         | Go                                                      | Notes                                                                                             |
| :----------------------------------------- | :------------------------------------------------------ | :------------------------------------------------------------------------------------------------ |
| `oscHandler.on(address, handler)`          | `m.BaseMode.RegisterHandler(address, handler)`          | Utilise l'infrastructure de `BaseMode`.                                                           |
| `oscHandler.removeListener(address, ...)`  | `m.BaseMode.UnregisterHandler(address)`                 | Nettoyage des handlers.                                                                           |
| Stockage interne (ex: `Map` ou `Object`)   | `backgroundHandlers map[string]HandlerFunc`             | Séparation possible entre handlers actifs uniquement lorsque le mode est actif et ceux en arrière-plan. |
| Méthodes (`start`, `stopBackground`, etc.) | Méthodes (`Start`, `StopBackground`, `Activate`, etc.) | Conventions de nommage Go (PascalCase).                                                           |

### 3.4 Données des pistes (`TracksData`)

* **JS:** `Map<number, Object>` ou structure similaire, souvent avec des objets non typés.
* **Go:** `map[int]*TrackData` où `TrackData` est une struct définie dans `*_types.go`, assurant un typage fort.

## 4. Display Manager (`*_displayManager.go`)

* **Responsabilité:** Gérer la logique d'affichage (mise à jour des slots, pages, couleurs) et la communication associée.
* **Injection de dépendances:** Reçoit les dépendances nécessaires via son constructeur `New...DisplayManager`.
    ```go
    func NewLiveVolumeDisplayManager(
        commManager *communication.CommunicationManager,
        trackManager *live.LiveTrackManager, // Ou interface équivalente
        state *LiveVolumeModeState,
        parentMode *LiveVolumeMode, // Ou interface pour IsActive() etc.
    ) *LiveVolumeDisplayManager
    ```
* **Méthodes principales (Exemples):**

    | JS                                       | Go                                                                 |
    | :--------------------------------------- | :----------------------------------------------------------------- |
    | `updateSlotForMode(letter, data, sub)`   | `UpdateSlotForMode(slotLetter string, trackData *TrackData, sub)` |
    | `clearSlot(letter)`                      | `ClearSlot(slotLetter string)`                                     |
    | `updatePage(page)`                       | `UpdatePage(page int)`                                             |
    | `updateSelectedTrack(letter)`            | `UpdateSelectedTrack(slotLetter string)`                           |
    | `updateColor(letter, color)`             | `UpdateColor(slotLetter, color string)`                            |
    | `formatTrackData(...)`                   | `formatTrackData(trackData *TrackData, subMode string) string`     |

## 5. Formatage des données et messages

* **Messages OSC/Hardware:**
    * JS: Template literals (ex: `` `sl${slot}${payload}` ``).
    * Go: `fmt.Sprintf` (ex: `fmt.Sprintf("sl%s%s", slot, payload)`). Standardiser les préfixes (`sl`, `vo`, `pa`, `co`, etc.).
* **Conversion de couleur:**
    * JS: Peut manipuler directement des valeurs décimales issues d'hexadécimal.
    * Go: Nécessite souvent de parser explicitement une chaîne hexadécimale en décimal: `fmt.Sscanf(hexString, "%x", &colorDec)`.
* **Nombres:**
    * JS: `Math.round`.
    * Go: `math.Round` (nécessite un cast explicite vers le type entier désiré ensuite, ex: `int(math.Round(floatValue))`).
* **Formatage spécifique (`formatTrackData`):**
    * Logique d'extraction des champs de `TrackData` similaire.
    * Conversion Pan `[-1, 1]` vers `[0, 100]` identique.
    * Assemblage final de la chaîne avec `fmt.Sprintf`.

## 6. Utilitaires de conversion (`Live/utils`)

* Les logiques de conversion spécifiques (ex: dB <-> float) sont extraites dans des packages dédiés (`oscbridge/Live/utils`).
* JS: `new LiveVolumeConverter().toDb(value)`
* Go: `utils.NewVolumeConverter().ToDb(value)` (ou fonctions package si pas d'état nécessaire).

## 7. Mise en cache et déduplication

* **Objectif:** Éviter l'envoi redondant de commandes OSC/hardware identiques pour améliorer les performances.
* **JS:** Utilisation de `Map` pour stocker le dernier message envoyé pour chaque slot/clé.
* **Go:** Utilisation de `map[string]string` (ou type plus spécifique).
    ```go
    // Dans DisplayManager ou Mode
    slotsCache map[string]string

    // Lors de l'envoi
    messageKey := fmt.Sprintf("sl%s", slotLetter) // Clé unique par slot
    newMessage := formatTrackData(...)
    if cachedMessage, found := m.slotsCache[messageKey]; !found || cachedMessage != newMessage {
        m.commManager.SendMessage(fmt.Sprintf("sl%s%s", slotLetter, newMessage))
        m.slotsCache[messageKey] = newMessage
    }
    ```

## 8. Concurrence, Mutex et Goroutines

* **JS:** Essentiellement mono-thread. La concurrence n'est pas une préoccupation majeure pour l'accès aux données partagées.
* **Go:** Multi-thread par nature (via goroutines). L'accès concurrentiel aux données partagées (maps, slices, état) DOIT être protégé par des `sync.Mutex`.
* **Cas spécifique LiveVolumeMode (Buffer de Structure):**
    * Ce mode reçoit des données de structure (`/structure/begin`, `/structure/track`, `/structure/end`) en plusieurs messages OSC pour reconstruire `tracksData`.
    * Nécessite un buffer temporaire (`pendingTracksData`) et un mutex (`structureMutex`) pour gérer l'assemblage atomique des données reçues de manière asynchrone.
        ```go
        type LiveVolumeMode struct {
            // ...
            pendingTracksData map[int]*TrackData
            structureMutex    sync.Mutex
            // ...
        }

        // Dans onStructureTrack:
        m.structureMutex.Lock()
        m.pendingTracksData[trackID] = parsedTrackData
        m.structureMutex.Unlock()

        // Dans onStructureEnd:
        m.structureMutex.Lock()
        m.tracksData = m.pendingTracksData // Échange
        m.pendingTracksData = make(map[int]*TrackData) // Réinitialisation
        m.structureMutex.Unlock()
        // Mettre à jour l'affichage...
        ```
    * **Important:** Ce mécanisme de buffer de structure est spécifique au `LiveVolumeMode` et ne doit *pas* être appliqué aveuglément aux autres modes s'ils ne reçoivent pas de données fragmentées de cette manière.

## 9. Pagination et verrouillage (`Lock`)

* La logique de changement de page (`currentPage`) et de verrouillage (`isLocked`) est gérée dans l'état (`LiveVolumeModeState`).
* **Délais/Debouncing:** Si un délai est nécessaire (ex: JS `setTimeout` pour debouncer le changement de page), Go peut utiliser `time.Sleep` dans une goroutine ou des mécanismes plus complexes avec `time.Ticker` ou `time.Timer` si nécessaire. Une struct `PageChangeState` peut aider à gérer l'état lié à la pagination.

## 10. Patterns récurrents et Bonnes pratiques pour les conversions futures

1.  **Structure de Fichiers:** Adopter une structure cohérente par mode :
    * `live<ModeName>Mode.go` (logique principale)
    * `live<ModeName>_displayManager.go` (affichage)
    * `live<ModeName>_types.go` (constantes, types spécifiques au mode: `TrackData`, `ModeState`)
    * `live<ModeName>_oscHandlers.go` (handlers OSC)
    * `live<ModeName>_hardwareHandlers.go` (handlers hardware)
    * `live<ModeName>_pageHandlers.go` (si logique de pagination complexe)
2.  **Typage Fort:** Définir des `struct` claires pour toutes les données manipulées (état, données de piste, buffers). Utiliser les types Go (int, float64, bool, string, etc.).
3.  **Séparation des préoccupations:** Isoler la logique du mode, de l'affichage, et de la communication.
4.  **Injection de Dépendances:** Passer les dépendances (managers, état) via les constructeurs (`New...`).
5.  **Gestion Explicite de la Concurrence:** Utiliser `sync.Mutex` pour protéger *toute* donnée partagée accessible depuis différentes goroutines (handlers OSC, timers, etc.).
6.  **`BaseMode`:** Intégrer (`embed`) `BaseMode` pour réutiliser l'enregistrement des handlers OSC et la gestion du cycle de vie.
7.  **Formatage:** Utiliser `fmt.Sprintf` pour construire les messages sortants.
8.  **Utilitaires:** Placer la logique de conversion réutilisable dans le package `Live/utils`.
9.  **Mise en Cache:** Implémenter un cache dans le `DisplayManager` pour éviter l'envoi redondant de messages.
10. **Logging:** Remplacer `console.log` par le package `log` de Go (`log.Printf`, `log.Println`).
11. **Conventions Go:** Utiliser `PascalCase` pour les noms exportés (structs, méthodes, constantes), `camelCase` pour les variables locales/non exportées. Donner des noms clairs et descriptifs.

## 11. Conclusion

La conversion de JavaScript vers Go pour les modes Live implique principalement :

* L'adoption du **typage statique et fort** de Go via les `struct`.
* Le passage d'un modèle basé sur les classes/prototypes à des **`struct` avec méthodes associées**.
* La **gestion explicite de la concurrence** avec les `mutex`.
* La **réutilisation d'une infrastructure commune** (`BaseMode`, `CommunicationManager`).
* Une **structuration de code plus modulaire** par fonctionnalité (handlers, display, types).

En suivant les patterns et bonnes pratiques décrits dans ce guide, la conversion des autres modes (Pan, Sends, Learn, etc.) devrait être plus systématique, cohérente et aboutir à un code Go plus robuste et maintenable.