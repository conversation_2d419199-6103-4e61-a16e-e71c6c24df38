const EventEmitter = require("events");
const EncoderController = require("../Comm/encoderController");
const DeviceParameterManager = require("./DeviceModeClasses/DeviceParameterManager");
const DevicePropertyManager = require("./DeviceModeClasses/DevicePropertyManager");

class LiveDeviceMode extends EventEmitter {
  constructor(osc<PERSON><PERSON><PERSON>, liveTrackManager, communicationManager, sharedState) {
    super();
    this.oscHandler = oscHandler;
    this.liveTrackManager = liveTrackManager;
    this.communicationManager = communicationManager;
    this.state = sharedState.device;
    this.globalState = sharedState.global;

    // Initialisation du state
    this.state.path = [];
    this.state.submode = 0;
    this.state.isDrumRack = false;

    // Initialisation des managers avec configuration cohérente
    this.parameterManager = new DeviceParameterManager({
      oscHand<PERSON>,
      communicationManager,
      state: this.state
    });

    this.propertyManager = new DevicePropertyManager({
      osc<PERSON><PERSON><PERSON>,
      communication<PERSON>ana<PERSON>,
      liveTrackManager,
      state: this.state
    });

    this.currentPage = 1;
    this.slotsPerPage = 8;

    // Initialisation des managers
    this.encoderController = new EncoderController();

    this.listenAddresses = [
      "/live/device/get/parameter/value",
      "/live/device/get/isActive",
      "/live/deviceMode/get/parameters/bulk",
      "/live/track/get/devices/name",
      "/live/chain/get/parameters/bulk",

      "/live/device_unlock",
      "/live/device_lock_reference",
      "/live/device/get/is_collapsed",
      "/live/device/get/can_have_chains",
      "/live/deviceMode/get/environment/",
      "/live/deviceMode/selTrack/properties",
      "/live/deviceMode/get/path_string",
      "/live/chain/get/volume",
      "/live/chain/get/pan",
      "/live/chain/get/mute",
      "/live/chain/get/solo",
    ];

    // Lier les méthodes dans le constructeur
    this._boundHandleOscMessage = this.handleOscMessage.bind(this);
    this._boundSelectedTrackDeviceChange = this.onSelectedTrackDeviceChange.bind(this);
    this._boundEncoderChange = this.onEncoderChanged.bind(this);
    this._boundButtonPressed = this.onButtonPressed.bind(this);
    this._boundTouchPressed = this.onTouched.bind(this);
  }

  backgroundStart() {
    console.log("\n=== Starting DeviceMode background listeners ===");

    this.listenAddresses.forEach((address) => {
      this.oscHandler.on(address, (args) => this._boundHandleOscMessage(args, address));
    });

    this.liveTrackManager.on("selectedTrackDeviceUpdate", this._boundSelectedTrackDeviceChange);
    if (this.liveTrackManager.devicesCount === 0 && !this.state.isLocked) {
      this.communicationManager.sendMessage("di,0,nul,0,nul,0,nul,0,nul,0,nul,0,nul,0,nul,0,nul", this.state.isActive);
      this.communicationManager.sendMessage("dd,0,-1", this.state.isActive);
    }
    this.disableLock();
  }

  start() {
    try {
      console.log("Starting DeviceMode");
      this.state.isActive = true;

      // Ajouter uniquement les listeners d'interaction utilisateur
      this.communicationManager.on("encoderChange", this._boundEncoderChange);
      this.communicationManager.on("buttonPressed", this._boundButtonPressed);
      this.communicationManager.on("touchPressed", this._boundTouchPressed);

      // Basculer l'affichage en mode device
      this.communicationManager.sendMessage("mo,2", this.state.isActive);

    } catch (error) {
      console.error("Erreur dans start():", error);
    }
  }

  async cleanup() {
    console.log("Starting cleanup in DeviceMode");
    this.state.isActive = false;

    // Nettoyer les listeners d'interaction utilisateur
    this.communicationManager.removeListener("encoderChange", this._boundEncoderChange);
    this.communicationManager.removeListener("buttonPressed", this._boundButtonPressed);
    this.communicationManager.removeListener("touchPressed", this._boundTouchPressed);
  }

  async stopBackground() {
    console.log("\n=== Stopping DeviceMode background listeners ===");

    // Nettoyer les listeners OSC
    this.listenAddresses.forEach((address) => {
      this.oscHandler.removeAllListeners(address);
    });

    // Nettoyer les listeners de base
    this.liveTrackManager.removeListener("selectedTrackDeviceUpdate", this._boundSelectedTrackDeviceChange);

    await this.oscHandler.sendOscMessage("/live/deviceMode/stop");
  }

  async cleanupForExit() {
    await this.cleanup();
    await this.stopBackground();
  }

  async cleanupForSwitch() {
    await this.cleanup();
    if (this.state.isLocked) {
      console.log(`[LOCK] Sauvegarde de parametersCount: ${this.liveTrackManager.parametersCount} dans lockedParametersCount`);
      this.state.lockedParametersCount = this.liveTrackManager.parametersCount;
    } else {
      console.log(`[UNLOCK] Stop sans sauvegarde de parametersCount (${this.liveTrackManager.parametersCount})`);
    }
  }

  handleOscMessage(args, address) {
    const messageKey = `${address}-${args.join("-")}`;
    if (this.lastProcessedMessage === messageKey) {
      console.log("Message already processed, skipping");
      return;
    }
    this.lastProcessedMessage = messageKey;

    if (address === "/live/device_unlock") {
      this.disableLock();
      console.log("Device unlocked");
      return;
    }

    // Nouvelles adresses pour les paramètres groupés
    if (address === "/live/deviceMode/get/parameters/info") {
      const parameterValues = args;
      const messageType = address.split('/').pop();
      this.parameterManager.handleBulkParameters(messageType, parameterValues, this.state.isLocked);
      return;
    }

    if (address === "/live/deviceMode/get/environment/") {
      this.propertyManager.handleEnvironment(args);
      return;
    }

    if (address === "/live/device/get/parameter/value") {
      this.parameterManager.handleParameterMessage(
        address,
        args,
        this.currentPage,
        this.slotsPerPage,
        this.state.isLocked
      );
    }

    if (address === "/live/device_lock_reference") {
      const [deviceName, trackName, trackColor] = args;
      this.communicationManager.sendMessage(`ld${deviceName}`, this.state.isActive);
      this.communicationManager.sendMessage(`dp,${trackName},${trackColor}`, this.state.isActive);
      return;
    }

    if (address === "/live/deviceMode/selTrack/properties") {
      const [trackName, trackColor] = args;
      this.communicationManager.sendMessage(`dp,${this.shortString(trackName)},${trackColor}`, this.state.isActive);
      return;
    }

    if (address === "/live/device/get/isActive") {
      this.propertyManager.handleIsActive(args);
      return;
    }

    // Ajout du nouveau type de message pour les paramètres groupés
    if (address === "/live/deviceMode/get/parameters/bulk") {
      this.parameterManager.handleBulkDeviceParameters(args);
      return;
    }

    if (address === "/live/chain/get/parameters/bulk") {
      this.parameterManager.handleBulkChainParameters(args);
      return;
    }

    if (address.startsWith("/live/chain/get/")) {
      this.parameterManager.handleChainParameterMessage(address, args);
      return;
    }

    if (address === "/live/deviceMode/get/path_string") {
      this.propertyManager.handlePathString(args);
      return;
    }

    return;
  }

  onEncoderChanged(encoderIndex, direction) {
    if (encoderIndex < 8) {
      this.handleDeviceParameterEncoder(encoderIndex, direction);
    } else {
      this.handleTrackParameterEncoder(encoderIndex, direction);
    }
  }

  handleDeviceParameterEncoder(encoderIndex, direction) {
    const startParam = (this.currentPage - 1) * this.slotsPerPage + 1;
    const parameterIndex = startParam + encoderIndex;

    if (parameterIndex >= this.liveTrackManager.parametersCount) {
      console.log(
        `L'encodeur ${encoderIndex} n'a pas de paramètre associé sur cette page`
      );
      return;
    }

    const paramInfo = this.parameterManager.getParameterInfo(parameterIndex, this.state.isLocked);
    if (!paramInfo) {
      console.log(
        `Aucune information de paramètre trouvée pour l'index ${parameterIndex}`
      );
      return;
    }

    const { isQuantized, minValue, maxValue, currentValue } = paramInfo;
    const key = `${this.liveTrackManager.selectedTrack}-${this.liveTrackManager.selectedDevice}-${parameterIndex}`;

    if (!isQuantized) {
      const minStep = (maxValue - minValue) / 1000;
      const maxStep = (maxValue - minValue) / 40;
      const newValue = this.encoderController.updateParameter(
        key,
        direction,
        currentValue,
        minValue,
        maxValue,
        minStep,
        maxStep
      );
      this.parameterManager.sendParameterUpdate(
        this.liveTrackManager.selectedTrack,
        this.liveTrackManager.selectedDevice,
        parameterIndex,
        newValue
      );
      paramInfo.currentValue = newValue;
    } else {
      this.handleQuantizedParameter(paramInfo, direction, parameterIndex);
    }

    this.parameterManager.updateParameter(parameterIndex, paramInfo);
  }

  handleQuantizedParameter(paramInfo, direction, parameterIndex) {
    if (!paramInfo.hasOwnProperty("buffer")) {
      paramInfo.buffer = 0;
      paramInfo.lastDirection = 0;
    }

    if (direction !== paramInfo.lastDirection) {
      paramInfo.buffer = 0;
    }

    const bufferStep = 0.25;
    paramInfo.buffer += direction * bufferStep;

    if (Math.abs(paramInfo.buffer) >= 1) {
      const change = Math.sign(paramInfo.buffer);
      const newValue = Math.max(
        paramInfo.minValue,
        Math.min(paramInfo.maxValue, paramInfo.currentValue + change)
      );

      if (newValue !== paramInfo.currentValue) {
        this.parameterManager.sendParameterUpdate(
          this.liveTrackManager.selectedTrack,
          this.liveTrackManager.selectedDevice,
          parameterIndex,
          newValue
        );
        paramInfo.currentValue = newValue;
      }

      paramInfo.buffer -= change;
    }

    paramInfo.lastDirection = direction;
  }

  onButtonPressed(buttonIndex) {
    switch (buttonIndex) {
      case 0:
        this.pageDown();
        break;
      case 1:
        this.pageUp();
        break;
      case 2:
        if (this.state.isLocked) {
          this.disableLock();
        } else {
          this.enableLock();
        }
        break;
      case 3:
        const startTrack = (this.currentPage - 1) * this.slotsPerPage;
        const thirdTrackIndex = startTrack + 2;
        this.setSelectedTrack(thirdTrackIndex);
        break;
    }
  }

  setPage(page) {
    if (this.parameterManager.setPage(page, this.liveTrackManager.parametersCount, this.slotsPerPage, this.state.isLocked)) {
      this.currentPage = page;
      const totalPages = Math.ceil((this.liveTrackManager.parametersCount - 1) / this.slotsPerPage);
      console.log(`Switched to page ${this.currentPage}/${totalPages}`);
      this.communicationManager.sendMessage(`pd${this.currentPage}/${totalPages}`, this.state.isActive);
    }
  }

  pageUp() {
    this.setPage(this.currentPage + 1);
  }

  pageDown() {
    this.setPage(this.currentPage - 1);
  }

  onSelectedTrackDeviceChange(selectedTrack, selectedDevice, devicesCount, parametersCount) {
    //this.propertyManager.displayDevices(this.liveTrackManager.selectedDevice);
    this.currentPage = 1;
    const totalPages = Math.ceil((this.liveTrackManager.parametersCount - 1) / this.slotsPerPage);
    console.log(`Switched to page ${this.currentPage}/${totalPages}`);
    this.communicationManager.sendMessage(`pd${this.currentPage}/${totalPages}`, this.state.isActive);

    // Réinitialisation des états de devices pour éviter des comportements indéfinis
    this.propertyManager.lastDevicesState = null;
    this.propertyManager.lastRackDevicesState = null;

    if (this.liveTrackManager.devicesCount === 0 && !this.state.isLocked) {
      this.communicationManager.sendMessage("di,0,nul,0,nul,0,nul,0,nul,0,nul,0,nul,0,nul,0,nul", this.state.isActive);
      this.communicationManager.sendMessage("dd,0,-1", this.state.isActive);
      this.communicationManager.sendMessage(`pd,-1`, this.state.isActive);
      this.communicationManager.sendMessage(`ds,no device.`, this.state.isActive);
    }
  }

  enableLock() {
    console.log(`[LOCK] Activation du lock:`, {
      currentParametersCount: this.liveTrackManager.parametersCount,
      selectedDevice: this.liveTrackManager.selectedDevice,
      selectedTrack: this.liveTrackManager.selectedTrack
    });

    this.state.isLocked = true;
    this.state.lockedDevice = this.liveTrackManager.selectedDevice;
    this.state.lockedTrack = this.liveTrackManager.selectedTrack;

    this.oscHandler.sendOscMessage("/live/device_lock", "1");
  }

  disableLock() {
    console.log(`[UNLOCK] Désactivation du lock:`, {
      currentParametersCount: this.liveTrackManager.parametersCount,
      lockedParametersCount: this.state.lockedParametersCount
    });

    this.state.isLocked = false;
    this.state.lockedDevice = null;
    this.state.lockedTrack = null;
    this.state.lockedParametersCount = null;
    this.communicationManager.sendMessage("ld00", this.state.isActive);
    this.oscHandler.sendOscMessage("/live/device_lock", "0");

    this.parameterManager.handleUnlock();
  }

  setSelectedDevice(deviceIndex) {
    if (deviceIndex >= 0 && deviceIndex < this.liveTrackManager.devicesCount) {
      this.state.keepDisplayPosition = true;
      this.oscHandler.sendOscMessage("/live/view/set/selected_device", [
        this.liveTrackManager.selectedTrack,
        deviceIndex,
      ]);
      console.log(
        `Sélection du device ${deviceIndex} sur la piste ${this.liveTrackManager.selectedTrack}, keepDisplayPosition: ${this.state.keepDisplayPosition}`
      );
    } else {
      console.log(`Index de device invalide: ${deviceIndex}`);
    }
  }

  setSelectedTrack(trackIndex) {
    this.oscHandler.sendOscMessage("/live/view/set/selected_track", [
      trackIndex,
    ]);
  }

  onTouched(type, index, index2) {
    if (type === "mv") {
      console.log(`Move touch event avec index1=${index} et index2=${index2}`);

      let moveParams;

      // Si on est au niveau de la piste principale (path vide)
      if (this.state.path.length === 0) {
        moveParams = [
          index,          // Index du device source
          -1,             // Pas de rack/chain source (-1 pour indiquer la piste principale)
          -1,             // Pas de rack/chain cible (-1 pour indiquer la piste principale)
          index2          // Position cible
        ];
      } else {
        // Si on est dans un rack/chain
        if (this.state.submode === 0) { // Mode chain
          // Dans le mode chain, on ajoute l'index au path actuel
          const sourcePath = [...this.state.path, index];
          const targetPath = [...this.state.path, index2];

          moveParams = [
            ...sourcePath,      // Chemin source complet
            ...targetPath       // Chemin cible avec la position finale
          ];
        } else { // Mode device
          // Dans le mode device, on utilise le path actuel sans le dernier élément
          const basePath = this.state.path.slice(0, -1); // Enlève le dernier élément du path
          moveParams = [
            ...basePath, index,    // Chemin source (path + device source)
            ...basePath, index2    // Chemin cible (même path + position cible)
          ];
        }
      }

      console.log('Sending move device command:', {
        params: moveParams,
        path: this.state.path,
        submode: this.state.submode
      });

      this.oscHandler.sendOscMessage("/live/device/move_by_path", moveParams);
    } else if (type === "sd") {
      if (index === this.state.path[0]) {
        this.oscHandler.sendOscMessage("/live/select/chain", [index, 0]);
      } else {
        this.oscHandler.sendOscMessage("/live/select/device", [index]);
      }
    } else if (type === "rd") {
      console.log('Current path for rd:', this.state.path);

      if (this.state.submode === 1 && index === this.state.path[this.state.path.length - 1]) {
        // Si on est en mode device ET l'index correspond au dernier élément
        const newPath = [...this.state.path, 0];
        console.log('rd: sending chain path:', newPath);
        this.oscHandler.sendOscMessage("/live/select/chain", newPath);
      } else {
        if (this.state.submode === 0) {
          const newPath = [...this.state.path, index];
          console.log('rd: sending path (chain mode):', newPath);
          this.oscHandler.sendOscMessage("/live/select/device", newPath);
        } else {
          const newPath = [...this.state.path.slice(0, -1), index];
          console.log('rd: sending path (device mode):', newPath);
          this.oscHandler.sendOscMessage("/live/select/device", newPath);
        }
      }
    } else if (type === "rc") {
      console.log('Current path for rc:', this.state.path);

      if (this.state.submode === 0) {
        const newPath = [...this.state.path.slice(0, -1), index];
        console.log('rc: sending path (chain mode):', newPath);
        this.oscHandler.sendOscMessage("/live/select/chain", newPath);
      } else {
        const newPath = [...this.state.path.slice(0, -2), index];
        console.log('rc: sending path (device mode):', newPath);
        this.oscHandler.sendOscMessage("/live/select/chain", newPath);
      }
    } else if (type === "dp") {
      if (this.state.submode === 0) {
        const newPath = [...this.state.path.slice(0, -1), index];
        console.log('dp: sending path (drum rack chain mode):', newPath);
        this.oscHandler.sendOscMessage("/live/select/chain_by_note", newPath);
      } else {
        const newPath = [...this.state.path.slice(0, -2), index];
        console.log('dp: sending path (drum rack device mode):', newPath);
        this.oscHandler.sendOscMessage("/live/select/chain_by_note", newPath);
      }
    } else if (type === "ba") {
      console.log('Current path for back:', this.state.path);
      let newPath;

      if (this.state.submode === 0) {
        // En mode chain, on enlève le dernier élément
        newPath = this.state.path.slice(0, -1);
      } else {
        // En mode device, on enlève les deux derniers éléments
        newPath = this.state.path.slice(0, -2);
      }

      console.log('Back: sending path:', newPath);
      this.oscHandler.sendOscMessage("/live/select/device", newPath);
    } else if (type === "ld") {
      if (index === 1) {
        this.enableLock();
      } else if (index === 0) {
        this.disableLock();
      }
    } else if (type === "ia") {
      const deviceIndex = this.state.isLocked ? -4 : -3;
      this.oscHandler.sendOscMessage("/live/device/set/isActiveToggle", [
        deviceIndex,
      ]);
    } else if (type === "sm") {
      if (index === 1) {
        console.log("Mode volume");
        this.emit("modeChange", "volume");
      } else if (index === 0) {
        console.log("Mode track");
        this.emit("modeChange", "track");
      } else if (index === 3) {
        console.log("Mode learn");
        this.emit("modeChange", "learn");
      } else if (index === 4) {
        console.log("Mode browser");
        this.emit("modeChange", "browser");
      }
    } else if (type === "le") {
      if (this.state.submode === 1) {
        // Mode device - comportement actuel
        const startParam = (this.currentPage - 1) * this.slotsPerPage + 1;
        const paramIndex = index + 1 + ((this.currentPage - 1) * this.slotsPerPage);
        const paramInfo = this.parameterManager.getParameterInfo(paramIndex, this.state.isLocked);

        this.globalState.learnData = {
          param_type: 4,
          track_index: -2,
          device_index: this.state.isLocked ? -4 : this.liveTrackManager.selectedDevice,
          param_index: paramIndex,
          isQuantized: paramInfo.isQuantized,
          minValue: paramInfo.minValue,
          maxValue: paramInfo.maxValue
        }
        console.log("Mode learn (device)", this.globalState.learnData);
        this.emit("modeChange", "learn");
      } else if (this.state.submode === 0) {
        // Mode chain
        if (index === 0) {
          this.globalState.learnData = {
            param_type: 5,
            track_index: -2,
            chain_path: this.state.path // Ajout du chemin de la chaîne
          }
          console.log("Mode learn (chain - volume)", this.globalState.learnData);
          this.emit("modeChange", "learn");
        } else if (index === 1) {
          this.globalState.learnData = {
            param_type: 6,
            track_index: -2,
            chain_path: this.state.path // Ajout du chemin de la chaîne
          }
          console.log("Mode learn (chain - pan)", this.globalState.learnData);
          this.emit("modeChange", "learn");
        } else if (index === 2) {
          this.globalState.learnData = {
            param_type: 9,
            track_index: -2,
            chain_path: this.state.path // Ajout du chemin de la chaîne
          }
          console.log("Mode learn (chain - mute)", this.globalState.learnData);
          this.emit("modeChange", "learn");
        } else if (index === 3) {
          this.globalState.learnData = {
            param_type: 10,
            track_index: -2,
            chain_path: this.state.path // Ajout du chemin de la chaîne
          }
          console.log("Mode learn (chain - solo)", this.globalState.learnData);
          this.emit("modeChange", "learn");
        }
      }
    } else if (type === "on") {
      let newPath;
      if (this.state.submode === 0) {
        // En mode chain, on ajoute l'index au path actuel
        newPath = [...this.state.path, index];
      } else {
        // En mode device, on remplace le dernier élément par l'index
        newPath = [...this.state.path.slice(0, -1), index];
      }
      console.log('on: sending path:', newPath);
      this.oscHandler.sendOscMessage("/live/device/set/isActiveTogByPath", newPath);
    } else if (type === "de") {
      let newPath;
      if (this.state.submode === 0) {
        // En mode chain, on ajoute l'index au path actuel
        newPath = [...this.state.path, index];
      } else {
        // En mode device, on remplace le dernier élément par l'index
        newPath = [...this.state.path.slice(0, -1), index];
      }
      console.log('de: sending path for deletion:', newPath);
      this.oscHandler.sendOscMessage("/live/device/delete_by_path", newPath);
    } else if (type === "ho") {
      // Activation du hotswap
      let newPath;
      if (this.state.submode === 0) {
        // En mode chain, on ajoute l'index au path actuel
        newPath = [...this.state.path, index];
      } else {
        // En mode device, on remplace le dernier élément par l'index
        newPath = [...this.state.path.slice(0, -1), index];
      }
      this.oscHandler.sendOscMessage("/live/browser/hotswap/enable", newPath);
      // Basculement vers le mode browser
      console.log("Mode browser");
      this.emit("modeChange", "browser");
    } else if (type === "st") {
      if (this.state.submode === 1) {
        // Mode device - comportement existant
        const startParam = (this.currentPage - 1) * this.slotsPerPage + 1;
        const parameterIndex = startParam + index;

        const nextValue = this.parameterManager.toggleQuantizedValue(parameterIndex);

        if (nextValue !== false) {
          this.parameterManager.sendParameterUpdate(
            this.state.path,
            parameterIndex,
            nextValue
          );
        }
      } else if (this.state.submode === 0) {
        // Mode chain - nouveau comportement
        if (index <= 3) { // On ne traite que les index 0 à 3       

          this.parameterManager.sendChainParameterUpdate(
            this.state.path,
            index,
          );
        }
      }
    }
  }

  logParameterInfo() {
    this.parameterManager.logParameterInfo(this.state.isLocked);
  }

  selectChain(...chainIndices) {
    if (chainIndices.length === 0) return;
    this.oscHandler.sendOscMessage("/live/select/chain", [...chainIndices]);
  }

  selectChainDevice(...chainIndices) {
    if (chainIndices.length === 0) return;
    this.oscHandler.sendOscMessage("/live/select/device", [...chainIndices]);
  }

  goBack() {
    this.oscHandler.sendOscMessage("/live/navigation/back");
  }

  test() {
    this.oscHandler.sendOscMessage("/live/browser/load", [1, 1, 1, 0, -1, 0, 0, 0]);
  }

  shortString(stringstoshorten) {
    if (stringstoshorten.length > 30) {
      stringstoshorten = stringstoshorten.slice(0, 30) + '...';
    }
    return stringstoshorten;
  }
}


module.exports = LiveDeviceMode;
