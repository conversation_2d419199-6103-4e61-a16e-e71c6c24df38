class DevicePropertyManager {
  constructor({ osc<PERSON><PERSON><PERSON>, communicationManager, liveTrackManager, state }) {
    this.oscHandler = oscHandler;
    this.communicationManager = communicationManager;
    this.liveTrackManager = liveTrackManager;
    this.state = state;
    this.deviceProperties = new Map();
    this.chainProperties = new Map();
    console.log('Path initialized:', this.state.path);
  }

  shortDelay = 2;
  mediumShortDelay = 30;
  mediumLongDelay = 30;
  longDelay = 50;

  updateDeviceProperty(trackIndex, deviceIndex, propertyName, value) {
    const deviceKey = `${trackIndex}-${deviceIndex}`;
    if (!this.deviceProperties.has(deviceKey)) {
      this.deviceProperties.set(deviceKey, {});
    }
    const properties = this.deviceProperties.get(deviceKey);
    properties[propertyName] = value;
    this.deviceProperties.set(deviceKey, properties);
  }

  getDeviceProperty(trackIndex, deviceIndex) {
    const deviceKey = `${trackIndex}-${deviceIndex}`;
    return this.deviceProperties.get(deviceKey);
  }

  clearProperties() {
    this.deviceProperties.clear();
  }

  sendSetIsCollapsed(trackIndex, deviceIndex) {
    this.oscHandler.sendOscMessage("/live/device/set/is_collapsed", [
      trackIndex,
      deviceIndex,
    ]);
  }

  async handleEnvironment(args) {
    const isDrumRack = args[0];
    const pathLength = args[1];
    const path = args[2];
    const chainsCount = args[3];
    const chainsData = args[4];
    const devicesCount = args[5];
    const devicesData = args[6];

    if (Array.isArray(path)) {
      this.state.path = [...path];
      this.state.submode = this.state.path.length % 2;
      this.state.isDrumRack = isDrumRack === 1;

    } else {
      this.state.path = [];
      this.state.submode = 0;
      this.state.isDrumRack = false;
    }

    // Clear des maps existantes
    this.chainProperties.clear();
    this.deviceProperties.clear();

    // Si pathLength est 1, on affiche les root devices
    if (pathLength === 1) {
      await this.displayRootDevices(devicesCount, path[0], devicesData);
      return;
    }
    else if (isDrumRack) {
      await this.displayRackDevices(devicesCount, devicesData, path);
      await this.displayDrumChains(chainsCount, chainsData);
    }
    else {
      await this.displayRackDevices(devicesCount, devicesData, path);
      await this.displayRackChains(chainsCount, chainsData, path);
    }

    // Mise à jour des chains
    for (let i = 0; i < chainsCount; i++) {
      const offset = i * 2; // Maintenant 2 valeurs par chain (nom, status)
      const chainKey = `${i}`;
      const status = chainsData[offset + 1];

      this.chainProperties.set(chainKey, {
        name: chainsData[offset],
        mute: status === 1 || status === 3,
        solo: status === 2 || status === 3
      });
    }

    // Mise à jour des devices
    for (let i = 0; i < devicesCount; i++) {
      const offset = i * 2; // 2 valeurs par device (nom, status)
      const deviceKey = `${i}`;
      const status = devicesData[offset + 1];

      this.deviceProperties.set(deviceKey, {
        name: devicesData[offset],
        isActive: status <= 2,
        canHaveChains: status === 1 || status === 4,
        canHaveDrumpads: status === 2 || status === 5
      });
    }
  }

  async displayRootDevices(devicesCount, selectedDevice, devicesData) {
    // Vérifier si this.state.isActive est défini
    console.log('displayRootDevices - isActive:', this.state.isActive);

    await this.displayDevices({
      devicesCount,
      selectedDevice,
      devicesData,
      type: 'root',
      messagePrefix: {
        display: 'dd',
        batch: 'dk',
        remove: 'de',
        add: 'du',
      },
      stateProperty: 'lastDevicesState',
      containerPrefix: 'new_middle_bottom.devices',
      colorNormal: 'COULEUR_GRIS',
      colorSelected: 'COULEUR_GRIS_MOYEN',
      maxDevices: 64,
      devicesPerBatch: 8
    });
  }

  async displayRackDevices(devicesCount, devicesData, path) {
    const selectedDevice = this.state.submode === 1 ? path[path.length - 1] : -1;

    await this.displayDevices({
      devicesCount,
      selectedDevice,
      devicesData,
      type: 'rack',
      messagePrefix: {
        display: 'd1',
        batch: 'd2',
        remove: 'd3',
        add: 'd4',
      },
      stateProperty: 'lastRackDevicesState',
      containerPrefix: 'rack_middle_bottom.rack_devices',
      colorNormal: 'COULEUR_GRIS_FONCE',
      colorSelected: 'COULEUR_GRIS_MOYEN',
      maxDevices: 64,
      devicesPerBatch: 8
    });
  }

  /**
   * Méthode commune pour l'affichage des périphériques (root ou rack)
   * @param {Object} options Les options de configuration
   * @param {number} options.devicesCount Nombre total de périphériques
   * @param {number} options.selectedDevice Index du périphérique sélectionné
   * @param {Array} options.devicesData Données des périphériques
   * @param {string} options.type Type de périphériques ('root' ou 'rack')
   * @param {Object} options.messagePrefix Préfixes des messages
   * @param {string} options.stateProperty Nom de la propriété d'état
   * @param {string} options.containerPrefix Préfixe du conteneur
   * @param {string} options.colorNormal Couleur normale
   * @param {string} options.colorSelected Couleur sélectionnée
   * @param {number} options.maxDevices Nombre maximum de périphériques
   * @param {number} options.devicesPerBatch Nombre de périphériques par lot
   */
  async displayDevices(options) {
    const {
      devicesCount,
      selectedDevice,
      devicesData,
      type,
      messagePrefix,
      stateProperty,
      maxDevices,
      devicesPerBatch
    } = options;

    const effectiveDeviceCount = Math.min(devicesCount, maxDevices);

    // Création du nouvel état
    const newDevicesState = [];
    for (let i = 0; i < effectiveDeviceCount; i++) {
      const offset = i * 2;
      newDevicesState.push({
        name: devicesData[offset] || "-",
        status: devicesData[offset + 1]
      });
    }

    // Détection des suppressions
    if (this[stateProperty] && this[stateProperty].length === newDevicesState.length + 1) {
      let deletedIndex = -1;

      for (let i = 0; i < this[stateProperty].length; i++) {
        if (i >= newDevicesState.length ||
          this[stateProperty][i].name !== newDevicesState[i].name ||
          this[stateProperty][i].status !== newDevicesState[i].status) {

          if (i + 1 < this[stateProperty].length &&
            i < newDevicesState.length &&
            this[stateProperty][i + 1].name === newDevicesState[i].name &&
            this[stateProperty][i + 1].status === newDevicesState[i].status) {

            deletedIndex = i;
            break;
          }
        }
      }

      if (deletedIndex !== -1) {
        console.log(`Périphérique ${type === 'rack' ? 'de rack ' : ''}supprimé à l'index ${deletedIndex}`);
        this.communicationManager.sendMessage(
          `${messagePrefix.remove},${deletedIndex},${selectedDevice}`,
          this.state.isActive
        );
        this[stateProperty] = newDevicesState;
        return;
      }
    }

    // Message de configuration initial
    this.communicationManager.sendMessage(
      `${messagePrefix.display},${effectiveDeviceCount},${selectedDevice}`,
      this.state.isActive
    );

    await new Promise(resolve => setTimeout(resolve, this.shortDelay));

    // Envoi des périphériques par lots
    for (let batchIndex = 0; batchIndex * devicesPerBatch < effectiveDeviceCount; batchIndex++) {
      const startIdx = batchIndex * devicesPerBatch;
      const batchEnd = Math.min(startIdx + devicesPerBatch, effectiveDeviceCount);
      const batchSize = batchEnd - startIdx;
      const batchMessage = [messagePrefix.batch, startIdx, batchSize];

      for (let j = startIdx; j < batchEnd; j++) {
        const device = newDevicesState[j];
        batchMessage.push(device.name, device.status);
      }

      this.communicationManager.sendMessage(batchMessage.join(","), this.state.isActive);
      await new Promise(resolve => setTimeout(resolve, this.mediumLongDelay));
    }

    this[stateProperty] = newDevicesState;
  }

  async displayRackChains(chainsCount, chainsData, path) {
    const BATCH_SIZE = 8;
    const BATCH_DELAY = this.mediumLongDelay;
    const SECTION_DELAY = this.mediumShortDelay;
    const MAX_NAME_LENGTH = 20;

    const selectedChain = this.state.submode === 0
      ? path[path.length - 1]
      : path[path.length - 2];

    // Fonction utilitaire pour tronquer les noms
    const truncateName = (name) => {
      if (name.length > MAX_NAME_LENGTH) {
        return '...' + name.slice(-MAX_NAME_LENGTH);
      }
      return name;
    };

    // Envoi du message initial pour les chains
    this.communicationManager.sendMessage(`dc,${chainsCount},${selectedChain}`, this.state.isActive);
    await new Promise(resolve => setTimeout(resolve, SECTION_DELAY));

    // Envoi des chains par lots
    for (let i = 0; i < chainsCount; i += BATCH_SIZE) {
      const batchMessage = [`dc+`];
      const batchEnd = Math.min(i + BATCH_SIZE, chainsCount);

      for (let j = i; j < batchEnd; j++) {
        const offset = j * 2;
        const name = truncateName(chainsData[offset] || "-");
        const status = chainsData[offset + 1];
        batchMessage.push(name, status);
      }

      await new Promise(resolve => setTimeout(resolve, BATCH_DELAY));
      this.communicationManager.sendMessage(batchMessage.join(","), this.state.isActive);
    }

    await new Promise(resolve => setTimeout(resolve, SECTION_DELAY));
    this.communicationManager.sendMessage(`dc!`, this.state.isActive);
  }

  async displayDrumChains(chainsCount, chainsData) {
    const selectedChainIndex = this.state.submode === 0
      ? this.state.path[this.state.path.length - 1]
      : this.state.path[this.state.path.length - 2];

    // Récupération de la note MIDI du pad sélectionné
    const selectedPadNote = selectedChainIndex >= 0 && selectedChainIndex * 2 < chainsData.length
      ? chainsData[selectedChainIndex * 2]  // La note MIDI est à l'index pair
      : -1;

    // Envoi du message pour les pads de batterie (chains)
    const displayChainMessage = ["dz", chainsCount, selectedPadNote];
    for (let i = 0; i < chainsCount; i++) {
      const offset = i * 2; // 2 valeurs par chain (note MIDI, status)
      const note = chainsData[offset];
      const status = chainsData[offset + 1];
      displayChainMessage.push(note, status);
    }
    this.communicationManager.sendMessage(displayChainMessage.join(","), this.state.isActive);
  }

  updateDevicePropertiesMap(args) {
    this.deviceProperties.clear();
  }

  handleIsActive(args) {
    const [deviceIndex, state] = args;
    console.log('handleIsActive - deviceIndex:', deviceIndex, 'state:', state);

    // Si on n'est pas en mode locked (deviceIndex !== -4), on met à jour la propriété
    if (deviceIndex !== -4) {
      this.communicationManager.sendMessage(`ia,${state}`, this.state.isActive);
    }

    this.updateDeviceProperty(deviceIndex, 'isActive', Boolean(state));
  }

  handlePathString(args) {
    let pathString = args[0];
    if (pathString.length > 65) {
      pathString = '...' + pathString.slice(-65);
    }
    this.communicationManager.sendMessage(`ds,${pathString}`, this.state.isActive);
  }
}

module.exports = DevicePropertyManager;
