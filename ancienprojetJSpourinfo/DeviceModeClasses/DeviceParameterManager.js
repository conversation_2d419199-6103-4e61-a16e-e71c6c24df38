const EventEmitter = require('events');
const LiveVolumeConverter = require('../liveUtilityClasses/liveVolumeConverter');

class DeviceParameterManager extends EventEmitter {
  constructor({ oscHandler, communicationManager, state }) {
    super();
    this.oscHandler = oscHandler;
    this.communicationManager = communicationManager;
    this.state = state;
    this.volumeConverter = new LiveVolumeConverter();
    this.parameterInfo = new Map();
    this.parameterInfoLocked = new Map();
    this.messageQueue = [];
    this.isProcessingQueue = false;
    this.messageDelay = 3;

    // Nouvelles propriétés pour la gestion des pages
    this.isFirstPageChange = true;
    this.lastPageChangeTime = 0;
    this.pageChangeTimeout = null;
  }


  sendParameterUpdate(path, paramIndex, newValue) {
    // On envoie le path comme une liste unique, suivi de l'index du paramètre et de la nouvelle valeur
    const args = [path, paramIndex, newValue];

    this.oscHandler.sendOscMessage("/live/device/set/parameter/value", args);
  }

  handleParameterMessage(address, args, currentPage, slotsPerPage, isLocked) {
    const trackIndex = args[0];
    const paramIndex = args[1];
    const value = args[2];
    const valueString = args[3];

    const targetMap = isLocked ? this.parameterInfoLocked : this.parameterInfo;
    const shouldSendMessage = (isLocked && trackIndex === -4) || (!isLocked && trackIndex === -3);

    if (address === "/live/device/get/parameter/value") {
      if (targetMap.has(paramIndex)) {
        const paramInfo = targetMap.get(paramIndex);
        paramInfo.currentValue = value;
        paramInfo.valueString = valueString;
        targetMap.set(paramIndex, paramInfo);

        // On ignore le paramètre 0
        if (paramIndex > 0 && shouldSendMessage) {
          const startParam = ((currentPage - 1) * slotsPerPage) + 1;
          const endParam = startParam + slotsPerPage;

          if (paramIndex >= startParam && paramIndex < endParam) {
            const slotIndex = paramIndex - startParam;
            const slotLetter = String.fromCharCode(65 + slotIndex);

            const { minValue, maxValue } = paramInfo;
            const normalizedValue = Math.round(
              ((value - minValue) / (maxValue - minValue)) * 100
            );
            const formattedValue = normalizedValue.toString().padStart(3, "0");

            this.queueMessage(`pv,${slotLetter},${formattedValue},${valueString}`);
          }
        }
      }
    }
  }

  queueMessage(message) {
    if (this.messageQueue.includes(message)) {
      return;
    }

    this.messageQueue.push(message);

    if (!this.isProcessingQueue) {
      this.processMessageQueue();
    }
  }

  processMessageQueue() {
    if (this.messageQueue.length === 0) {
      this.isProcessingQueue = false;
      return;
    }

    this.isProcessingQueue = true;
    const message = this.messageQueue.shift();
    this.communicationManager.sendMessage(message, this.state.isActive);

    setTimeout(() => {
      this.processMessageQueue();
    }, this.messageDelay);
  }

  handleBulkDeviceParameters(values) {
    const trackIndex = values[0];
    const paramCount = values[1];
    const bulk_data = values.slice(2);

    // En mode verrouillé (-4) on utilise parameterInfoLocked, sinon parameterInfo
    const targetMap = (trackIndex === -4) ? this.parameterInfoLocked : this.parameterInfo;

    // On efface d'abord toutes les données existantes de la map cible
    targetMap.clear();

    // Mise à jour des données dans le map approprié
    for (let i = 0; i < paramCount; i++) {
      const baseIndex = i * 7;
      const paramIndex = bulk_data[baseIndex];

      targetMap.set(paramIndex, {
        name: bulk_data[baseIndex + 1],
        isQuantized: bulk_data[baseIndex + 2],
        minValue: bulk_data[baseIndex + 3],
        maxValue: bulk_data[baseIndex + 4],
        currentValue: bulk_data[baseIndex + 5],
        valueString: bulk_data[baseIndex + 6]
      });
    }

    // On envoie les messages uniquement si:
    // - On est en mode verrouillé (-4)
    // - OU on est en mode normal (-3) et PAS de lock actif
    if (trackIndex === -4 || (trackIndex === -3 && this.parameterInfoLocked.size === 0)) {
      const param0Info = targetMap.get(0);
      const param0Value = param0Info ? (param0Info.currentValue === 1 ? 1 : 0) : 0;
      this.communicationManager.sendMessage(`ia,${param0Value}`, this.state.isActive);
      this.setPage(1, paramCount, 8, trackIndex === -4);
    }
  }

  handleBulkChainParameters(values) {
    if (this.state.isLocked) return;  // On n'envoie rien si on est en mode lock

    const [volume, pan, mute, solo] = values[1];

    // Préparation du message des noms (nn)
    const nnMessage = ['nn', 'Volume', 'Pan', 'Mute', 'Solo', ' ', ' ', ' ', ' '].join(',');

    // Préparation du message des valeurs (di)
    const diParts = ['di'];

    // Volume avec conversion dB
    const dbValue = this.volumeConverter.toDb(volume);
    diParts.push(Math.round(volume * 100), dbValue === "-inf" ? "-INF" : `${dbValue} dB`);

    // Pan (-1 à 1 mappé vers 0-100 pour la valeur numérique)
    // et -50L à 50R pour la chaîne
    const panPercent = Math.round((pan + 1) * 50); // Mappe -1,1 vers 0,100
    const panValue = Math.round(Math.abs(pan * 50)); // Pour la chaîne de caractères
    const panString = pan === 0 ? 'C' : (pan < 0 ? `${panValue}L` : `${panValue}R`);
    diParts.push(panPercent, panString);

    // Mute
    diParts.push(mute * 100, mute ? 'On' : 'Off');

    // Solo
    diParts.push(solo * 100, solo ? 'On' : 'Off');

    // Remplir le reste avec des valeurs nulles
    for (let i = 0; i < 4; i++) {
      diParts.push(0, 'nul');
    }

    const diMessage = diParts.join(',');

    // Envoi des messages
    this.queueMessage(nnMessage);
    this.queueMessage(diMessage);
  }

  // Nouvelle méthode à appeler lors du unlock
  handleUnlock() {
    this.parameterInfoLocked.clear();

    if (this.parameterInfo.size > 0) {
      const param0Info = this.parameterInfo.get(0);
      const param0Value = param0Info ? (param0Info.currentValue === 1 ? 1 : 0) : 0;
      this.communicationManager.sendMessage(`ia,${param0Value}`, this.state.isActive);
      this.setPage(1, this.parameterInfo.size, 8, false);
    }
  }

  setPage(page, parametersCount, slotsPerPage, isLocked = false) {
    const maxPage = Math.ceil((parametersCount - 1) / slotsPerPage);
    const targetMap = isLocked ? this.parameterInfoLocked : this.parameterInfo;
    const currentTime = Date.now();

    if (page >= 1 && page <= maxPage) {
      // Nettoyer le timeout existant s'il y en a un
      if (this.pageChangeTimeout) {
        clearTimeout(this.pageChangeTimeout);
      }

      // Si c'est le premier changement de page ou si plus de 200ms se sont écoulées
      if (this.isFirstPageChange || (currentTime - this.lastPageChangeTime) > 200) {
        this.completePageUpdate(page, targetMap, slotsPerPage);
        this.isFirstPageChange = false;
      } else {
        // Programmer la mise à jour complète après 150ms
        this.pageChangeTimeout = setTimeout(() => {
          this.completePageUpdate(page, targetMap, slotsPerPage);
          this.isFirstPageChange = true;
        }, 150);
      }

      // Mettre à jour le timestamp du dernier changement
      this.lastPageChangeTime = currentTime;
      return true;
    }
    return false;
  }

  completePageUpdate(page, targetMap, slotsPerPage) {
    const startParam = ((page - 1) * slotsPerPage) + 1;
    const diMessageParts = ['di'];
    const nnMessageParts = ['nn'];

    for (let i = 0; i < slotsPerPage; i++) {
      const paramIndex = startParam + i;
      const paramInfo = targetMap.get(paramIndex);

      if (paramInfo) {
        // Nom du paramètre avec puce si quantifié
        const paramName = paramInfo.name || ' ';
        const displayName = paramInfo.isQuantized ? `|| ${paramName}` : paramName;
        nnMessageParts.push(displayName);

        // Valeur normalisée et chaîne de valeur
        if (paramInfo.currentValue !== null) {
          const normalizedValue = Math.round(
            ((paramInfo.currentValue - paramInfo.minValue) /
              (paramInfo.maxValue - paramInfo.minValue)) * 100
          );
          diMessageParts.push(normalizedValue);
          diMessageParts.push(paramInfo.valueString || 'nul');
        } else {
          diMessageParts.push(0);
          diMessageParts.push('nul');
        }
      } else {
        nnMessageParts.push(' ');
        diMessageParts.push(0);
        diMessageParts.push('nul');
      }
    }

    const diMessage = diMessageParts.join(',');
    const nnMessage = nnMessageParts.join(',');

    console.log(`[DEBUG] Messages finaux:`);
    console.log(`NN: ${nnMessage}`);
    console.log(`DI: ${diMessage}`);

    this.queueMessage(nnMessage);
    this.queueMessage(diMessage);
  }

  logParameterInfo(isLocked = false) {
    const targetMap = isLocked ? this.parameterInfoLocked : this.parameterInfo;
    console.log(`\n=== État des paramètres ${isLocked ? 'verrouillés' : 'non verrouillés'} ===`);

    targetMap.forEach((info, index) => {
      console.log(`\nParamètre #${index}:`);
      console.log(`  Nom: ${info.name}`);
      console.log(`  Valeur actuelle: ${info.currentValue}`);
      console.log(`  Chaîne de valeur: ${info.valueString}`);
      console.log(`  Quantifié: ${info.isQuantized}`);
      console.log(`  Valeur min: ${info.minValue}`);
      console.log(`  Valeur max: ${info.maxValue}`);
    });

    if (targetMap.size === 0) {
      console.log('Aucun paramètre enregistré.');
    }
    console.log('\n===================================');
  }

  handleChainParameterMessage(address, args) {
    if (this.state.isLocked) return;  // On n'envoie rien si on est en mode lock

    const value = args[2];
    let slotLetter, normalizedValue, valueString;

    switch (address) {
      case "/live/chain/get/volume":
        slotLetter = 'A';
        normalizedValue = Math.round(value * 100);
        const dbValue = this.volumeConverter.toDb(value);
        valueString = dbValue === "- inf dB" ? "- INF dB" : `${dbValue} dB`;
        break;

      case "/live/chain/get/pan":
        slotLetter = 'B';
        normalizedValue = Math.round((value + 1) * 50); // Mappe -1,1 vers 0,100
        const panValue = Math.round(Math.abs(value * 50));
        valueString = value === 0 ? 'C' : (value < 0 ? `${panValue}L` : `${panValue}R`);
        break;

      case "/live/chain/get/mute":
        slotLetter = 'C';
        normalizedValue = value * 100;
        valueString = value ? 'On' : 'Off';
        break;

      case "/live/chain/get/solo":
        slotLetter = 'D';
        normalizedValue = value * 100;
        valueString = value ? 'On' : 'Off';
        break;

      default:
        return;
    }

    const formattedValue = normalizedValue.toString().padStart(3, "0");
    this.queueMessage(`pv,${slotLetter},${formattedValue},${valueString}`);
  }

  getParameterInfo(parameterIndex, isLocked = false) {
    const targetMap = isLocked ? this.parameterInfoLocked : this.parameterInfo;
    return targetMap.get(parameterIndex);
  }

  toggleQuantizedValue(parameterIndex) {
    const paramInfo = this.getParameterInfo(parameterIndex, this.state.isLocked);

    if (!paramInfo || !paramInfo.isQuantized) {
      return false;
    }

    const { minValue, maxValue, currentValue } = paramInfo;

    // Calculer la prochaine valeur
    let nextValue;
    if (currentValue >= maxValue) {
      // Si on est au max, on retourne au min
      nextValue = minValue;
    } else {
      // Sinon on passe à la valeur suivante
      nextValue = Math.min(maxValue, currentValue + 1);
    }

    return nextValue;
  }

  sendChainParameterUpdate(path, index) {
    switch (index) {
      case 0:
        this.oscHandler.sendOscMessage("/live/chain/set/volume", [path, 0.85]);
        break;
      case 1:
        this.oscHandler.sendOscMessage("/live/chain/set/pan", [path, 0]);
        break;
      case 2:
        this.oscHandler.sendOscMessage("/live/chain/set/togglemute", [path]);
        break;
      case 3:
        this.oscHandler.sendOscMessage("/live/chain/set/togglesolo", [path]);
        break;
      default:
        // Ignorer les autres index
        break;
    }
  }
}

module.exports = DeviceParameterManager;
