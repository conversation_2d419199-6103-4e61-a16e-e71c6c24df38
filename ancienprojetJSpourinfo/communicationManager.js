const EventEmitter = require('events');

// communicationManager.js
const BLEManager = require('./bleManager');
const SerialManager = require('./serialManager');

class CommunicationManager extends EventEmitter {
  constructor() {
    super();
    this.serialManager = new SerialManager();
    this.bleManager = new BLEManager();
    this.activeManager = null;
    this.serialConnected = false;
    this.bleConnected = false;
    this.isInitialized = false;
    this.serialCheckInterval = null;
    this.priorityMessageQueue = [];    // Queue prioritaire pour le mode actif
    this.backgroundMessageQueue = [];  // Queue pour les modes en background
    this.isProcessingPriorityQueue = false;
    this.isProcessingBackgroundQueue = false;
    this.messageDelay = 3; // délai en ms entre chaque message prioritaire
    this.backgroundMessageDelay = 10; // délai en ms entre chaque message en arrière-plan
    this.priorityCheckDelay = 10; // délai en ms pour vérifier la queue prioritaire pendant le traitement en arrière-plan

    // Gestionnaires d'événements
    this.onSerialDisconnected = this.onSerialDisconnected.bind(this);
    this.onBLEDisconnected = this.onBLEDisconnected.bind(this);
    this.onBLEConnected = this.onBLEConnected.bind(this);

    // Configuration des gestionnaires d'événements BLE
    this.setupBLEEventHandlers();

    // Abonnez-vous aux événements de message des gestionnaires
    this.serialManager.on('message', this.handleMessage.bind(this));
    this.bleManager.on('message', this.handleMessage.bind(this));
  }

  setupBLEEventHandlers() {
    this.bleManager.on('connected', this.onBLEConnected);
    this.bleManager.on('disconnected', this.onBLEDisconnected);
  }

  async initialize() {
    await this.attemptSerialConnection();
    if (!this.serialConnected) {
      console.log("Initialisation série échouée, tentative de connexion BLE");
      await this.initializeBLE();
    }
    this.isInitialized = true;
  }

  async attemptSerialConnection() {
    console.log("Tentative de connexion série...");
    const serialInitialized = await this.serialManager.initializeSerialPort();
    if (serialInitialized) {
      this.serialConnected = true;
      this.setActiveManager(this.serialManager);
      this.bleManager.close();
      this.serialManager.sendMessage("SerETest");

      this.serialManager.removeListener('disconnected', this.onSerialDisconnected);
      this.serialManager.on('disconnected', this.onSerialDisconnected);

      console.log("Connexion série établie");
    }
    return serialInitialized;
  }

  handleMessage(message) {
    console.log(`Message reçu (CommunicationManager) : ${message}`);

    // Traitement existant des autres messages
    const match = message.match(/^([ebt]),([^,]+)(?:,([-\d]+)(?:,([-\d]+))?)?$/);

    if (match) {
      const [, type, value1, value2, value3] = match;

      if (type === 'e') {
        this.emit('encoderChange', parseInt(value1), parseInt(value2));
      } else if (type === 'b') {
        this.emit('buttonPressed', parseInt(value1));
      } else if (type === 't') {
        if (value3 !== undefined) {
          console.log(`Émission touchPressed avec type=${value1}, index1=${parseInt(value2)}, index2=${parseInt(value3)}`);
          this.emit('touchPressed', value1, parseInt(value2), parseInt(value3));
        } else {
          console.log(`Émission touchPressed avec type=${value1}, touchIndex=${parseInt(value2)}`);
          this.emit('touchPressed', value1, parseInt(value2));
        }
      }
    } else {
      // Ne pas émettre les messages d'acquittement comme des messages génériques
      this.emit('message', message);
    }
  }

  handleSerialMessage(message) {
    // Cette fonction semble redondante avec handleMessage car serialManager émet 'message'.
    // On peut potentiellement la supprimer ou la refactoriser si elle avait un but spécifique.
    // Pour l'instant, je la laisse mais commente son contenu pour éviter double traitement.
    /*
    console.log(`Message reçu (CommunicationManager - handleSerialMessage) : ${message}`);
    if (message === "SerOK" || message === "SerEOK") {
      // ... logique spécifique ...
    } else if (message === "SerTest" || message === "SerETest") {
      // ...
    } else {
      // ...
    }
    */
  }

  onSerialDisconnected() {
    console.log("Déconnexion série détectée, tentative de connexion BLE");
    this.serialConnected = false;
    this.setActiveManager(null);
    // Arrêter la vérification si on perd la connexion série ? Ou continuer en espérant une reconnexion ?
    // Pour l'instant, on laisse tourner.
    this.initialize(); // Tente de reconnecter (BLE ou Série)
  }

  onBLEDisconnected() {
    console.log("Déconnexion BLE détectée, tentative de reconnexion série");
    this.bleConnected = false;
    this.setActiveManager(null);
    this.initialize(); // Tente de reconnecter (Série ou BLE)
  }

  async initializeBLE() {
    console.log("Initialisation BLE...");
    try {
      await this.bleManager.initialize();
      this.bleConnected = true;
      this.setActiveManager(this.bleManager);
      console.log("Initialisation BLE réussie");
    } catch (error) {
      console.log("Initialisation BLE échouée : ", error.message);
    }
  }

  onBLEConnected() {
    console.log("Connexion BLE établie, tentative de reconnexion série");
    this.attemptSerialConnection();
  }

  setActiveManager(manager) {
    this.activeManager = manager;
    console.log('\x1b[34m%s\x1b[0m', `Gestionnaire actif défini sur : ${manager === this.serialManager ? "Série" : manager === this.bleManager ? "BLE" : "null"}`);
  }

  async sendMessage(message, isPriority = true) {
    if (!this.isInitialized) {
      console.warn("CommunicationManager n'est pas encore initialisé. Message ignoré.");
      return;
    }
    if (!this.activeManager) {
      console.warn("Aucun gestionnaire actif. Message ignoré.");
      return;
    }

    // Ajouter le message à la queue appropriée
    if (isPriority) {
      this.priorityMessageQueue.push(message);
      if (!this.isProcessingPriorityQueue) {
        this.processPriorityQueue();
      }
    } else {
      this.backgroundMessageQueue.push(message);
      if (!this.isProcessingBackgroundQueue) {
        this.processBackgroundQueue();
      }
    }
  }

  async processPriorityQueue() {
    if (this.isProcessingPriorityQueue || this.priorityMessageQueue.length === 0) {
      return;
    }

    this.isProcessingPriorityQueue = true;

    while (this.priorityMessageQueue.length > 0) {
      const message = this.priorityMessageQueue.shift();
      try {
        // Interrompre le traitement en arrière-plan si nécessaire
        if (this.isProcessingBackgroundQueue) {
          this.isProcessingBackgroundQueue = false;
          console.log("Mode prioritaire : interruption du traitement en arrière-plan");
        }

        await this.activeManager.sendMessage(message);
        await new Promise(resolve => setTimeout(resolve, this.messageDelay));
      } catch (error) {
        console.error('Erreur lors de l\'envoi du message prioritaire:', error);
      }
    }

    this.isProcessingPriorityQueue = false;
  }

  async processBackgroundQueue() {
    if (this.isProcessingBackgroundQueue || this.backgroundMessageQueue.length === 0) {
      return;
    }

    this.isProcessingBackgroundQueue = true;
    let lastPriorityCheck = Date.now();

    while (this.backgroundMessageQueue.length > 0) {
      // Vérification périodique de la queue prioritaire
      const now = Date.now();
      if (now - lastPriorityCheck >= this.priorityCheckDelay) {
        if (this.priorityMessageQueue.length > 0) {
          console.log("Messages prioritaires détectés, interruption du traitement en arrière-plan");
          await this.processPriorityQueue();
          // Attendre un délai plus long après le traitement prioritaire
          await new Promise(resolve => setTimeout(resolve, this.backgroundMessageDelay * 2));
          lastPriorityCheck = Date.now();
          continue;
        }
        lastPriorityCheck = now;
      }

      const message = this.backgroundMessageQueue.shift();
      try {
        await this.activeManager.sendMessage(message);
        // Utiliser le délai plus long pour les messages en arrière-plan
        await new Promise(resolve => setTimeout(resolve, this.backgroundMessageDelay));
      } catch (error) {
        console.error('Erreur lors de l\'envoi du message en arrière-plan:', error);
      }
    }

    this.isProcessingBackgroundQueue = false;
  }

  setMessageHandler(handler) {
    if (typeof handler === 'function') {
      this.on('message', handler);
    } else {
      console.error('Le gestionnaire de messages doit être une fonction');
    }
  }

  async close() {
    if (this.serialCheckInterval) {
      clearInterval(this.serialCheckInterval);
    }
    if (this.serialManager) {
      this.serialManager.removeListener('disconnected', this.onSerialDisconnected);
      await this.serialManager.close();
    }
    if (this.bleManager) {
      this.bleManager.removeListener('disconnected', this.onBLEDisconnected);
      await this.bleManager.close();
    }
    this.isInitialized = false;
  }
}

// Singleton
const instance = new CommunicationManager();
module.exports = instance;
