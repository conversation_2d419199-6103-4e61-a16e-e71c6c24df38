/**
 * @fileoverview Handles OSC messages related to learning parameter mappings
 * from Ableton Live and updating the corresponding state and
 * external communication interface.
 */

const LiveVolumeConverter = require("../liveUtilityClasses/liveVolumeConverter");
const LiveVolumeSendConverter = require("../liveUtilityClasses/liveVolumeSendConverter");

// Constants for parameter types for better readability
const PARAM_TYPE = {
      VOLUME: 1,
      PANNING: 2,
      SEND: 3,
      DEVICE: 4,
      CHAIN_VOLUME: 5,
      CHAIN_PANNING: 6,
      MUTE: 7,
      SOLO: 8,
      CHAIN_MUTE: 9,
      CHAIN_SOLO: 10,
};

// Constants for special indices or values
const LOCKED_DEVICE_INDEX = -4; // Special index indicating a locked device parameter
const CHAIN_TRACK_INDEX = -2;   // Special index used when sending OSC for chain parameters

// Default structure for an empty/cleared slot
const DEFAULT_SLOT_INFO = {
      type: null,
      trackColor: "",
      trackName: "",
      deviceName: "",
      parameterName: "",
      parameterIndex: null,
      sendIndex: null,
      value: null,
      valueString: "",
      isQuantize: null,
      min: null,
      max: null,
      buffer: 0, // Assuming buffer is part of the slot state, keeping it
};

class LearnMessageHandler {
      /**
       * Creates an instance of LearnMessageHandler.
       * @param {object} options - Configuration options.
       * @param {OscHandler} options.oscHandler - Instance to handle outgoing OSC messages.
       * @param {CommunicationManager} options.communicationManager - Instance to manage communication with the external interface.
       * @param {LiveTrackManager} options.liveTrackManager - Instance to manage track information (names, colors, return tracks).
       * @param {object} options.state - Shared application state (activeSlot, currentPage, slots, trackNames, trackColors, isLearning, isActive).
       */
      constructor({ oscHandler, communicationManager, liveTrackManager, state }) {
            this.oscHandler = oscHandler;
            this.communicationManager = communicationManager;
            this.liveTrackManager = liveTrackManager;
            this.state = state; // Shared state object

            this.slotsPerPage = 8; // Number of slots displayed per page on the interface

            // Stores detailed parameter information fetched from Live for devices
            this.deviceParameters = new Map(); // Key: deviceIndex, Value: { names:[], isQuantized:[], min:[], max:[], deviceName:'' }

            // Utility classes for converting Live's internal values to display values
            this.volumeConverter = new LiveVolumeConverter();
            this.volumeSendConverter = new LiveVolumeSendConverter();

            // State related to handling potential conflicts between mute/solo messages for chains
            this.lastMuteTime = 0;
            this.lastMuteSlot = null;
            this.muteTimeout = null; // Timeout ID for delayed mute processing
            this.muteDebounceTime = 100; // Delay (ms) to wait before processing chain mute, allows solo to override
      }

      // --- Main Message Handling ---

      /**
       * Primary handler for incoming OSC messages during the learning phase.
       * Dispatches messages to specific handlers based on the OSC address.
       * @param {Array<any>} args - Arguments received with the OSC message.
       * @param {string} address - The OSC address pattern.
       * @param {number} realTrackIndex - The index of the track associated with the message.
       */
      handleLearningMessages(args, address, realTrackIndex) {
            console.log(`[DEBUG] handleLearningMessages - Start - isLearning: ${this.state.isLearning}, address: ${address}, args:`, args);

            // Calculate display-related information based on the active slot
            const slotPage = Math.floor(this.state.activeSlot / this.slotsPerPage) + 1;
            const displaySlot = this.state.activeSlot % this.slotsPerPage; // Slot index on the current page (0-7)
            const isOnCurrentPage = slotPage === this.state.currentPage;

            // Retrieve track information, fallback to defaults if not found
            const trackName = this.state.trackNames?.get(realTrackIndex) || 'Track ' + realTrackIndex;
            const trackColor = this.state.trackColors?.get(realTrackIndex) || '#FFFFFF'; // Default color

            // --- Dispatch based on OSC Address ---
            // Note: Most learning handlers will set this.state.isLearning = false upon completion.

            switch (address) {
                  // --- Locked Device Parameter Info ---
                  case "/live/get/lockedDevice/paramProperties":
                        this._handleLearnLockedDeviceProperties(args);
                        // This message only provides properties, doesn't complete learning
                        break;

                  // --- Parameter Value (Main learning trigger for devices) ---
                  case "/live/device/learning/parameter/value":
                        if (args[1] === LOCKED_DEVICE_INDEX) {
                              // Special handling for parameters coming from a "Locked Device" M4L device
                              this._handleLearnLockedDeviceValue(args, displaySlot, isOnCurrentPage);
                        } else {
                              // Handling for regular device parameters
                              this._handleLearnDeviceValue(args, displaySlot, isOnCurrentPage, trackName, trackColor);
                        }
                        break;

                  // --- Track Parameter Learning ---
                  case "/live/track/learning/volume":
                        this._handleLearnTrackVolume(args, realTrackIndex, displaySlot, isOnCurrentPage, trackName, trackColor);
                        break;

                  case "/live/track/learning/panning":
                        this._handleLearnTrackPanning(args, realTrackIndex, displaySlot, isOnCurrentPage, trackName, trackColor);
                        break;

                  case "/live/track/learning/sends":
                        this._handleLearnTrackSend(args, realTrackIndex, displaySlot, isOnCurrentPage, trackName, trackColor);
                        break;

                  case "/live/track/learning/mute":
                        this._handleLearnTrackMute(args, realTrackIndex, displaySlot, isOnCurrentPage, trackName, trackColor);
                        break;

                  case "/live/track/learning/solo":
                        this._handleLearnTrackSolo(args, realTrackIndex, displaySlot, isOnCurrentPage, trackName, trackColor);
                        break;

                  // --- Chain Parameter Learning ---
                  case "/live/chain/learning/volume":
                        this._handleLearnChainVolume(args, displaySlot, isOnCurrentPage, trackName, trackColor);
                        break;

                  case "/live/chain/learning/panning":
                        this._handleLearnChainPanning(args, displaySlot, isOnCurrentPage, trackName, trackColor);
                        break;

                  case "/live/chain/learning/mute":
                        this._handleLearnChainMute(args, displaySlot, isOnCurrentPage, trackName, trackColor);
                        break;

                  case "/live/chain/learning/solo":
                        this._handleLearnChainSolo(args, displaySlot, isOnCurrentPage, trackName, trackColor);
                        break;

                  default:
                        console.warn(`[WARN] Unhandled learning message address: ${address}`);
                        break;
            }
            console.log(`[DEBUG] handleLearningMessages - End - isLearning: ${this.state.isLearning}`);
      }

      /**
       * Handles incoming OSC messages containing device parameter details (names, ranges, etc.).
       * Stores this information for later use when a parameter value is learned.
       * @param {Array<any>} args - Arguments received with the OSC message.
       * @param {string} address - The OSC address pattern.
       */
      handleDeviceParameters(args, address) {
            console.log(`[DEBUG] handleDeviceParameters - Start - address: ${address}, args:`, args);

            const deviceIndex = args[1]; // Device index is always the second argument

            // Ensure the entry for this device exists in the map
            if (!this.deviceParameters.has(deviceIndex)) {
                  this.deviceParameters.set(deviceIndex, {
                        names: [],
                        isQuantized: [],
                        min: [],
                        max: [],
                        deviceName: `Device ${deviceIndex}` // Default name
                  });
            }
            const deviceParam = this.deviceParameters.get(deviceIndex);

            if (address === "/live/device/learning/bulk_parameters") {
                  // Message contains multiple parameter properties
                  // Args format: [?, deviceIndex, param1Name, param1IsQuantized, param1Min, param1Max, param2Name, ...]
                  for (let i = 2; i < args.length - 3; i += 4) {
                        const paramIndex = (i - 2) / 4; // Calculate index based on position
                        deviceParam.names[paramIndex] = args[i];
                        deviceParam.isQuantized[paramIndex] = args[i + 1] === 1 || args[i + 1] === true;
                        deviceParam.min[paramIndex] = parseFloat(args[i + 2]);
                        deviceParam.max[paramIndex] = parseFloat(args[i + 3]);
                  }
                  console.log(`[DEBUG] Bulk parameters updated for device ${deviceIndex}:`, deviceParam);

            } else if (address === "/live/device/learning/name") {
                  // Message contains the device name
                  // Args format: [?, deviceIndex, deviceName]
                  deviceParam.deviceName = args[2];
                  console.log(`[DEBUG] Device name received - index: ${deviceIndex}, name: ${deviceParam.deviceName}`);
            }

            console.log(`[DEBUG] Device parameters map after update:`, this.deviceParameters);
      }

      /**
       * Handles incoming OSC messages that provide updates to the *value* of an already learned parameter.
       * Updates the internal state and sends updates to the communication interface if the slot is visible.
       * @param {Array<any>} args - Arguments received with the OSC message. (Typically starts with slotIndex).
       * @param {string} address - The OSC address pattern.
       */
      handleParameterUpdates(args, address) {
            const slotIndex = args[0]; // Slot index is always the first argument
            const slot = this.state.slots.get(slotIndex);

            // Ignore updates for unlearned slots
            if (!slot || slot.type === null) {
                  // console.warn(`[WARN] Received update for unlearned/cleared slot ${slotIndex}, address: ${address}`);
                  return;
            }

            // Calculate display-related information
            const displaySlot = slotIndex % this.slotsPerPage;
            const slotPage = Math.floor(slotIndex / this.slotsPerPage) + 1;
            const isOnCurrentPage = slotPage === this.state.currentPage;

            // --- Dispatch based on OSC Address ---
            switch (address) {
                  case "/live/tracklearn/get/volume": {
                        const value = args[1];
                        if (isOnCurrentPage) {
                              const volumePercent = Math.round(value * 100);
                              const volumeDb = this.volumeConverter.toDb(value);
                              this.communicationManager.sendMessage(`lu,${displaySlot},${volumePercent},${volumeDb} dB`, this.state.isActive);
                        }
                        this.updateParameterInfo(slotIndex, { value: value });
                        break;
                  }
                  case "/live/tracklearn/get/panning": {
                        const value = args[1];
                        if (isOnCurrentPage) {
                              const { percentage, displayString } = this.convertPanToDisplay(value);
                              this.communicationManager.sendMessage(`lu,${displaySlot},${percentage},${displayString}`, this.state.isActive);
                        }
                        this.updateParameterInfo(slotIndex, { value: value });
                        break;
                  }
                  case "/live/tracklearn/get/sends": {
                        // Args: [slotIndex, sendIndex, value]
                        const value = args[2];
                        if (isOnCurrentPage) {
                              const sendPercent = Math.round(value * 100);
                              const sendDb = this.volumeSendConverter.toDb(value);
                              this.communicationManager.sendMessage(`lu,${displaySlot},${sendPercent},${sendDb} dB`, this.state.isActive);
                        }
                        // Ensure sendIndex matches if already learned, update value
                        if (slot.sendIndex === args[1]) {
                              this.updateParameterInfo(slotIndex, { value: value });
                        }
                        break;
                  }
                  case "/live/devicelearn/get/parameter/value": {
                        // Args: [slotIndex, parameterIndex(?), value, valueString] - Need to confirm args[1] role
                        const value = args[2];
                        const valueString = args[3];
                        if (isOnCurrentPage && slot.min !== null && slot.max !== null && slot.max !== slot.min) {
                              const normalizedValue = Math.round(((value - slot.min) / (slot.max - slot.min)) * 100);
                              const formattedValue = normalizedValue.toString().padStart(3, '0');
                              this.communicationManager.sendMessage(`lu,${displaySlot},${formattedValue},${valueString}`, this.state.isActive);
                        }
                        this.updateParameterInfo(slotIndex, { value: value, valueString: valueString });
                        break;
                  }
                  case "/live/chainlearn/get/volume": {
                        // Args: [slotIndex, chain_path, value]
                        const value = args[2];
                        if (isOnCurrentPage) {
                              const volumePercent = Math.round(value * 100);
                              const volumeDb = this.volumeConverter.toDb(value);
                              this.communicationManager.sendMessage(`lu,${displaySlot},${volumePercent},${volumeDb} dB`, this.state.isActive);
                        }
                        // Ensure chain_path matches if already learned, update value and potentially path
                        if (slot.chain_path === args[1]) {
                              this.updateParameterInfo(slotIndex, { value: value });
                        } else {
                              this.updateParameterInfo(slotIndex, { value: value, chain_path: args[1] }); // Update path as well
                        }
                        break;
                  }
                  case "/live/chainlearn/get/panning": {
                        // Args: [slotIndex, chain_path, value]
                        const value = args[2];
                        if (isOnCurrentPage) {
                              const { percentage, displayString } = this.convertPanToDisplay(value);
                              this.communicationManager.sendMessage(`lu,${displaySlot},${percentage},${displayString}`, this.state.isActive);
                        }
                        // Ensure chain_path matches if already learned, update value and potentially path
                        if (slot.chain_path === args[1]) {
                              this.updateParameterInfo(slotIndex, { value: value });
                        } else {
                              this.updateParameterInfo(slotIndex, { value: value, chain_path: args[1] }); // Update path as well
                        }
                        break;
                  }
                  case "/live/tracklearn/get/mute":
                  case "/live/tracklearn/get/solo":
                  case "/live/chainlearn/get/mute": // Args: [slotIndex, chain_path, value]
                  case "/live/chainlearn/get/solo": // Args: [slotIndex, chain_path, value]
                        {
                              const isChain = address.includes("chainlearn");
                              const valueArgIndex = isChain ? 2 : 1;
                              const value = args[valueArgIndex] === 1; // Convert 1/0 to true/false

                              if (isOnCurrentPage) {
                                    const percentValue = value ? 100 : 0;
                                    const display = value ? 'on' : 'off';
                                    this.communicationManager.sendMessage(`lu,${displaySlot},${percentValue},${display}`, this.state.isActive);
                              }

                              if (isChain) {
                                    // Ensure chain_path matches if already learned, update value and potentially path
                                    if (slot.chain_path === args[1]) {
                                          this.updateParameterInfo(slotIndex, { value: value });
                                    } else {
                                          this.updateParameterInfo(slotIndex, { value: value, chain_path: args[1] });
                                    }
                              } else {
                                    this.updateParameterInfo(slotIndex, { value: value });
                              }
                              break;
                        }

                  default:
                        console.warn(`[WARN] Unhandled parameter update address: ${address}`);
                        break;
            }
      }

      /**
       * Handles updates to the display properties of a slot (track name, device name, color).
       * Sends updates to the communication interface if the slot is visible.
       * @param {Array<any>} args - Arguments: [slotIndex, deviceName, trackName, trackColor].
       */
      handleSlotProperties(args) {
            const [slotIndex, deviceName, trackName, trackColor] = args;
            const slot = this.state.slots.get(slotIndex);

            // Ignore updates for unlearned slots
            if (!slot || slot.type === null) {
                  // console.warn(`[WARN] Received properties for unlearned/cleared slot ${slotIndex}`);
                  return;
            }

            // Update internal state
            this.updateParameterInfo(slotIndex, { deviceName, trackName, trackColor });

            // Update display if on the current page
            const displaySlot = slotIndex % this.slotsPerPage;
            const slotPage = Math.floor(slotIndex / this.slotsPerPage) + 1;
            const isOnCurrentPage = slotPage === this.state.currentPage;

            if (isOnCurrentPage) {
                  // Add bullet point prefix if device name exists
                  const deviceNameToSend = deviceName ? `• ${deviceName}` : '';
                  this.communicationManager.sendMessage(
                        `lo,${displaySlot},${deviceNameToSend},${trackName},${trackColor}`, // 'lo' for Layout/Object properties update
                        this.state.isActive
                  );
            }
      }

      /**
       * Handles a message indicating that a learned slot should be cleared.
       * @param {Array<any>} args - Arguments: [slotIndex].
       */
      handleSlotCleared(args) {
            const slotIndex = args[0];
            console.log(`[INFO] Clearing slot ${slotIndex}`);
            this.clearSlot(slotIndex);
      }


      // --- Private Learning Handlers (_handleLearn*) ---
      // These methods encapsulate the logic for specific learning message types.

      /**
       * Handles learning properties of a locked device parameter.
       * @private
       */
      _handleLearnLockedDeviceProperties(args) {
            // Args: [?, paramIndex, min, max, isQuantized, paramName]
            this.updateParameterInfo(this.state.activeSlot, {
                  type: PARAM_TYPE.DEVICE, // It's a type of device parameter
                  min: args[2],
                  max: args[3],
                  isQuantize: args[4] === 1 || args[4] === true,
                  parameterName: args[5],
                  deviceIndex: LOCKED_DEVICE_INDEX, // Mark as locked device
                  parameterIndex: args[1]
            });
            // Does not complete learning, wait for value message
      }

      /**
       * Handles learning the value of a locked device parameter.
       * @private
       */
      _handleLearnLockedDeviceValue(args, displaySlot, isOnCurrentPage) {
            // Args: [?, lockedDeviceIndex (-4), paramIndex, value, valueString]
            const value = parseFloat(args[3]);
            const valueString = args[4];
            const paramIndex = args[2];

            this.updateParameterInfo(this.state.activeSlot, {
                  value: value,
                  valueString: valueString
                  // Type, min, max, name etc. should have been set by _handleLearnLockedDeviceProperties
            });

            // Send confirmation OSC back to Live
            this.oscHandler.sendOscMessage("/live/learn/slot", [
                  this.state.activeSlot, PARAM_TYPE.DEVICE, CHAIN_TRACK_INDEX, LOCKED_DEVICE_INDEX, paramIndex
            ]);

            // Update display if necessary
            if (isOnCurrentPage) {
                  const slot = this.state.slots.get(this.state.activeSlot); // Re-fetch potentially updated slot
                  if (slot && slot.min !== null && slot.max !== null && slot.max !== slot.min) {
                        const normalizedValue = Math.round(((value - slot.min) / (slot.max - slot.min)) * 100);
                        const formattedValue = normalizedValue.toString().padStart(3, '0');
                        this.communicationManager.sendMessage(
                              `ls,${displaySlot},${slot.parameterName || 'Device Param'},${formattedValue},${valueString}`, // 'ls' for Learn Slot
                              this.state.isActive
                        );
                  } else {
                        // Fallback if min/max aren't known yet (should be rare)
                        this.communicationManager.sendMessage(
                              `ls,${displaySlot},${slot?.parameterName || 'Device Param'},?,${valueString}`,
                              this.state.isActive
                        );
                  }
            }
            this.state.isLearning = false; // Learning complete
      }

      /**
       * Handles learning the value of a regular device parameter.
       * @private
       */
      _handleLearnDeviceValue(args, displaySlot, isOnCurrentPage, trackName, trackColor) {
            console.log(`[DEBUG] _handleLearnDeviceValue - Start - args:`, args);
            // Args: [?, deviceIndex, paramIndex, value, valueString]
            const deviceIndexValue = args[1];
            const paramIndex = args[2];
            const value = parseFloat(args[3]);
            const valueString = args[4];

            // Retrieve pre-fetched device parameter details
            const deviceParams = this.deviceParameters.get(deviceIndexValue);
            console.log(`[DEBUG] Device parameters lookup for index ${deviceIndexValue}:`, deviceParams);

            if (!deviceParams) {
                  console.warn(`[WARN] No parameters found for device index ${deviceIndexValue} during learning. Using defaults.`);
                  // Provide default structure if lookup failed (should ideally not happen if handleDeviceParameters works)
                  this.updateParameterInfo(this.state.activeSlot, {
                        type: PARAM_TYPE.DEVICE,
                        trackName: trackName,
                        trackColor: trackColor,
                        deviceName: `Device ${deviceIndexValue}`,
                        parameterName: `Param ${paramIndex}`,
                        parameterIndex: paramIndex,
                        deviceIndex: deviceIndexValue,
                        value: value,
                        valueString: valueString,
                        isQuantize: false,
                        min: 0, // Default fallback
                        max: 1  // Default fallback
                  });
            } else {
                  const parameterName = deviceParams.names?.[paramIndex] || `Param ${paramIndex}`;
                  const isQuantized = deviceParams.isQuantized?.[paramIndex] ?? false;
                  const min = deviceParams.min?.[paramIndex] ?? 0;
                  const max = deviceParams.max?.[paramIndex] ?? 1;

                  console.log(`[DEBUG] Parameter name for index ${paramIndex}:`, parameterName);

                  this.updateParameterInfo(this.state.activeSlot, {
                        type: PARAM_TYPE.DEVICE,
                        trackName: trackName,
                        trackColor: trackColor,
                        deviceName: deviceParams.deviceName || `Device ${deviceIndexValue}`,
                        parameterName: parameterName,
                        parameterIndex: paramIndex,
                        deviceIndex: deviceIndexValue,
                        value: value,
                        valueString: valueString,
                        isQuantize: isQuantized,
                        min: min,
                        max: max
                  });
            }


            // Send confirmation OSC back to Live
            this.oscHandler.sendOscMessage("/live/learn/slot", [
                  this.state.activeSlot, PARAM_TYPE.DEVICE, CHAIN_TRACK_INDEX, deviceIndexValue, paramIndex
            ]);

            // Update display if necessary
            if (isOnCurrentPage) {
                  const slot = this.state.slots.get(this.state.activeSlot); // Re-fetch updated slot
                  if (slot && slot.min !== null && slot.max !== null && slot.max !== slot.min) {
                        const normalizedValue = Math.round(((value - slot.min) / (slot.max - slot.min)) * 100);
                        const formattedValue = normalizedValue.toString().padStart(3, '0');
                        this.communicationManager.sendMessage(
                              `ls,${displaySlot},${slot.parameterName},${formattedValue},${valueString}`,
                              this.state.isActive
                        );
                  } else {
                        // Fallback display
                        this.communicationManager.sendMessage(
                              `ls,${displaySlot},${slot?.parameterName || 'Device Param'},?,${valueString}`,
                              this.state.isActive
                        );
                  }
            }

            this.state.isLearning = false; // Learning complete
            console.log(`[DEBUG] _handleLearnDeviceValue - End - Updated slot:`, this.state.slots.get(this.state.activeSlot));
      }

      /**
     * Handles learning track volume.
     * @private
     */
      _handleLearnTrackVolume(args, realTrackIndex, displaySlot, isOnCurrentPage, trackName, trackColor) {
            // Args: [?, value]
            const value = args[1];
            this.updateParameterInfo(this.state.activeSlot, {
                  type: PARAM_TYPE.VOLUME,
                  trackName: trackName,
                  trackColor: trackColor,
                  value: value,
                  min: 0, // Live volume range
                  max: 1,
                  parameterName: 'volume' // Explicitly set name
            });
            this.oscHandler.sendOscMessage("/live/learn/slot", [this.state.activeSlot, PARAM_TYPE.VOLUME, realTrackIndex]);

            if (isOnCurrentPage) {
                  const volumePercent = Math.round(value * 100);
                  const volumeDb = this.volumeConverter.toDb(value);
                  this.communicationManager.sendMessage(
                        `ls,${displaySlot},volume,${volumePercent},${volumeDb} dB`,
                        this.state.isActive
                  );
            }
            this.state.isLearning = false;
      }

      /**
      * Handles learning track panning.
      * @private
      */
      _handleLearnTrackPanning(args, realTrackIndex, displaySlot, isOnCurrentPage, trackName, trackColor) {
            // Args: [?, value]
            const value = args[1];
            this.updateParameterInfo(this.state.activeSlot, {
                  type: PARAM_TYPE.PANNING,
                  trackName: trackName,
                  trackColor: trackColor,
                  value: value,
                  min: -1, // Live pan range
                  max: 1,
                  parameterName: 'pan' // Explicitly set name
            });
            this.oscHandler.sendOscMessage("/live/learn/slot", [this.state.activeSlot, PARAM_TYPE.PANNING, realTrackIndex]);

            if (isOnCurrentPage) {
                  const { percentage: panPercent, displayString: panDisplay } = this.convertPanToDisplay(value);
                  this.communicationManager.sendMessage(
                        `ls,${displaySlot},pan,${panPercent},${panDisplay}`,
                        this.state.isActive
                  );
            }
            this.state.isLearning = false;
      }

      /**
      * Handles learning track send level.
      * @private
      */
      _handleLearnTrackSend(args, realTrackIndex, displaySlot, isOnCurrentPage, trackName, trackColor) {
            // Args: [?, sendIndex, value]
            const sendIndex = args[1];
            const value = args[2];
            const returnTrackName = this.liveTrackManager.returnTracksName?.[sendIndex] || `send ${String.fromCharCode(65 + sendIndex)}`; // A, B, C...

            this.updateParameterInfo(this.state.activeSlot, {
                  type: PARAM_TYPE.SEND,
                  trackName: trackName,
                  trackColor: trackColor,
                  sendIndex: sendIndex,
                  value: value,
                  min: 0, // Live send range
                  max: 1,
                  parameterName: `send ${returnTrackName}` // Set specific name
            });
            this.oscHandler.sendOscMessage("/live/learn/slot", [this.state.activeSlot, PARAM_TYPE.SEND, realTrackIndex, sendIndex]);

            if (isOnCurrentPage) {
                  const sendPercent = Math.round(value * 100);
                  const sendDb = this.volumeSendConverter.toDb(value);
                  this.communicationManager.sendMessage(
                        `ls,${displaySlot},send ${returnTrackName},${sendPercent},${sendDb} dB`,
                        this.state.isActive
                  );
            }
            this.state.isLearning = false;
      }

      /**
      * Handles learning track mute state.
      * @private
      */
      _handleLearnTrackMute(args, realTrackIndex, displaySlot, isOnCurrentPage, trackName, trackColor) {
            // Args: [?, value (0 or 1)]
            const value = args[1] === 1; // Convert to boolean
            this.updateParameterInfo(this.state.activeSlot, {
                  type: PARAM_TYPE.MUTE,
                  trackName: trackName,
                  trackColor: trackColor,
                  value: value,
                  min: 0,
                  max: 1,
                  isQuantize: true, // Mute/Solo are effectively quantized boolean toggles
                  parameterName: 'Mute'
            });
            this.oscHandler.sendOscMessage("/live/learn/slot", [this.state.activeSlot, PARAM_TYPE.MUTE, realTrackIndex]);

            if (isOnCurrentPage) {
                  const percentValue = value ? 100 : 0;
                  const display = value ? 'On' : 'Off'; // Use On/Off for clarity
                  this.communicationManager.sendMessage(
                        `ls,${displaySlot},Mute,${percentValue},${display}`,
                        this.state.isActive
                  );
            }
            this.state.isLearning = false;
      }

      /**
      * Handles learning track solo state.
      * @private
      */
      _handleLearnTrackSolo(args, realTrackIndex, displaySlot, isOnCurrentPage, trackName, trackColor) {
            // Args: [?, value (0 or 1)]
            const value = args[1] === 1; // Convert to boolean
            this.updateParameterInfo(this.state.activeSlot, {
                  type: PARAM_TYPE.SOLO,
                  trackName: trackName,
                  trackColor: trackColor,
                  value: value,
                  min: 0,
                  max: 1,
                  isQuantize: true,
                  parameterName: 'Solo'
            });
            this.oscHandler.sendOscMessage("/live/learn/slot", [this.state.activeSlot, PARAM_TYPE.SOLO, realTrackIndex]);

            if (isOnCurrentPage) {
                  const percentValue = value ? 100 : 0;
                  const display = value ? 'On' : 'Off';
                  this.communicationManager.sendMessage(
                        `ls,${displaySlot},Solo,${percentValue},${display}`,
                        this.state.isActive
                  );
            }
            this.state.isLearning = false;
      }

      /**
      * Handles learning chain volume.
      * @private
      */
      _handleLearnChainVolume(args, displaySlot, isOnCurrentPage, trackName, trackColor) {
            // Args: [?, chain_path, value]
            const chainPath = args[1];
            const value = args[2];
            this.updateParameterInfo(this.state.activeSlot, {
                  type: PARAM_TYPE.CHAIN_VOLUME,
                  trackName: trackName, // Track the chain belongs to
                  trackColor: trackColor,
                  value: value,
                  min: 0,
                  max: 1,
                  chain_path: chainPath, // Store the specific chain path
                  parameterName: 'Chain Vol'
            });
            this.oscHandler.sendOscMessage("/live/learn/slot", [
                  this.state.activeSlot, PARAM_TYPE.CHAIN_VOLUME, CHAIN_TRACK_INDEX, chainPath
            ]);

            if (isOnCurrentPage) {
                  const volumePercent = Math.round(value * 100);
                  const volumeDb = this.volumeConverter.toDb(value);
                  this.communicationManager.sendMessage(
                        `ls,${displaySlot},Chain Vol,${volumePercent},${volumeDb} dB`,
                        this.state.isActive
                  );
            }
            this.state.isLearning = false;
      }

      /**
      * Handles learning chain panning.
      * @private
      */
      _handleLearnChainPanning(args, displaySlot, isOnCurrentPage, trackName, trackColor) {
            // Args: [?, chain_path, value]
            const chainPath = args[1];
            const value = args[2];
            this.updateParameterInfo(this.state.activeSlot, {
                  type: PARAM_TYPE.CHAIN_PANNING,
                  trackName: trackName,
                  trackColor: trackColor,
                  value: value,
                  min: -1,
                  max: 1,
                  chain_path: chainPath,
                  parameterName: 'Chain Pan'
            });
            this.oscHandler.sendOscMessage("/live/learn/slot", [
                  this.state.activeSlot, PARAM_TYPE.CHAIN_PANNING, CHAIN_TRACK_INDEX, chainPath
            ]);

            if (isOnCurrentPage) {
                  const { percentage: panPercent, displayString: panDisplay } = this.convertPanToDisplay(value);
                  this.communicationManager.sendMessage(
                        `ls,${displaySlot},Chain Pan,${panPercent},${panDisplay}`,
                        this.state.isActive
                  );
            }
            this.state.isLearning = false;
      }

      /**
      * Handles learning chain mute state, with debouncing to allow solo to override.
      * @private
      */
      _handleLearnChainMute(args, displaySlot, isOnCurrentPage, trackName, trackColor) {
            // Args: [?, chain_path, value (0 or 1)]
            const chainPath = args[1];
            const value = args[2] === 1;

            // Store mute info temporarily
            this.lastMuteTime = Date.now();
            this.lastMuteSlot = this.state.activeSlot;

            // Clear any existing timeout for this slot
            if (this.muteTimeout) {
                  clearTimeout(this.muteTimeout);
                  this.muteTimeout = null;
            }

            // Set a timeout to process the mute after a short delay
            this.muteTimeout = setTimeout(() => {
                  console.log(`[DEBUG] Processing delayed chain mute for slot ${this.state.activeSlot}`);
                  this.updateParameterInfo(this.state.activeSlot, {
                        type: PARAM_TYPE.CHAIN_MUTE,
                        trackName: trackName,
                        trackColor: trackColor,
                        value: value,
                        min: 0,
                        max: 1,
                        isQuantize: true,
                        chain_path: chainPath,
                        parameterName: 'Chain Mute'
                  });

                  this.oscHandler.sendOscMessage("/live/learn/slot", [
                        this.state.activeSlot, PARAM_TYPE.CHAIN_MUTE, CHAIN_TRACK_INDEX, chainPath
                  ]);

                  if (isOnCurrentPage) {
                        const percentValue = value ? 100 : 0;
                        const display = value ? 'On' : 'Off';
                        this.communicationManager.sendMessage(
                              `ls,${displaySlot},Chain Mute,${percentValue},${display}`,
                              this.state.isActive
                        );
                  }
                  this.state.isLearning = false; // Learning complete (after delay)
                  this.muteTimeout = null; // Clear timeout ID
                  this.lastMuteSlot = null; // Reset last mute tracking

            }, this.muteDebounceTime); // Wait before processing
      }

      /**
     * Handles learning chain solo state, cancelling any pending mute for the same slot.
     * @private
     */
      _handleLearnChainSolo(args, displaySlot, isOnCurrentPage, trackName, trackColor) {
            // Args: [?, chain_path, value (0 or 1)]
            const chainPath = args[1];
            const value = args[2] === 1;

            // Check if a mute message arrived recently for the *same slot*
            if (Date.now() - this.lastMuteTime < this.muteDebounceTime && this.lastMuteSlot === this.state.activeSlot) {
                  console.log(`[DEBUG] Chain solo received quickly after mute for slot ${this.state.activeSlot}, cancelling mute timeout.`);
                  // Cancel the pending mute processing
                  clearTimeout(this.muteTimeout);
                  this.muteTimeout = null;
                  this.lastMuteSlot = null; // Reset tracking
            }

            // Process solo immediately
            this.updateParameterInfo(this.state.activeSlot, {
                  type: PARAM_TYPE.CHAIN_SOLO,
                  trackName: trackName,
                  trackColor: trackColor,
                  value: value,
                  min: 0,
                  max: 1,
                  isQuantize: true,
                  chain_path: chainPath,
                  parameterName: 'Chain Solo'
            });

            this.oscHandler.sendOscMessage("/live/learn/slot", [
                  this.state.activeSlot, PARAM_TYPE.CHAIN_SOLO, CHAIN_TRACK_INDEX, chainPath
            ]);

            if (isOnCurrentPage) {
                  const percentValue = value ? 100 : 0;
                  const display = value ? 'On' : 'Off';
                  this.communicationManager.sendMessage(
                        `ls,${displaySlot},Chain Solo,${percentValue},${display}`,
                        this.state.isActive
                  );
            }
            this.state.isLearning = false; // Learning complete
      }


      // --- Utility Methods ---

      /**
       * Updates the information stored for a specific slot index.
       * Merges new information with existing information.
       * Initializes the slot if it doesn't exist.
       * @param {number} slotIndex - The index of the slot to update.
       * @param {object} newInfo - An object containing properties to update.
       * @private - Recommended to be private, but keeping public based on original structure
       */
      updateParameterInfo(slotIndex, newInfo) {
            // Ensure the slot exists in the map, initialize with defaults if not
            if (!this.state.slots.has(slotIndex)) {
                  this.state.slots.set(slotIndex, {
                        ...DEFAULT_SLOT_INFO, // Spread default properties
                        index: slotIndex      // Set the specific index
                  });
            }

            const currentInfo = this.state.slots.get(slotIndex);
            // Merge existing info with new info, new info takes precedence
            const updatedInfo = { ...currentInfo, ...newInfo };

            // Add prefix for quantized parameters for visual indication
            // Check if isQuantize is explicitly true and parameterName exists
            if (updatedInfo.isQuantize === true && updatedInfo.parameterName) {
                  // Add prefix only if it's not already there
                  if (!updatedInfo.parameterName.startsWith("|| ")) {
                        updatedInfo.parameterName = "|| " + updatedInfo.parameterName;
                  }
            } else if (updatedInfo.isQuantize === false && updatedInfo.parameterName) {
                  // Remove prefix if parameter is explicitly not quantized
                  if (updatedInfo.parameterName.startsWith("|| ")) {
                        updatedInfo.parameterName = updatedInfo.parameterName.substring(3); // Remove first 3 chars ("|| ")
                  }
            }


            // Store the updated information back into the state map
            this.state.slots.set(slotIndex, updatedInfo);
            // console.log(`[DEBUG] Slot ${slotIndex} updated:`, updatedInfo);
      }

      /**
       * Clears the information for a specific slot index and notifies the interface.
       * @param {number} slotIndex - The index of the slot to clear.
        * @private - Recommended to be private, but keeping public based on original structure
       */
      clearSlot(slotIndex) {
            // Create a new object with default values for the cleared slot
            const clearedInfo = {
                  ...DEFAULT_SLOT_INFO, // Spread default properties
                  index: slotIndex      // Set the specific index
            };

            // Update the state map with the cleared information
            this.state.slots.set(slotIndex, clearedInfo);
            console.log(`[INFO] Slot ${slotIndex} cleared in state.`);

            // Notify the communication interface if the slot is on the current page
            const slotPage = Math.floor(slotIndex / this.slotsPerPage) + 1;
            const displaySlot = slotIndex % this.slotsPerPage;
            if (slotPage === this.state.currentPage) {
                  // Send a message indicating the slot is now empty/null
                  // The format 'ls,slot,null,-,-,-,-,-' seems specific to the receiving interface.
                  this.communicationManager.sendMessage(`ls,${displaySlot},null,-,-,-,-,-`, this.state.isActive);
                  console.log(`[INFO] Sent clear message to interface for display slot ${displaySlot}`);
            }
      }

      /**
       * Converts a panning value (-1 to 1) into a percentage (0-100) and a display string (e.g., "C", "50L", "30R").
       * @param {number} panValue - The panning value from Live (-1 to 1).
       * @returns {{percentage: number, displayString: string}} - Object containing percentage and display string.
       * @private - Recommended to be private, but keeping public based on original structure
       */
      convertPanToDisplay(panValue) {
            // Clamp value just in case
            const clampedValue = Math.max(-1, Math.min(1, panValue));

            // Calculate percentage (0-100)
            const percentage = Math.round((clampedValue + 1) * 50);

            // Determine display string
            let displayString;
            if (clampedValue === 0) {
                  displayString = "C"; // Center
            } else if (clampedValue < 0) {
                  // Calculate absolute percentage for Left (1-50)
                  displayString = `${Math.round(Math.abs(clampedValue) * 50)}L`;
            } else { // panValue > 0
                  // Calculate percentage for Right (1-50)
                  displayString = `${Math.round(clampedValue * 50)}R`;
            }

            return { percentage, displayString };
      }
}

module.exports = LearnMessageHandler;