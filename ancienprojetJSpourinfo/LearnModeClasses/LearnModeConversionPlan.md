# Plan d'action pour la conversion du LearnMode JS en Go

Ce document décrit les étapes nécessaires pour porter le code JavaScript du "Learn Mode" en Go, en suivant les règles de `ConversionGuide.md` et en s'appuyant sur l'exemple du `liveVolumeMode`.

---

## 1. Lecture du guide de conversion

- Ouvrir `ConversionGuide.md` pour comprendre les conventions :
  - mapping `class` → `struct`
  - `require(...)` → `import`
  - callbacks/événements → canaux (`chan`) ou méthodes
  - gestion d'état global → structs partagés

## 2. Analyse du code JS existant

### 2.1 LearnMessageHandler.js
- Constantes (`PARAM_TYPE`, indices spéciaux) → constantes Go (`const` ou `iota`).
- `DEFAULT_SLOT_INFO` → struct Go `SlotInfo` avec valeurs par défaut.
- `class LearnMessageHandler` → struct Go `LearnMessageHandler` + méthodes associées.
- Références à `oscHandler`, `communicationManager`, `liveTrackManager`, `state` → interfaces Go.

### 2.2 liveLearnMode.js
- Initialisation du mode, binding des handlers, boucles d'écoute OSC.
- Conversion en fonction `InitLearnMode(...)` dans Go ou méthode de package.

## 3. Étude de l'exemple `liveVolumeMode`

- Parcourir `/Live/liveVolumeMode` :
  - Structure des OSC handlers en Go.
  - Modèle de `volume_oscHandlers.go`, converters, state.
- Noter patterns réutilisables (initialisation, routeurs OSC, update state).

## 4. Définition de la structure Go

- Créer un package `learnmode` sous `/Live/learnMode`.
- Définir :
  - `type SlotInfo struct { ... }`
  - `type LearnMessageHandler struct { oscHandler OscHandler; ... }
  - Interfaces Go : `OscHandler`, `CommunicationManager`, `LiveTrackManager`.
  - Converters (si besoin, copier/migrer ceux de volume pour Learn converters).

## 5. Implémentation des handlers OSC

- Traduis chaque méthode JS (`onLearnStart`, `onLearnEnd`, etc.) en méthode Go :
  ```go
  func (h *LearnMessageHandler) HandleLearnStart(msg osc.Message) { ... }
  ```
- Utiliser les API du package OSC existant.
- Mettre à jour `state` partagé (struct Go protégée par mutex si concurrent).
- Envoyer messages via `h.oscHandler.Send(...)`.

## 6. Intégration et bootstrap

- Dans `main.go` :
  ```go
  handler := learnmode.NewLearnMessageHandler(...)
  oscRouter.Register("/learn/*", handler)
  ```
- Initialiser `communicationManager` et `liveTrackManager` existants.

## 7. Tests unitaires

- Créer des tests Go (`*_test.go`) pour :
  - Conversion de slot par `SlotInfo`.
  - Handlers OSC : simulation d'un message, vérification du state et envois.

## 8. Documentation et README

- Mettre à jour README pour expliquer le nouveau dossier `learnmode`.
- Documenter les interfaces et méthodes clés.

## 9. Relecture et validation

- Vérifier la conformité aux règles de `ConversionGuide.md`.
- Comparer comportement JS vs Go (tests manuels).
- Profiling et optimisation (goroutines, canaux).

---

*Date : 2025-04-24*
