const EventEmitter = require("events");
const EncoderController = require("../Comm/encoderController");
const LiveVolumeConverter = require("./liveUtilityClasses/liveVolumeConverter");
const LiveVolumeSendConverter = require("./liveUtilityClasses/liveVolumeSendConverter");
const LearnMessageHandler = require("./LearnModeClasses/LearnMessageHandler");

class LiveLearnMode extends EventEmitter {
  constructor(oscHandler, liveTrackManager, communicationManager, sharedState) {
    super();
    this.oscHandler = oscHandler;
    this.liveTrackManager = liveTrackManager;
    this.communicationManager = communicationManager;
    this.state = sharedState.learn;
    this.globalState = sharedState.global;

    console.log("[DEBUG] Constructor - Initial liveTrackManager.returnTracksName:", this.liveTrackManager.returnTracksName);
    this.returnTracksName = this.liveTrackManager.returnTracksName;
    console.log("[DEBUG] Constructor - Initial this.returnTracksName:", this.returnTracksName);

    // Initialisation du state
    this.state.slots = new Map();
    this.state.currentPage = 1;
    this.state.isLearning = false;
    this.state.activeSlot = null;

    // S'assurer que returnTracksName est initialisé même si liveTrackManager.returnTracksName est undefined
    this.returnTracksName = this.liveTrackManager.returnTracksName || [];

    // Création du message handler avec toutes les dépendances
    this.messageHandler = new LearnMessageHandler({
      oscHandler,
      communicationManager,
      liveTrackManager,
      state: this.state
    });

    // Bind des méthodes
    this._boundTrackCountUpdate = this.onTrackCountChange.bind(this);
    this._boundSelectedTrackChange = this.onSelectedTrackDeviceChange.bind(this);
    this._boundReturnTracksNameChange = this.onReturnTracksNameChange.bind(this);
    this._boundEncoderChange = this.onEncoderChanged.bind(this);
    this._boundButtonPressed = this.onButtonPressed.bind(this);
    this._boundTouchPressed = this.onTouched.bind(this);

    // Maps pour les handlers
    this._backgroundHandlers = new Map();
    this._activeHandlers = new Map();

    this.slotsPerPage = 8;
    this.isReadyToListen = true;

    if (!this.state.slots) {
      this.state.slots = new Map(
        Array(32).fill(null).map((_, i) => [
          i,
          {
            index: i,
            type: null,
            trackColor: "",
            trackName: "",
            deviceName: "",
            parameterName: "",
            parameterIndex: null,
            sendIndex: null,
            value: null,
            valueString: "",
            isQuantize: null,
            min: null,
            max: null,
            buffer: 0,
          },
        ])
      );
    }

    this.backgroundListenAddresses = [
      "/live/learnslot/get/properties",
      "/live/tracklearn/get/panning",
      "/live/devicelearn/get/parameter/value",
      "/live/learn/slot/cleared",
      "/live/tracklearn/get/volume",
      "/live/tracklearn/get/sends",
      "/live/chainlearn/get/volume",
      "/live/chainlearn/get/panning",
      "/live/chainlearn/get/mute",
      "/live/chainlearn/get/solo",
      "/live/tracklearn/get/mute",
      "/live/tracklearn/get/solo",
    ];

    this.activeListenAddresses = [
      "/live/device/learning/bulk_parameters",
      "/live/device/learning/name",
      "/live/track/learning/panning",
      "/live/track/learning/volume",
      "/live/track/learning/sends",
      "/live/device/learning/parameter/value",
      "/live/device/learning/parameter/value_string",
      "/live/chain/learning/volume",
      "/live/chain/learning/panning",
      "/live/track/learning/mute",
      "/live/track/learning/solo",
      "/live/chain/learning/mute",
      "/live/chain/learning/solo",
      "/live/readyToListen",
      "/live/get/lockedDevice/paramProperties",
    ];

    this.encoderController = new EncoderController();
    this.volumeConverter = new LiveVolumeConverter();
    this.volumeSendConverter = new LiveVolumeSendConverter();
    this.onButtonPressed = this.onButtonPressed.bind(this);

    this.deviceParameters = new Map();
    this.trackNames = new Map();
    this.trackColors = new Map();
  }

  backgroundStart() {
    this.oscHandler.sendOscMessage("/live/learn/stop", []);
    console.log("\n=== Starting LearnMode background listeners ===");

    this.backgroundListenAddresses.forEach((address) => {
      const handler = (args) => this.handleOscMessage(args, address);
      this.oscHandler.on(address, handler);
      this._backgroundHandlers.set(address, handler);
    });

    this.setPage(this.state.currentPage);
    this.communicationManager.sendMessage(`lp,${this.state.currentPage}`, this.state.isActive);
  }

  start() {
    // Activer explicitement le mode
    this.state.isActive = true;

    console.log("\n=== Starting LearnMode active listeners ===");
    this.state.currentPage = this.state.currentPage || 1;
    this.learnSlot = this.state.activeSlot;

    // Ajouter les listeners actifs
    this.activeListenAddresses.forEach((address) => {
      const handler = (args) => this.handleOscMessage(args, address);
      this.oscHandler.on(address, handler);
      this._activeHandlers.set(address, handler);
    });

    // Ajouter les listeners de communication
    this.communicationManager.on('encoderChange', this._boundEncoderChange);
    this.communicationManager.on('buttonPressed', this._boundButtonPressed);
    this.communicationManager.on('touchPressed', this._boundTouchPressed);

    // Ajouter les listeners d'événements de base
    this.liveTrackManager.on("handleTrackCountUpdate", this._boundTrackCountUpdate);
    this.liveTrackManager.on("selectedTrackDeviceUpdate", this._boundSelectedTrackChange);
    this.liveTrackManager.on("returnTracksNameChange", this._boundReturnTracksNameChange);

    this.sendStartListen();
    //this.oscHandler.sendOscMessage("/live/learn_mode/", [1]);

    this.communicationManager.sendMessage("mo,3", this.state.isActive);
  }

  async stopBackground() {
    console.log("\n=== Stopping LearnMode background listeners ===");

    // Nettoyer les listeners background
    this._backgroundHandlers.forEach((handler, address) => {
      this.oscHandler.removeListener(address, handler);
    });
    this._backgroundHandlers.clear();
  }

  async cleanup() {
    console.log("\n=== LearnMode Cleanup Start ===");

    // Désactiver explicitement le mode
    this.state.isActive = false;

    this.oscHandler.sendOscMessage("/live/learn/stop_device", []);
    this.oscHandler.sendOscMessage("/live/learn/stop_track", []);
    // Nettoyer les listeners actifs
    this._activeHandlers.forEach((handler, address) => {
      this.oscHandler.removeListener(address, handler);
    });
    this._activeHandlers.clear();

    // Retirer les listeners de communication
    this.communicationManager.removeListener('encoderChange', this._boundEncoderChange);
    this.communicationManager.removeListener('buttonPressed', this._boundButtonPressed);
    this.communicationManager.removeListener('touchPressed', this._boundTouchPressed);

    // Supprimer les listeners d'événements
    this.liveTrackManager.off("handleTrackCountUpdate", this._boundTrackCountUpdate);
    this.liveTrackManager.off("selectedTrackDeviceUpdate", this._boundSelectedTrackChange);
    this.liveTrackManager.off("returnTracksNameChange", this._boundReturnTracksNameChange);

    console.log("=== LearnMode Cleanup Complete ===\n");
  }

  async cleanupForSwitch() {
    await this.cleanup();
    this.globalState.learnData = {
      param_type: null,
      track_index: null,
      send_index: null,
      device_index: null,
      param_index: null
    };
  }

  async cleanupForExit() {
    await this.cleanup();
    await this.stopBackground();
    await this.oscHandler.sendOscMessage("/live/learn/stop", []);
  }

  sendStartListen() {
    console.log("Sending start_listen messages");
    this.oscHandler.sendOscMessage("/live/learn/setup_track", []);
    this.oscHandler.sendOscMessage("/live/learn/setup_device", []);
  }

  handleOscMessage(args, address) {
    console.log(`[DEBUG] LiveLearnMode handleOscMessage - Start - isLearning: ${this.state.isLearning}, address: ${address}`);
    const realTrackIndex = Math.abs(args[0]) - 1;

    if (this.state.isLearning) {
      this.messageHandler.handleLearningMessages(args, address, realTrackIndex);
      return;
    }

    switch (address) {
      case "/live/device/learning/bulk_parameters":
      case "/live/device/learning/name":
        this.messageHandler.handleDeviceParameters(args, address);
        break;

      case "/live/tracklearn/get/volume":
      case "/live/tracklearn/get/panning":
      case "/live/tracklearn/get/sends":
      case "/live/tracklearn/get/mute":
      case "/live/tracklearn/get/solo":
      case "/live/devicelearn/get/parameter/value":
      case "/live/chainlearn/get/volume":
      case "/live/chainlearn/get/panning":
      case "/live/chainlearn/get/mute":
      case "/live/chainlearn/get/solo":
      case "/live/get/lockedDevice/paramProperties":
        this.messageHandler.handleParameterUpdates(args, address);
        break;

      case "/live/learnslot/get/properties":
        this.messageHandler.handleSlotProperties(args);
        break;

      case "/live/learn/slot/cleared":
        this.messageHandler.handleSlotCleared(args);
        break;
    }
    console.log(`[DEBUG] LiveLearnMode handleOscMessage - End - isLearning: ${this.state.isLearning}`);
  }

  getDisplayIndex(trackIdx) {
    const normalTrackCount = this.liveTrackManager.trackCount - this.liveTrackManager.getReturnTrackCount() - 1;
    return trackIdx >= normalTrackCount && trackIdx < this.liveTrackManager.trackCount - 1
      ? String.fromCharCode(65 + (trackIdx - normalTrackCount))
      : (trackIdx + 1).toString();
  }
  updateParameterInfo(slotIndex, updates) {
    if (!this.state.slots.has(slotIndex)) {
      this.state.slots.set(slotIndex, {
        index: slotIndex,
        type: null,
        trackColor: "",
        trackName: "",
        deviceName: "",
        parameterName: "",
        parameterIndex: null,
        sendIndex: null,
        value: null,
        valueString: "",
        isQuantize: null,
        min: null,
        max: null,
        buffer: 0,
      });
    }

    const currentInfo = this.state.slots.get(slotIndex);
    const updatedInfo = { ...currentInfo, ...updates };
    /*   console.log(`Mise à jour du slot ${slotIndex}:`, {
        avant: currentInfo,
        miseAJour: updates,
        après: updatedInfo
      }); */
    this.state.slots.set(slotIndex, updatedInfo);
  }

  startLearning(slotIndex) {
    console.log(`[DEBUG] startLearning - Before - isLearning: ${this.state.isLearning}`);
    this.state.isLearning = true;
    this.state.activeSlot = slotIndex;
    this.learnSlot = slotIndex;
    console.log(`[DEBUG] startLearning - After - isLearning: ${this.state.isLearning}`);
  }

  onEncoderChanged(encoderIndex, direction) {
    const paramInfo = this.state.slots.get(encoderIndex);
    let indexBis = -3;

    if (paramInfo.type == null) {
      console.log(
        `Encoder ${encoderIndex} has no associated parameter on this page`
      );
      return;
    }

    // Pour mute et solo, on utilise une logique binaire simple
    if (paramInfo.type === 7 || paramInfo.type === 8) {
      const newValue = direction > 0 ? 1 : 0;
      if (newValue !== paramInfo.value) {
        paramInfo.value = newValue;
        this.sendParameterUpdate(encoderIndex, paramInfo.type, indexBis, newValue);
        console.log(`Encoder ${encoderIndex} value changed to ${newValue}`);
      }
      this.state.slots.set(encoderIndex, paramInfo);
      return;
    }

    if (paramInfo.type == 3) {
      indexBis = paramInfo.sendIndex;
    } else if (paramInfo.type == 4) {
      indexBis = paramInfo.parameterIndex;
    }

    const key = `${encoderIndex}-${paramInfo.type}-${indexBis}`;
    let newValue;

    if (!paramInfo.isQuantize) {
      const minStep = (paramInfo.max - paramInfo.min) / 1000;
      const maxStep = (paramInfo.max - paramInfo.min) / 40;
      console.log("paramInfo:", JSON.stringify(paramInfo, null, 2));
      console.log("key:", key);
      console.log("direction:", direction);
      console.log("current value:", paramInfo.value);
      console.log("min:", paramInfo.min);
      console.log("max:", paramInfo.max);
      newValue = this.encoderController.updateParameter(
        key,
        direction,
        paramInfo.value,
        paramInfo.min,
        paramInfo.max,
        minStep,
        maxStep
      );
    } else {
      if (!paramInfo.hasOwnProperty('buffer')) {
        paramInfo.buffer = 0;
        paramInfo.lastDirection = 0;
      }

      if (direction !== paramInfo.lastDirection) {
        paramInfo.buffer = 0;
      }

      const bufferStep = 0.25;
      paramInfo.buffer += direction * bufferStep;

      if (Math.abs(paramInfo.buffer) >= 1) {
        const change = Math.sign(paramInfo.buffer);
        newValue = Math.max(paramInfo.min, Math.min(paramInfo.max, paramInfo.value + change));
        paramInfo.buffer -= change;
      } else {
        newValue = paramInfo.value;
      }

      paramInfo.lastDirection = direction;
    }

    if (newValue !== paramInfo.value) {
      paramInfo.value = newValue;
      this.sendParameterUpdate(encoderIndex, paramInfo.type, indexBis, newValue);
      console.log(`Encoder ${encoderIndex} value changed to ${newValue}`);
    }

    this.state.slots.set(encoderIndex, paramInfo);
  }

  sendParameterUpdate(index, type, indexBis, newValue) {
    this.oscHandler.sendOscMessage("/live/learn/set/value", [parseInt(index), parseInt(type), parseInt(indexBis), parseFloat(newValue)]);
    if (type === 5 || type === 6) { // Volume ou Pan de chaîne
      const slot = this.state.slots.get(index);
      if (slot && slot.chain_path) {
        // Utiliser le chemin de la chaîne stocké
        const messageType = type === 5 ? "volume" : "panning";
        this.oscHandler.sendOscMessage(`/live/chain/set/${messageType}`, [slot.chain_path, newValue]);
      }
    }
  }

  onButtonPressed(buttonIndex) {
    this.startLearning(buttonIndex);
  }

  clearSlot(slotIndex) {
    const slotPage = Math.floor(slotIndex / this.slotsPerPage) + 1;
    const displaySlot = slotIndex % this.slotsPerPage;
    const isOnCurrentPage = slotPage === this.state.currentPage;
    if (isOnCurrentPage) {
      this.communicationManager.sendMessage(
        `ls,${displaySlot},null,-,-,-,-,-`,
        this.state.isActive
      );
    }
    const clearedInfo = {
      index: slotIndex,
      type: null,
      trackColor: "",
      trackName: "",
      deviceName: "",
      parameterName: "",
      parameterIndex: null,
      sendIndex: null,
      value: null,
      valueString: "",
      isQuantize: null,
      min: null,
      max: null,
      buffer: 0,
    };

    this.state.slots.set(slotIndex, clearedInfo);
    console.log(`Les informations du slot ${slotIndex} ont été réinitialisées`);
  }

  pageUp() {
    const { currentPage, totalPages } = this.getPageInfo();
    if (currentPage < totalPages) {
      this.setPage(currentPage + 1);
    }
  }

  pageDown() {
    const { currentPage } = this.getPageInfo();
    if (currentPage > 1) {
      this.setPage(currentPage - 1);
    }
  }

  onSelectedTrackDeviceChange() {
    console.log(`Selected track and/or device changed`);
    //this.isReadyToListen = false;
    this.sendStartListen();
  }

  onTrackCountChange() {
    console.log(`trackCount and/or returnTrackCount changed`);
    //this.isReadyToListen = false;
    this.sendStartListen();
  }

  onReturnTracksNameChange() {
    console.log(`Return tracks name changed`);

    // Parcourir tous les slots
    this.state.slots.forEach((slotInfo, slotIndex) => {
      // Vérifier si c'est un slot de type send (3)
      if (slotInfo.type === 3) {
        // Mettre à jour le nom du paramètre avec le nouveau nom de la piste return
        const returnTrackName = this.liveTrackManager.returnTracksName[slotInfo.sendIndex];
        if (returnTrackName) {
          slotInfo.parameterName = returnTrackName;

          // Si le slot est sur la page courante, mettre à jour l'affichage
          const slotPage = Math.floor(slotIndex / this.slotsPerPage) + 1;
          if (slotPage === this.state.currentPage) {
            const displaySlot = slotIndex % this.slotsPerPage;
            const value = Math.round(slotInfo.value * 100);
            const display = this.volumeSendConverter.toDb(slotInfo.value);

            this.sendLearnSlotMessage({
              displaySlot,
              paramName: returnTrackName,
              value,
              display,
              trackName: slotInfo.trackName,
              trackColor: slotInfo.trackColor
            });
          }
        }
      }
    });
  }

  async setPage(page) {
    console.log("[DEBUG] setPage - Début - returnTracksName:", this.returnTracksName);
    console.log("[DEBUG] setPage - liveTrackManager.returnTracksName:", this.liveTrackManager.returnTracksName);

    this.state.isLearning = false;
    this.state.currentPage = page;
    const startSlot = (page - 1) * this.slotsPerPage;
    const endSlot = startSlot + this.slotsPerPage;
    this.communicationManager.sendMessage(`lp,${page}`, this.state.isActive);

    // Ajouter un délai initial après le changement de page
    await new Promise(resolve => setTimeout(resolve, 1));

    for (let slotIndex = startSlot; slotIndex < endSlot; slotIndex++) {
      const displaySlot = slotIndex % this.slotsPerPage;
      const slotInfo = this.state.slots.get(slotIndex);

      if (!slotInfo || slotInfo.type === null) {
        await this.communicationManager.sendMessage(`ls,${displaySlot},null,-,-,-,-,-`, this.state.isActive);
        await new Promise(resolve => setTimeout(resolve, 1));
        continue;
      }

      if (slotInfo.type === 3) {
        console.log(`[DEBUG] setPage - Slot ${slotIndex} - Send Info:`, {
          sendIndex: slotInfo.sendIndex,
          returnTracksName: this.returnTracksName,
          liveManagerReturnTracksName: this.liveTrackManager.returnTracksName
        });
      }

      // Préparer les données communes
      const baseInfo = {
        displaySlot,
        trackName: slotInfo.trackName,
        trackColor: slotInfo.trackColor
      };

      // Formater le message selon le type
      switch (slotInfo.type) {
        case 1: { // Volume
          const value = Math.round(slotInfo.value * 100);
          const display = this.volumeConverter.toDb(slotInfo.value);
          this.sendLearnSlotMessage({
            ...baseInfo,
            paramName: 'volume',
            value,
            display
          });
          break;
        }
        case 2: { // Pan
          const { percentage, displayString } = this.convertPanToDisplay(slotInfo.value);
          this.sendLearnSlotMessage({
            ...baseInfo,
            paramName: 'pan',
            value: percentage,
            display: displayString
          });
          break;
        }
        case 3: { // Send
          const value = Math.round(slotInfo.value * 100);
          const display = this.volumeSendConverter.toDb(slotInfo.value);
          const returnTrackName = this.liveTrackManager.returnTracksName[slotInfo.sendIndex];
          this.sendLearnSlotMessage({
            ...baseInfo,
            paramName: `send ${returnTrackName}`,
            value,
            display
          });
          break;
        }
        case 4: { // Device Parameter
          const value = Math.round(
            ((slotInfo.value - slotInfo.min) / (slotInfo.max - slotInfo.min)) * 100
          );
          let paramName = slotInfo.parameterName;
          if (slotInfo.isQuantize && !paramName.startsWith("|| ")) {
            paramName = "|| " + paramName;
          }
          this.sendLearnSlotMessage({
            ...baseInfo,
            paramName,
            value,
            display: slotInfo.valueString,
            deviceName: slotInfo.deviceName
          });
          break;
        }
        case 5: { // Chain Volume
          const value = Math.round(slotInfo.value * 100);
          const display = this.volumeConverter.toDb(slotInfo.value);
          this.sendLearnSlotMessage({
            ...baseInfo,
            paramName: 'chain volume',
            value,
            display,
            deviceName: slotInfo.deviceName
          });
          break;
        }
        case 6: { // Chain Pan
          const { percentage, displayString } = this.convertPanToDisplay(slotInfo.value);
          this.sendLearnSlotMessage({
            ...baseInfo,
            paramName: 'chainpan',
            value: percentage,
            display: displayString,
            deviceName: slotInfo.deviceName
          });
          break;
        }
        case 7: { // Mute
          const value = slotInfo.value ? 100 : 0;
          this.sendLearnSlotMessage({
            ...baseInfo,
            paramName: 'mute',
            value,
            display: slotInfo.value ? 'on' : 'off'
          });
          break;
        }
        case 8: { // Solo
          const value = slotInfo.value ? 100 : 0;
          this.sendLearnSlotMessage({
            ...baseInfo,
            paramName: 'solo',
            value,
            display: slotInfo.value ? 'on' : 'off'
          });
          break;
        }
      }

      // Ajouter un délai de 1ms après chaque envoi dans le switch
      await new Promise(resolve => setTimeout(resolve, 1));
    }
  }

  // Nouvelle méthode helper pour formater et envoyer les messages ls
  sendLearnSlotMessage({ displaySlot, paramName, value, display, deviceName, trackName, trackColor }) {

    if (paramName && (paramName === 'volume' || paramName === 'chain volume' || paramName.startsWith('send'))) {
      display = display + " dB";
    }

    const formattedValue = value.toString().padStart(3, '0');
    this.communicationManager.sendMessage(
      `ls,${displaySlot},${paramName},${formattedValue},${display}`,
      this.state.isActive
    );

    this.communicationManager.sendMessage(
      `lo,${displaySlot},${deviceName || ''},${trackName || '-'},${trackColor || 0}`,
      this.state.isActive
    );
  }

  convertPanToDisplay(panValue) {
    const percentage = Math.round((panValue + 1) * 50);

    let displayString;
    if (panValue === 0) {
      displayString = "C";
    } else if (panValue < 0) {
      displayString = `${Math.abs(Math.round(panValue * 50))}L`;
    } else {
      displayString = `${Math.round(panValue * 50)}R`;
    }

    return { percentage, displayString };
  }

  getPageInfo() {
    const totalSlots = 32;
    const totalPages = Math.ceil(totalSlots / this.slotsPerPage);
    return {
      currentPage: this.state.currentPage,
      totalPages: totalPages,
      startSlot: (this.state.currentPage - 1) * this.slotsPerPage,
      endSlot: Math.min(this.state.currentPage * this.slotsPerPage, totalSlots)
    };
  }

  onTouched(type, index) {
    console.log(`onTouched appelé avec type=${type}, index=${index}`);

    if (type === "lst") {
      const absoluteSlotIndex = index + this.slotsPerPage * (this.state.currentPage - 1);
      this.startLearning(absoluteSlotIndex);
    } else if (type === "sm") {
      if (this.state.isLearning) {
        this.state.isLearning = false;
        this.oscHandler.sendOscMessage("/live/learn/del_slot", [this.state.activeSlot]);
      }

      // Gestion du changement de mode
      if (index === 1) {
        console.log("Mode volume");
        this.emit('modeChange', 'volume');
      } else if (index === 0) {
        console.log("Mode track");
        this.emit('modeChange', 'track');
      } else if (index === 2) {
        console.log("Mode device");
        this.emit('modeChange', 'device');
      } else if (index === 4) {
        console.log("Mode browser");
        this.emit('modeChange', 'browser');
      }
    } else if (type === "cl") {
      const absoluteIndex = index + this.slotsPerPage * (this.state.currentPage - 1);
      const slotInfo = this.state.slots.get(absoluteIndex);
      console.log("globalState.learnData", this.globalState.learnData);
      if (this.globalState.learnData.param_type !== null) {
        this.startLearning(absoluteIndex);
        // Gestion différente selon le type de paramètre

        switch (this.globalState.learnData.param_type) {
          case 1: // Volume
            // Au lieu d'envoyer un message OSC, on simule directement la réponse
            this.messageHandler.handleLearningMessages(
              [this.globalState.learnData.track_index + 1, this.globalState.learnData.value || 0],
              "/live/track/learning/volume",
              this.globalState.learnData.track_index
            );
            break;
          case 2: // Pan
            // Simuler la réponse pour le pan
            this.messageHandler.handleLearningMessages(
              [this.globalState.learnData.track_index + 1, this.globalState.learnData.value || 0],
              "/live/track/learning/panning",
              this.globalState.learnData.track_index
            );
            break;
          case 3: // Send
            // Simuler la réponse pour le send
            this.messageHandler.handleLearningMessages(
              [this.globalState.learnData.track_index + 1, this.globalState.learnData.send_index, this.globalState.learnData.value || 0],
              "/live/track/learning/sends",
              this.globalState.learnData.track_index
            );
            break;
          case 4: // Device parameter
            this.oscHandler.sendOscMessage("/live/device/learning/parameter/value",
              [-3, this.globalState.learnData.device_index, this.globalState.learnData.param_index]
            );
            break;
          case 5: // Chain volume
            // Simuler la réponse pour le volume de la chaîne avec chain_id spécial -1
            this.messageHandler.handleLearningMessages(
              [-2, this.globalState.learnData.chain_path, this.globalState.learnData.value || 0],
              "/live/chain/learning/volume",
              -1
            );
            break;
          case 6: // Chain pan
            // Simuler la réponse pour le pan de la chaîne avec chain_id spécial -1
            this.messageHandler.handleLearningMessages(
              [-2, this.globalState.learnData.chain_path, this.globalState.learnData.value || 0],
              "/live/chain/learning/panning",
              -1
            );
            break;
          case 7: // Mute
            this.messageHandler.handleLearningMessages(
              [this.globalState.learnData.track_index + 1, this.globalState.learnData.value ? 1 : 0],
              "/live/track/learning/mute",
              this.globalState.learnData.track_index
            );
            break;
          case 8: // Solo
            this.messageHandler.handleLearningMessages(
              [this.globalState.learnData.track_index + 1, this.globalState.learnData.value ? 1 : 0],
              "/live/track/learning/solo",
              this.globalState.learnData.track_index
            );
            break;
          case 9: // Chain mute
            this.messageHandler.handleLearningMessages(
              [-2, this.globalState.learnData.chain_path, this.globalState.learnData.value ? 1 : 0],
              "/live/chain/learning/mute",
              this.globalState.learnData.track_index
            );
            break;
          case 10: // Chain solo
            this.messageHandler.handleLearningMessages(
              [-2, this.globalState.learnData.chain_path, this.globalState.learnData.value ? 1 : 0],
              "/live/chain/learning/solo",
              this.globalState.learnData.track_index
            );
            break;
        }

        // Réinitialisation du buffer learnData
        this.globalState.learnData = {
          param_type: null,
          track_index: null,
          device_index: null,
          param_index: null
        };
        return;
      }
      if (!slotInfo || slotInfo.type === null) {
        // Si le slot est vide, démarrer l'apprentissagez
        if (this.state.isLearning && this.state.activeSlot === absoluteIndex) {
          this.state.isLearning = false;
          this.oscHandler.sendOscMessage("/live/learn/del_slot", [this.state.activeSlot]);
        }
        else {
          this.communicationManager.sendMessage(`ls,${index},learn,-,-,-,-,-`, this.state.isActive);
          this.startLearning(absoluteIndex);
        }
      } else {
        // Si le slot contient déjà un paramètre, envoyer le message de sélection
        this.oscHandler.sendOscMessage("/live/learn/select", [absoluteIndex]);
      }
    } else if (type === "lp") {
      const absoluteIndex = index + this.slotsPerPage * (this.state.currentPage - 1);
      this.oscHandler.sendOscMessage("/live/learn/del_slot", [absoluteIndex])
    } else if (type === "ly") {
      const startSlot = (this.state.currentPage - 1) * this.slotsPerPage;
      const absoluteSlotIndex = startSlot + index;
      const slotInfo = this.state.slots.get(absoluteSlotIndex);

      if (slotInfo && slotInfo.type !== null) {
        let indexBis = -3;
        let value;

        switch (slotInfo.type) {
          case 1:
            this.oscHandler.sendOscMessage("/live/learn/set", [absoluteSlotIndex, 0.85]);
            break;
          case 2:
            this.oscHandler.sendOscMessage("/live/learn/set", [absoluteSlotIndex, 0]);
            break;
          case 3:
            indexBis = slotInfo.sendIndex;
            this.oscHandler.sendOscMessage("/live/learn/set", [absoluteSlotIndex, 0]);
            break;
          case 4:
            if (slotInfo.isQuantize) {
              // Si la valeur actuelle est égale à la valeur maximale, on revient à la valeur minimale
              const nextValue = slotInfo.value >= slotInfo.max ? slotInfo.min : slotInfo.value + 1;
              this.oscHandler.sendOscMessage("/live/learn/set", [absoluteSlotIndex, nextValue]);
            } else {
              console.log(`Le paramètre du slot ${absoluteSlotIndex} n'est pas quantifié, aucune action nécessaire.`);
            }
            break;
          case 5:
            this.oscHandler.sendOscMessage("/live/learn/set", [absoluteSlotIndex, 0.85]);
            break;
          case 6:
            this.oscHandler.sendOscMessage("/live/learn/set", [absoluteSlotIndex, 0]);
            break;
          case 7:
            this.oscHandler.sendOscMessage("/live/learn/set", [absoluteSlotIndex, 1]);
            break;
          case 8:
            this.oscHandler.sendOscMessage("/live/learn/set", [absoluteSlotIndex, 1]);
            break;
          case 9:
            this.oscHandler.sendOscMessage("/live/learn/set", [absoluteSlotIndex, 1]);
            break;
          case 10:
            this.oscHandler.sendOscMessage("/live/learn/set", [absoluteSlotIndex, 1]);
            break;
          default:
            console.log(`Type de slot non géré pour "ly": ${slotInfo.type}`);
        }
      } else {
        console.log(`Aucune information de slot trouvée pour l'index absolu ${absoluteSlotIndex} ou type de slot null.`);
      }
    }
  }
}
module.exports = LiveLearnMode;
