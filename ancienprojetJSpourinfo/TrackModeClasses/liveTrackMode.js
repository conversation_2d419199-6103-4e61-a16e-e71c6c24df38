const EventEmitter = require("events");
const EncoderController = require("../Comm/encoderController");
const LiveVolumeConverter = require("./liveUtilityClasses/liveVolumeConverter");
const LiveVolumeSendConverter = require("./liveUtilityClasses/liveVolumeSendConverter");
const TrackModeDisplayManager = require("./TrackModeClasses/TrackModeDisplayManager");

const TRACK_MODE_CONFIG = {
  SLOTS_PER_PAGE: 8,
  SENDS_PER_PAGE: 4,
  DEFAULT_TRACK_DATA: {
    volume: 0,
    panning: 0,
    sends: [],
    name: "",
    color: "",
    mute: false,
    mutedViaSolo: false,
    solo: false,
    arm: 0,
    trackIsFoldable: false,
    trackFoldState: false,
    trackIsGrouped: false,
    displayIndex: "",
    formattedTrackNumber: ""
  },
  BACKGROUND_LISTEN_ADDRESSES: [
    "/live/trackMode/get/volume",
    "/live/trackMode/get/color",
    "/live/trackMode/get/name",
    "/live/trackMode/get/panning",
    "/live/trackMode/get/mute",
    "/live/trackMode/get/solo",
    "/live/trackMode/get/muted_via_solo",
    "/live/trackMode/get/arm",
    "/live/trackMode/get/sends",
    "/live/start_listen_trackMode",
    "/live/track_unlock",
    "/live/track_lock",
    "/live/trackLockMode/get/volume",
    "/live/trackLockMode/get/color",
    "/live/trackLockMode/get/name",
    "/live/trackLockMode/get/panning",
    "/live/trackLockMode/get/mute",
    "/live/trackLockMode/get/solo",
    "/live/trackLockMode/get/muted_via_solo",
    "/live/trackLockMode/get/arm",
    "/live/trackLockMode/get/sends",
    "/live/start_listen_trackLockMode",
  ]
};

class LiveTrackMode extends EventEmitter {
  constructor(oscHandler, liveTrackManager, communicationManager, sharedState) {
    super();
    this.oscHandler = oscHandler;
    this.liveTrackManager = liveTrackManager;
    this.communicationManager = communicationManager;
    this.state = sharedState.track;
    this.globalState = sharedState.global;

    // Initialisation du state
    this.state.isActive = false;
    this.state.currentPage = 1;
    this.state.isLocked = false;
    this.state.isQuickView = false;

    this.selectedTrackData = {
      volume: 0,
      panning: 0,
      sends: [],
      name: "",
      color: "",
      mute: false,
      mutedViaSolo: false,
      solo: false,
    };

    // Création du displayManager avec toutes les dépendances
    this.displayManager = new TrackModeDisplayManager({
      communicationManager,
      state: this.state
    });

    // Initialisation des autres propriétés
    this.lockedTrackData = null;
    this.lockedTrackIndex = null;
    this.slotsPerPage = TRACK_MODE_CONFIG.SLOTS_PER_PAGE;
    this.sendsPerPage = TRACK_MODE_CONFIG.SENDS_PER_PAGE;

    // Bind des méthodes
    this._boundHandleOscMessage = this.handleOscMessage.bind(this);
    this._boundSelectedTrackChange = this.onSelectedTrackDeviceChange.bind(this);
    this._boundReturnTracksNameChange = this.handleReturnTracksNameChange.bind(this);
    this._boundEncoderChange = this.onEncoderChanged.bind(this);
    this._boundButtonPressed = this.onButtonPressed.bind(this);
    this._boundTouchPressed = this.onTouched.bind(this);

    // Initialisation des handlers et commandes
    this.initializeHandlers();
    this.initializeCommands();

    this.backgroundListenAddresses = TRACK_MODE_CONFIG.BACKGROUND_LISTEN_ADDRESSES;

    this.activeListenAddresses = [


    ];

    if (this.selectedTrackData) {
      this.validateTrackData(this.selectedTrackData);
    }

    this.volumeConverter = new LiveVolumeConverter();
    this.volumeSendConverter = new LiveVolumeSendConverter();
    this.encoderController = new EncoderController();
  }

  initializeHandlers() {
    this.messageHandlers = {
      "/live/track_unlock": () => this.handleTrackUnlock(),
      "/live/track_lock": () => this.handleTrackLock(),
      "/live/start_listen_trackMode": (args) => this.handleInitialTrackData(args, false),
      "/live/start_listen_trackLockMode": (args) => this.handleInitialTrackData(args, true)
    };
  }

  initializeCommands() {
    this.commands = {
      pageUp: () => this.setPage(this.state.currentPage + 1),
      pageDown: () => this.setPage(this.state.currentPage - 1),
      toggleLock: () => this.state.isLocked ? this.disableLock() : this.enableLock()
    };
  }

  initializeMode(isQuickView = false) {
    this.state.isQuickView = isQuickView;

    if (this.state.isQuickView) {
      this.communicationManager.sendMessage("tq0", this.state.isActive);
      this.updateTrackDisplay(this.selectedTrackData);
    } else {
      this.communicationManager.sendMessage("tq1", this.state.isActive);
      if (this.state.isLocked && this.lockedTrackData) {
        this.updateTrackDisplay(this.lockedTrackData);
        this.communicationManager.sendMessage("lt01", this.state.isActive);
      } else {
        this.updateTrackDisplay(this.selectedTrackData);
      }
    }
  }

  backgroundStart() {
    console.log("\n=== Starting background listeners ===");
    console.log("Existing background handlers:", this._backgroundHandlers ? [...this._backgroundHandlers.keys()] : 'none');

    this.displayManager.updateLockState(false);
    this.oscHandler.sendOscMessage("/live/track_lock", "0");

    this.backgroundListenAddresses.forEach((address) => {
      if (this._backgroundHandlers?.has(address)) {
        console.log(`WARNING: Handler already exists for ${address}`);
        return;
      }

      console.log(`Registering background listener for: ${address}`);
      const backgroundHandler = (args) => this._boundHandleOscMessage(args, address);
      this.oscHandler.on(address, backgroundHandler);
      this._backgroundHandlers = this._backgroundHandlers || new Map();
      this._backgroundHandlers.set(address, backgroundHandler);
    });


    this.liveTrackManager.on("selectedTrackDeviceUpdate", this._boundSelectedTrackChange);
    this.liveTrackManager.on("returnTracksNameChange", this._boundReturnTracksNameChange);

    const returnTracksNames = this.liveTrackManager.getReturnTracksName();
    if (returnTracksNames && returnTracksNames.length > 0) {
      this.handleReturnTracksNameChange(returnTracksNames);
    }

    this.oscHandler.sendOscMessage("/live/start_listen_trackMode");
  }

  start() {
    console.log("=== Starting active listeners ===");

    // Activer explicitement le mode
    this.state.isActive = true;

    this.oscHandler.sendOscMessage("/live/song/start_listen/position");

    if (this.state.isQuickView) {
      this.communicationManager.sendMessage("mo,0", this.state.isActive);
    }

    const listenAddresses = this.state.isQuickView ?
      this.activeListenAddresses.filter(addr => !addr.includes('track_lock')) :
      this.activeListenAddresses;

    listenAddresses.forEach((address) => {
      console.log(`Registering active listener for: ${address}`);
      const activeHandler = (args) => this._boundHandleOscMessage(args, address);
      this.oscHandler.on(address, activeHandler);
      this._activeHandlers = this._activeHandlers || new Map();
      this._activeHandlers.set(address, activeHandler);
    });

    this.communicationManager.on("encoderChange", this._boundEncoderChange);
    this.communicationManager.on('buttonPressed', this._boundButtonPressed);
    this.communicationManager.on('touchPressed', this._boundTouchPressed);

    this.communicationManager.sendMessage("mo,0", this.state.isActive);
  }

  async cleanup() {
    console.log("=== Cleaning up active listeners ===");
    console.log("Current active handlers:", this._activeHandlers ? [...this._activeHandlers.keys()] : 'none');
    //console.log("Sending /live/track/clear_listeners");
    //await this.oscHandler.sendOscMessage("/live/track/clear_listeners");
    this.oscHandler.sendOscMessage("/live/song/stop_listen/position");
    // Désactiver explicitement le mode
    this.state.isActive = false;

    if (this._activeHandlers) {
      this._activeHandlers.forEach((handler, address) => {
        this.oscHandler.removeListener(address, handler);
      });
      this._activeHandlers.clear();
    }

    this.communicationManager.removeListener("encoderChange", this._boundEncoderChange);
    this.communicationManager.removeListener('buttonPressed', this._boundButtonPressed);
    this.communicationManager.removeListener('touchPressed', this._boundTouchPressed);
  }

  async cleanupForSwitch() {
    // Pour un changement de mode, on ne nettoie que les listeners actifs
    await this.cleanup();
  }

  async cleanupForExit() {
    // Pour une sortie complète, on nettoie tout
    await this.cleanup();
    await this.stopBackground();
  }

  async stopBackground() {
    console.log("\n=== Cleaning up background listeners ===");
    console.log("Current background handlers:", this._backgroundHandlers ? [...this._backgroundHandlers.keys()] : 'none');

    if (this._backgroundHandlers) {
      this._backgroundHandlers.forEach((handler, address) => {
        console.log(`Removing listener for: ${address}`);
        this.oscHandler.removeListener(address, handler);
      });
      this._backgroundHandlers.clear();
    }

    this.liveTrackManager.removeListener("selectedTrackDeviceUpdate", this._boundSelectedTrackChange);
    this.liveTrackManager.removeListener("returnTracksNameChange", this._boundReturnTracksNameChange);
  }

  handleOscMessage(args, address) {
    console.log(`=== OSC Message received ===`);
    console.log(`Address: ${address}`);

    // Si c'est un handler spécial, on l'exécute directement
    if (this.messageHandlers[address]) {
      return this.messageHandlers[address](args);
    }

    // Sinon on traite les mises à jour de paramètres
    this.handleParameterUpdate(address.split('/').pop(), args);
  }

  handleParameterUpdate(param, args) {
    // Extraire trackIndex du premier argument
    const [trackIndex, ...otherArgs] = args;

    // Vérifications préliminaires pour le mode verrouillé
    if (this.state.isLocked) {
      if (trackIndex !== this.lockedTrackIndex) return;
    }

    const targetData = this.getTargetTrackData(trackIndex);
    if (!targetData) return;

    // Configuration des mises à jour de paramètres
    const parameterUpdates = {
      volume: {
        update: ([value]) => {
          targetData.volume = value;
          if (this.shouldUpdateUI(trackIndex)) {
            const dbValue = this.volumeConverter.toDb(value);
            this.displayManager.updateVolume(value, dbValue);
          }
        }
      },
      panning: {
        update: ([value]) => {
          targetData.panning = value;
          if (this.shouldUpdateUI(trackIndex)) {
            this.displayManager.updatePanning(value);
          }
        }
      },
      sends: {
        update: ([sendIndex, value]) => {
          targetData.sends[sendIndex] = value;
          if (this.shouldUpdateUI(trackIndex)) {
            const startSendIndex = (this.state.currentPage - 1) * this.sendsPerPage;
            const slotIndex = sendIndex - startSendIndex;
            if (slotIndex >= 0 && slotIndex < this.sendsPerPage) {
              const sendDB = this.volumeSendConverter.toDb(value);
              const slotLetter = String.fromCharCode(65 + slotIndex);
              this.displayManager.updateSend(slotLetter, value, sendDB);
            }
          }
        }
      },
      color: {
        update: ([color]) => {
          targetData.color = color;
          if (this.shouldUpdateUI(trackIndex)) {
            this.displayManager.updateColor(color);
          }
        }
      },
      name: {

        update: ([name]) => {
          if (this.shouldUpdateUI(trackIndex)) {
            this.displayManager.updateName(name);
          }
        }
      },
      mute: {
        update: ([value]) => {
          targetData.mute = value;
          if (this.shouldUpdateUI(trackIndex)) {
            this.updateTrackDisplay(targetData);
          }
        }
      },
      solo: {
        update: ([value]) => {
          targetData.solo = value;
          if (this.shouldUpdateUI(trackIndex)) {
            this.updateTrackDisplay(targetData);
          }
        }
      },
      muted_via_solo: {
        update: ([value]) => {
          targetData.mutedViaSolo = value;
          if (this.shouldUpdateUI(trackIndex)) {
            this.updateTrackDisplay(targetData);
          }
        }
      },
      arm: {
        update: ([value]) => {
          targetData.arm = value;
          if (this.shouldUpdateUI(trackIndex)) {
            this.displayManager.updateArm(value);
          }
        }
      }
    };

    // Exécuter la mise à jour si elle existe
    if (parameterUpdates[param]) {
      parameterUpdates[param].update(otherArgs);
    }
  }

  handleTrackUnlock() {
    this.state.isLocked = false;
    this.lockedTrackData = null;
    this.lockedTrackIndex = null;
    this.displayManager.updateLockState(false);

    // On s'assure d'avoir les données les plus récentes de la piste sélectionnée
    this.oscHandler.sendOscMessage("/live/start_listen_trackMode");
  }

  handleTrackLock() {
    this.state.isLocked = true;
    this.displayManager.updateLockState(true);
  }

  handleInitialTrackData(args, isLockMode) {
    const [
      trackIdx,
      trackIsFoldable,
      trackFoldState,
      trackIsGrouped,
      trackMute,
      trackMutedViaSolo,
      trackSolo,
      trackArm,
      trackColor,
      trackName,
      trackVolume,
    ] = args;

    const totalTracks = this.liveTrackManager.trackCount;
    const displayIndex = this.getDisplayIndex(trackIdx);
    const formattedTrackNumber = `track ${trackIdx + 1}/${totalTracks}`;

    const newData = {
      trackIdx,
      trackIsFoldable,
      trackFoldState,
      trackIsGrouped,
      mute: trackMute,
      mutedViaSolo: trackMutedViaSolo,
      solo: trackSolo,
      arm: trackArm,
      color: trackColor,
      name: trackName,
      volume: trackVolume,
      sends: [],
      panning: 0,
      displayIndex,
      formattedTrackNumber
    };

    if (isLockMode) {
      this.lockedTrackData = newData;
    } else {
      // Préserver les données existantes qui ne sont pas dans le message initial
      if (this.selectedTrackData) {
        newData.sends = this.selectedTrackData.sends;
        newData.panning = this.selectedTrackData.panning;
      }
      this.selectedTrackData = newData;
    }

    if (this.shouldUpdateUI(trackIdx)) {
      this.updateTrackDisplay(newData);
    }
  }

  updateTrackDisplay(trackData) {
    const totalTracks = this.liveTrackManager.trackCount;
    const isMainTrack = trackData.trackIdx === totalTracks - 1;

    const displayInfo = {
      soloState: isMainTrack ? 2 : (trackData.solo ? 1 : 0),
      muteState: isMainTrack ? 4 : (trackData.mute ? 1 : 0) + (trackData.mutedViaSolo ? 2 : 0),
      volumeDb: this.volumeConverter.toDb(trackData.volume),
      currentPage: this.state.currentPage,
      sendsPerPage: this.sendsPerPage
    };

    this.displayManager.updateTrackDisplay(trackData, displayInfo);
  }

  onEncoderChanged(encoderIndex, direction) {
    const encoderConfig = {
      0: {
        paramName: "volume",
        min: 0,
        max: 1,
        minStep: 0.001,
        maxStep: 0.025
      },
      1: {
        paramName: "panning",
        min: -1,
        max: 1,
        minStep: 0.002,
        maxStep: 0.05
      }
    };

    const config = encoderConfig[encoderIndex];
    if (!config) {
      if (encoderIndex >= 4 && encoderIndex <= 7) {
        return this.handleSendEncoder(encoderIndex, direction);
      }
      return;
    }

    const currentValue = this.selectedTrackData[config.paramName];
    const newValue = this.updateEncoderValue(config, currentValue, direction);
    this.updateParameter(config.paramName, newValue);
  }

  sendParameterUpdate(paramName, value, sendIndex = null) {
    const selectedTrack = this.liveTrackManager.selectedTrack;

    if (paramName === "volume") {
      this.oscHandler.sendOscMessage("/live/track/set/volume", [
        selectedTrack,
        value,
      ]);
    } else if (paramName === "panning") {
      this.oscHandler.sendOscMessage("/live/track/set/panning", [
        selectedTrack,
        value,
      ]);
    } else if (paramName === "send") {
      this.oscHandler.sendOscMessage("/live/track/set/sends", [
        selectedTrack,
        sendIndex,
        value,
      ]);
    }
  }
  onButtonPressed(buttonIndex) {
    const buttonActions = {
      0: 'pageDown',
      1: 'pageUp',
      2: 'toggleLock'
    };

    const action = buttonActions[buttonIndex];
    if (action && this.commands[action]) {
      this.commands[action]();
    }
  }
  pageUp() {
    const returnTrackCount = this.liveTrackManager.getReturnTrackCount();
    const maxPage = Math.ceil(returnTrackCount / this.sendsPerPage);

    if (this.state.currentPage < maxPage) {
      this.setPage(this.state.currentPage + 1);
    } else {
      console.log(`Déjà à la dernière page (${this.state.currentPage}/${maxPage})`);
    }
  }

  pageDown() {
    if (this.state.currentPage > 1) {
      this.setPage(this.state.currentPage - 1);
    } else {
      console.log("Déjà  la première page");
    }
  }

  setPage(page) {
    const returnTrackCount = this.liveTrackManager.getReturnTrackCount();
    const maxPage = Math.ceil(returnTrackCount / this.sendsPerPage);

    if (page >= 1 && page <= maxPage) {
      if (this.state.currentPage !== page) {
        this.state.currentPage = page;
        console.log(`Switched to page ${this.state.currentPage}`);

        // Mettre à jour les noms des returns
        const returnTracksNames = this.liveTrackManager.getReturnTracksName();
        if (returnTracksNames && returnTracksNames.length > 0) {
          this.handleReturnTracksNameChange(returnTracksNames);
        }

        // Mettre à jour l'affichage des sends pour la nouvelle page
        if (this.selectedTrackData) {
          this.updateSendsDisplay(this.selectedTrackData.sends);
        }
      }
    }
  }

  updateSendsDisplay(sends) {
    const startSendIndex = (this.state.currentPage - 1) * this.sendsPerPage;
    for (let i = 0; i < this.sendsPerPage; i++) {
      const sendIndex = startSendIndex + i;
      const slotLetter = String.fromCharCode(65 + i);

      if (sendIndex < sends.length) {
        const sendValue = sends[sendIndex];
        const sendDB = this.volumeSendConverter.toDb(sendValue);
        this.communicationManager.sendMessage(`ts${slotLetter},${Math.round(sendValue * 100)},${sendDB}`, this.state.isActive);
      } else {
        // Réinitialiser les slots non utilisés
        this.communicationManager.sendMessage(`ts${slotLetter},0,---`, this.state.isActive);
      }
    }
  }

  getPageInfo() {
    const returnTrackCount = this.liveTrackManager.getReturnTrackCount();
    const maxPage = Math.ceil(returnTrackCount / this.sendsPerPage);

    return {
      currentPage: this.state.currentPage,
      totalPages: maxPage || 1,
      selectedTrack: this.liveTrackManager.selectedTrack,
      returnTrackCount: returnTrackCount,
    };
  }

  onSelectedTrackDeviceChange(...args) {


    if (!this.state.isLocked) {
      // Vérifier si on est sur la piste principale
      const totalTracks = this.liveTrackManager.trackCount;
      const selectedTrack = this.liveTrackManager.selectedTrack;
      const isMainTrack = selectedTrack === totalTracks - 1;

      if (isMainTrack) {
        // Si c'est la piste principale, envoyer 'nul' pour tous les slots
        this.communicationManager.sendMessage('rn,nul,nul,nul,nul', this.state.isActive);
      } else {
        // Si c'est une piste normale, mettre à jour les noms des returns
        const returnTracksNames = this.liveTrackManager.getReturnTracksName();
        if (returnTracksNames && returnTracksNames.length > 0) {
          this.handleReturnTracksNameChange(returnTracksNames);
        }
      }


    }
  }



  toggleLock() {
    if (this.state.isQuickView) {
      console.log("Lock toggle not available in quick view mode");
      return;
    }

    this.state.isLocked = !this.state.isLocked;

    if (this.state.isLocked) {
      this.lockedTrackIndex = this.liveTrackManager.selectedTrack;
      console.log(`Track locked on index ${this.lockedTrackIndex}`);
      this.oscHandler.sendOscMessage("/live/track_lock", "1");
    } else {
      const returnTracksNames = this.liveTrackManager.getReturnTracksName();
      if (returnTracksNames && returnTracksNames.length > 0) {
        this.handleReturnTracksNameChange(returnTracksNames);
      }
      this.lockedTrackIndex = null;
      console.log("Track unlocked");
      this.oscHandler.sendOscMessage("/live/track_lock", "0");
    }
  }

  clearParameters() {
    this.selectedTrackData = {
      volume: 0,
      panning: 0,
      sends: [],
      name: "",
      color: "",
      mute: false,
      solo: false
    };
  }

  validateTrackData(data) {
    const requiredFields = ['volume', 'panning', 'sends', 'name', 'color', 'mute', 'solo'];
    for (const field of requiredFields) {
      if (!(field in data)) {
        data[field] = this.getDefaultValue(field);
      }
    }
  }


  onTouched(type, index) {
    console.log(`onTouched appelé avec type=${type}, index=${index}`);

    if (type === "mut") {
      const selectedTrack = this.liveTrackManager.selectedTrack;
      this.oscHandler.sendOscMessage("/live/track/set/mutetoggle", [selectedTrack]);
      console.log(`Toggle mute for track ${selectedTrack}`);
    } else if (type === "sol") {
      const selectedTrack = this.liveTrackManager.selectedTrack;
      this.oscHandler.sendOscMessage("/live/track/set/solotoggle", [selectedTrack]);
      console.log(`Toggle solo for track ${selectedTrack}`);
    } else if (type === "arm") {
      const selectedTrack = this.liveTrackManager.selectedTrack;
      this.oscHandler.sendOscMessage("/live/track/set/armtoggle", [selectedTrack]);
      console.log(`Toggle arm for track ${selectedTrack}`);
    } else if (type === "sm") {
      if (index === 1) {
        console.log("Mode volume");
        this.emit('modeChange', 'volume');
      } else if (index === 2) {
        console.log("Mode device");
        this.emit('modeChange', 'device');
      } else if (index === 3) {
        console.log("Mode learn");
        this.emit('modeChange', 'learn');
      } else if (index === 4) {
        console.log("Mode browser");
        this.emit('modeChange', 'browser');
      }
    } else if (type === "le") {
      let paramType, trackIndex, sendIndex, isQuantize = false;

      // Déterminer le type de paramètre en fonction de l'index
      if (index === -2) {
        paramType = 2;
        trackIndex = this.state.isLocked ? -1 : this.liveTrackManager.selectedTrack;
        sendIndex = null;
        isQuantize = false;
      } else if (index === -1) {
        paramType = 1;
        trackIndex = this.state.isLocked ? -1 : this.liveTrackManager.selectedTrack;
        sendIndex = null;
        isQuantize = false;
      } else if (index === -7) {
        paramType = 7;
        trackIndex = this.state.isLocked ? -1 : this.liveTrackManager.selectedTrack;
        sendIndex = null;
        isQuantize = true;
      } else if (index === -8) {
        paramType = 8;
        trackIndex = this.state.isLocked ? -1 : this.liveTrackManager.selectedTrack;
        sendIndex = null;
        isQuantize = true;
      } else {
        // Pour un index de send
        paramType = 3;
        trackIndex = this.state.isLocked ? -1 : this.liveTrackManager.selectedTrack;
        // Calculer l'index réel du send en tenant compte de la pagination
        sendIndex = ((this.state.currentPage - 1) * this.sendsPerPage) + index;
      }

      this.globalState.learnData = {
        param_type: paramType,
        track_index: trackIndex,
        send_index: sendIndex,
        device_index: null,
        param_index: null,
        is_quantize: isQuantize
      };

      console.log("Mode learn", this.globalState.learnData);
      this.emit("modeChange", "learn");
    } else if (type === "lkt") {
      this.toggleLock();
    } else if (type === "paninit") {
      console.log("Pan Init");
      const selectedTrack = this.liveTrackManager.selectedTrack;
      this.oscHandler.sendOscMessage("/live/track/set/panning", [selectedTrack, 0]);
    } else if (type === "volinit") {
      console.log("Vol Init");
      const selectedTrack = this.liveTrackManager.selectedTrack;
      this.oscHandler.sendOscMessage("/live/track/set/volume", [selectedTrack, 0.85])
    } else {
      console.log(`Type de toucher non géré: ${type}`);
    }
  }

  getDisplayIndex(trackIdx) {
    const normalTrackCount = this.liveTrackManager.trackCount - this.liveTrackManager.getReturnTrackCount() - 1;
    return trackIdx >= normalTrackCount && trackIdx < this.liveTrackManager.trackCount - 1
      ? String.fromCharCode(65 + (trackIdx - normalTrackCount))
      : (trackIdx + 1).toString();
  }

  handleReturnTracksNameChange(returnTracksNames) {
    if (returnTracksNames.length > 0) {
      this.displayManager.updateReturnNames(returnTracksNames, this.state.currentPage, this.sendsPerPage);
    } else {
      this.communicationManager.sendMessage("rn,nul,nul,nul,nul", this.state.isActive);
    }
  }

  trackUp() {
    const selectedTrack = this.liveTrackManager.selectedTrack;
    const totalTracks = this.liveTrackManager.trackCount;

    if (selectedTrack < totalTracks - 1) {
      const newTrackIndex = selectedTrack + 1;
      this.liveTrackManager.setSelectedTrack(newTrackIndex);
      console.log(`Sélection de la piste ${newTrackIndex + 1}`);
    } else {
      console.log("Déjà à la dernière piste");
    }
  }

  trackDown() {
    const selectedTrack = this.liveTrackManager.selectedTrack;

    if (selectedTrack > 0) {
      const newTrackIndex = selectedTrack - 1;
      this.liveTrackManager.setSelectedTrack(newTrackIndex);
      console.log(`Sélection de la piste ${newTrackIndex + 1}`);
    } else {
      console.log("Déjà à la première piste");
    }
  }

  // Mettre à jour la méthode sendTrackDisplayMessage
  sendTrackDisplayMessage(trackData, displayData) {
    this.displayManager.updateTrackDisplay(trackData, displayData);
  }

  getTargetTrackData(trackIndex) {
    return this.state.isLocked ?
      (trackIndex === this.lockedTrackIndex ? this.lockedTrackData : null) :
      this.selectedTrackData;
  }

  shouldUpdateUI(trackIndex) {
    return this.state.isQuickView ||
      (!this.state.isLocked && trackIndex === this.liveTrackManager.selectedTrack) ||
      (this.state.isLocked && trackIndex === this.lockedTrackIndex);
  }

  shortString(stringstoshorten, maxLength) {
    if (stringstoshorten.length > maxLength) {
      stringstoshorten = stringstoshorten.slice(0, maxLength) + '...';
    }
    return stringstoshorten;
  }
}

module.exports = LiveTrackMode;
