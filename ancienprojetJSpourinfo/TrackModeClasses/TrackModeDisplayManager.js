const LiveVolumeConverter = require("../liveUtilityClasses/liveVolumeConverter");
const LiveVolumeSendConverter = require("../liveUtilityClasses/liveVolumeSendConverter");

const TRACK_MODE_CONFIG = {
      SLOTS_PER_PAGE: 8,
      SENDS_PER_PAGE: 4,
      DEFAULT_TRACK_DATA: {
            volume: 0,
            panning: 0,
            sends: [],
            name: "",
            color: "",
            mute: false,
            mutedViaSolo: false,
            solo: false,
            arm: false,
            trackIsFoldable: false,
            trackFoldState: false,
            trackIsGrouped: false,
            displayIndex: "",
            formattedTrackNumber: ""
      }
};

class TrackModeDisplayManager {
      constructor({ communicationManager, state }) {
            this.communicationManager = communicationManager;
            this.state = state;
            this.volumeConverter = new LiveVolumeConverter();
            this.volumeSendConverter = new LiveVolumeSendConverter();
      }

      updateSlot(trackData, displayInfo) {
            this.updateTrackDisplay(trackData, displayInfo);

            const mappedPanning = Math.round(trackData.panning * 50);
            this.communicationManager.sendMessage(`pan${mappedPanning}`, this.state.isActive);

            if (trackData.sends) {
                  this.updateSendsDisplay(trackData.sends, displayInfo.currentPage, displayInfo.sendsPerPage);
            }
      }

      clearSlot() {
            this.communicationManager.sendMessage('st0', this.state.isActive);
            for (let i = 0; i < 4; i++) {
                  const slotLetter = String.fromCharCode(65 + i);
                  this.communicationManager.sendMessage(`ts${slotLetter},0,---`, this.state.isActive);
            }
      }

      updateTrackDisplay(trackData, displayInfo) {
            const sltMessage = this.formatTrackDisplayMessage(trackData, displayInfo);
            this.communicationManager.sendMessage(sltMessage, this.state.isActive);
      }

      formatTrackDisplayMessage(trackData, displayInfo) {
            const formattedName = `${this.shortString(trackData.name, 20)},${trackData.formattedTrackNumber}`;
            return `slt${trackData.trackIsFoldable ? '1' : '0'}${trackData.trackFoldState ? '1' : '0'}${trackData.trackIsGrouped ? '1' : '0'}${displayInfo.muteState}${displayInfo.soloState}${trackData.arm},${trackData.displayIndex},${trackData.color},${formattedName},${Math.round(trackData.volume * 100)},${displayInfo.volumeDb}`;
      }

      updateSendsDisplay(sends, currentPage, sendsPerPage) {
            const startSendIndex = (currentPage - 1) * sendsPerPage;
            for (let i = 0; i < sendsPerPage; i++) {
                  const sendIndex = startSendIndex + i;
                  const slotLetter = String.fromCharCode(65 + i);

                  if (sendIndex < sends.length) {
                        const sendValue = sends[sendIndex];
                        const sendDB = this.volumeSendConverter.toDb(sendValue);
                        this.communicationManager.sendMessage(`ts${slotLetter},${Math.round(sendValue * 100)},${sendDB}`, this.state.isActive);
                  } else {
                        this.communicationManager.sendMessage(`ts${slotLetter},0,---`, this.state.isActive);
                  }
            }
      }

      updateReturnNames(returnNames, currentPage, sendsPerPage) {
            const startIndex = (currentPage - 1) * sendsPerPage;
            const pageNames = new Array(sendsPerPage).fill('nul');

            returnNames
                  .slice(startIndex, startIndex + sendsPerPage)
                  .forEach((name, index) => {
                        if (name) pageNames[index] = this.shortString(name, 12);
                  });

            this.communicationManager.sendMessage(`rn,${pageNames.join(',')}`, this.state.isActive);
      }

      updateVolume(value, dbValue) {
            this.communicationManager.sendMessage(`vt${Math.round(value * 100)},${dbValue}`, this.state.isActive);
      }

      updatePanning(value) {
            const mappedPanning = Math.round(value * 50);
            this.communicationManager.sendMessage(`pan${mappedPanning}`, this.state.isActive);
      }

      updateLockState(isLocked) {
            this.communicationManager.sendMessage(`lt0${isLocked ? '1' : '0'}`, this.state.isActive);
      }

      updateNormalViewState(isQuickView) {
            this.communicationManager.sendMessage(`tq${isQuickView ? '1' : '0'}`, this.state.isActive);
      }

      updateMode(mode) {
            this.communicationManager.sendMessage(`mo,${mode}`, this.state.isActive);
      }

      clearSend(slotLetter) {
            this.communicationManager.sendMessage(`ts${slotLetter},0,---`, this.state.isActive);
      }

      updateSend(slotLetter, value, dbValue) {
            this.communicationManager.sendMessage(`ts${slotLetter},${Math.round(value * 100)},${dbValue}`, this.state.isActive);
      }

      updateColor(color) {
            this.communicationManager.sendMessage(`ct${color}`, this.state.isActive);
      }

      updateName(name) {
            this.communicationManager.sendMessage(`nt,${name}`, this.state.isActive);
      }

      updateArm(arm) {
            this.communicationManager.sendMessage(`ta${arm ? '1' : '0'}`, this.state.isActive);
      }

      shortString(stringstoshorten, maxLength) {
            if (stringstoshorten.length > maxLength) {
                  stringstoshorten = stringstoshorten.slice(0, maxLength) + '...';
            }
            return stringstoshorten;
      }

}

module.exports = TrackModeDisplayManager;