# Plan de conversion de liveTrackMode.js vers Go

## 1. Analyse et préparation
- Lire `ConversionGuide.md` pour comprendre les règles de traduction.
- Étudier l’implémentation Go de `liveVolumeMode` (`liveVolumeMode.go`) comme référence.

## 2. Structure du nouveau package Go
- Créer le package `liveTrackMode` dans `Live/`.
- Fi<PERSON>ers à créer :
  - `liveTrackMode.go` (struct principale).
  - `config.go` (constantes et config).
  - `liveVolumeConverter.go` et `liveVolumeSendConverter.go`.
  - `trackModeDisplayManager.go`.
  - `liveTrackMode_test.go` pour les tests unitaires.

## 3. Conversion de la configuration
- Traduire `TRACK_MODE_CONFIG` en :
  ```go
  type Config struct {
    SlotsPerPage int
    SendsPerPage int
    DefaultTrackData TrackData
    BackgroundListenAddresses []string
  }
  var DefaultConfig = Config{ ... }
  ```
- Initialiser `SLOTS_PER_PAGE`, `SENDS_PER_PAGE`, `DEFAULT_TRACK_DATA` et `BACKGROUND_LISTEN_ADDRESSES`.

## 4. Struct principale et constructeur
- Remplacer `class LiveTrackMode extends EventEmitter` par :
  ```go
  type LiveTrackMode struct {
    oscHandler OSCHandler
    trackManager TrackManager
    commManager CommunicationManager
    state *SharedState
    config Config
    displayManager *TrackModeDisplayManager
    // ...
  }
  func NewLiveTrackMode(osch OSCHandler, tm TrackManager, cm CommunicationManager, ss *SharedState) *LiveTrackMode {
    // initialisation
  }
  ```

## 5. Gestion des messages OSC
- Pour chaque adresse de `BACKGROUND_LISTEN_ADDRESSES`, binder un callback Go :
  ```go
  osch.AddListener(addr, func(msg OSCMessage) {
    // conversion + update state + display
  })
  ```
- Implémenter des méthodes `handleVolume`, `handleName`, `handleSends`, etc., calquées sur JS.

## 6. Converters
- Porter la logique de `LiveVolumeConverter` et `LiveVolumeSendConverter` en fonctions Go :
  ```go
  func ConvertVolume(raw float64) float64 { ... }
  func ConvertSends(raw []float64) []SendData { ... }
  ```

## 7. DisplayManager
- Étudier `TrackModeDisplayManager.js` et créer un struct Go avec méthodes équivalentes :
  ```go
  type TrackModeDisplayManager struct { ... }
  func NewDisplayManager(enc EncoderController) *TrackModeDisplayManager { ... }
  func (dm *TrackModeDisplayManager) Update(data []TrackData) { ... }
  ```

## 8. Pagination et navigation
- Implémenter `NextPage()`, `PrevPage()` en Go pour gérer l’affichage par pages.
- Mettre à jour l’état de page et appeler `displayManager` à chaque changement.

## 9. Tests unitaires
- Écrire des tests table-driven pour :
  - Converters (volume, sends).
  - Pagination.
  - Handlers OSC (avec un mock OSCHandler).
- Lancer `go test ./Live/liveTrackMode`.

## 10. Documentation et intégration
- Ajouter des commentaires GoDoc pour chaque package et méthode.
- Mettre à jour le README du projet pour inclure le mode Track.
- Intégrer la nouvelle cible au pipeline CI (gofmt, go vet, tests).

---

## Prochaines étapes
1. Créer la structure du package et stubs des fichiers.
2. Implémenter `config.go` et valider la config.
3. Écrire le constructeur et stub des handlers OSC.
4. Porter les converters et tester séparément.
5. Intégrer DisplayManager et navigation.
6. Finaliser les handlers OSC et tester sur le contrôleur.
