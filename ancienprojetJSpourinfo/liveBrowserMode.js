const EventEmitter = require('events');

class LiveBrowserMode extends EventEmitter {
      constructor(osc<PERSON><PERSON><PERSON>, liveTrackManager, communicationManager, sharedState) {
            super();
            this.oscHandler = oscHandler;
            this.liveTrackManager = liveTrackManager;
            this.communicationManager = communicationManager;
            this.globalState = sharedState.global;
            this.state = sharedState.browser;

            // Bind des méthodes
            this._boundHandleOscMessage = this.handleOscMessage.bind(this);
            this._boundTouchPressed = (type, index) => this.onTouched(type, index);
            this._boundButtonPressed = this.onButtonPressed.bind(this);


            // État du browser
            this.currentState = {
                  path: [],
                  items: [],
                  isPreviewEnabled: this.state.isPreviewEnabled || false,
                  isHotswapEnabled: false,
                  currentPage: 1,
                  totalPages: 1,
                  totalItems: 0
            };

            // Liste des adresses OSC pour le mode browser
            this.backgroundListenAddresses = [
                  "/live/browser/get/items",
                  "/live/browser/hotswap/enable",
                  "/live/browser/hotswap/disable",
                  "/live/browser/load",
                  "/live/browser/preview"
            ];

            // Ajout du tracking du dernier item prévisualisé
            this.lastPreviewedItem = {
                  index: null,
                  page: null
            };
      }

      async backgroundStart() {
            console.log("\n=== Starting BrowserMode background listeners ===");
            this.isActiveMode = false;

            // Ajouter d'abord tous les listeners
            this.backgroundListenAddresses.forEach((address) => {
                  this.oscHandler.on(address, (args) => this._boundHandleOscMessage(args, address));
            });

            // Envoyer le message une seule fois après avoir configuré tous les listeners
            this.communicationManager.sendMessage("bw,0", this.state.isActive);

            this.goHome();
      }

      async start() {
            console.log("\n=== Starting BrowserMode ===");
            this.isActiveMode = true;
            this.state.isActive = true;

            console.log("Attaching touch listener...");
            this.communicationManager.on('touchPressed', this._boundTouchPressed);
            console.log("Attaching button listener...");
            this.communicationManager.on('buttonPressed', this._boundButtonPressed);

            // Activer l'affichage du mode browser
            this.communicationManager.sendMessage("mo,5", this.state.isActive);



      }

      async cleanup() {
            console.log("\n=== BrowserMode Cleanup ===");
            this.isActiveMode = false;
            this.state.isActive = false;

            // Sauvegarder l'état du preview avant de nettoyer
            this.state.isPreviewEnabled = this.currentState.isPreviewEnabled;

            // Retirer tous les listeners de communication
            this.communicationManager.removeListener('touchPressed', this._boundTouchPressed);
            this.communicationManager.removeListener('buttonPressed', this._boundButtonPressed);

            // Réinitialiser l'état
            this.currentState = {
                  path: [],
                  items: [],
                  isPreviewEnabled: this.state.isPreviewEnabled, // Conserver la valeur sauvegardée
                  isHotswapEnabled: false,
                  currentPage: 1,
                  totalPages: 1,
                  totalItems: 0
            };

            this.goHome();
      }

      async cleanupForSwitch() {
            await this.cleanup();
      }

      async cleanupForExit() {
            await this.cleanup();
            await this.stopBackground();
      }

      async stopBackground() {
            console.log("\n=== Stopping BrowserMode background listeners ===");

            this.backgroundListenAddresses.forEach((address) => {
                  this.oscHandler.removeListener(address, this._boundHandleOscMessage);
            });
      }

      // Gestion des messages OSC
      handleOscMessage(args, address) {
            console.log("handleOscMessage reçu:", { address, args });

            if (address === "/live/browser/get/items") {
                  console.log("-> Transmission à handleGetItems avec totalPages =", args[1]);
                  this.handleGetItems(args);
                  console.log("-> Après handleGetItems, currentState =", {
                        currentPage: this.currentState.currentPage,
                        totalPages: this.currentState.totalPages,
                        path: this.currentState.path
                  });
            } else if (address === "/live/browser/hotswap/enable") {
                  this.currentState.isHotswapEnabled = true;
                  console.log("Hotswap enabled");
            } else if (address === "/live/browser/hotswap/disable") {
                  this.currentState.isHotswapEnabled = false;
                  console.log("Hotswap disabled");
            } else if (address === "/live/browser/load") {
                  console.log("Load message received", args);
                  this.emit('modeChange', 'device');
            } else if (address === "/live/browser/preview") {
                  console.log("Preview message received", args);
            }
      }

      handleGetItems(args) {
            console.log('handleGetItems received', args);

            // Mise à jour directe avec les valeurs du message
            this.currentState.currentPage = args[0];     // Premier argument = page courante
            this.currentState.totalPages = args[1];      // Deuxième argument = nombre total de pages
            this.currentState.items = args[2].map((index, i) => ({
                  index,
                  name: args[3][i],        // Liste des noms
                  type: args[4][i],        // Liste des types
                  loadable: args[5][i] === 1,    // Liste des loadables
                  isFolder: args[6][i] === 1,   // Liste des Folders
            }));
            this.currentState.totalItems = args[7];      // Nombre total d'items
            this.currentState.displayPath = args[8];     // Chemin d'affichage

            // Envoi du chemin et de la pagination
            this.communicationManager.sendMessage(`bp,${this.currentState.displayPath.join(" > ")},${this.currentState.currentPage}/${this.currentState.totalPages},${this.currentState.totalItems} items`, this.state.isActive);

            console.log("Items complets:", this.currentState.items.map(item => item.name));
            console.log("Nombre d'items avant slice:", this.currentState.items.length);

            // Diviser les items en deux messages
            const firstBatch = this.currentState.items
                  .slice(0, 8)
                  .map(item => `${item.name},${item.isFolder ? 1 : 0}`);

            const secondBatch = this.currentState.items
                  .slice(8, 16)
                  .map(item => `${item.name},${item.isFolder ? 1 : 0}`);

            // Envoyer en deux messages
            this.communicationManager.sendMessage(`b1,${firstBatch.join(',')}`, this.state.isActive);
            this.communicationManager.sendMessage(`b2,${secondBatch.join(',')}`, this.state.isActive);
      }

      displayCurrentPage() {
            console.log(`\nPath: ${this.currentState.displayPath.join(" > ")}`);
            console.log(
                  `Page: ${this.currentState.currentPage} / ${this.currentState.totalPages}, Total Items: ${this.currentState.totalItems}`
            );
            this.currentState.items.forEach((item, i) => {
                  if (i < 16) {
                        console.log(
                              `${i}: ${item.name} (${item.type}) - Loadable: ${item.loadable}, Folder: ${item.isFolder}`
                        );
                  }
            });
      }

      pageUp() {
            console.log('pageUp called', {
                  currentPage: this.currentState.currentPage,
                  totalPages: this.currentState.totalPages,
                  path: this.currentState.path
            });

            if (this.currentState.currentPage < this.currentState.totalPages) {
                  const nextPage = Number(this.currentState.currentPage) + 1;
                  console.log('Sending pageUp message', {
                        nextPage,
                        path: this.currentState.path
                  });
                  this.oscHandler.sendOscMessage("/live/browser/get/items", [nextPage, ...this.currentState.path]);
            }
      }

      pageDown() {
            console.log('pageDown called', {
                  currentPage: this.currentState.currentPage,
                  totalPages: this.currentState.totalPages,
                  path: this.currentState.path
            });

            if (this.currentState.currentPage > 1) {
                  const prevPage = Number(this.currentState.currentPage) - 1;
                  console.log('Sending pageDown message', {
                        prevPage,
                        path: this.currentState.path
                  });
                  this.oscHandler.sendOscMessage("/live/browser/get/items", [prevPage, ...this.currentState.path]);
            }
      }

      setPage(page) {
            if (page >= 1 && page <= this.currentState.totalPages) {
                  this.sendGetItemsMessage(page, ...this.currentState.path);
            }
      }

      goHome() {
            console.log("goHome called");
            const defaultFolders = [
                  'instruments',
                  'audio_effects',
                  'plugins',
                  'midi_effects',
                  'sounds',
                  'samples',
                  'drums',
                  'clips',
                  'user_library'
            ];

            // Simuler un message OSC pour l'écran d'accueil
            this.handleGetItems([
                  1,                          // currentPage
                  1,                          // totalPages
                  Array.from({ length: 9 }, (_, i) => i), // indices
                  defaultFolders,            // noms
                  Array(9).fill('Folder'),   // types
                  Array(9).fill(0),          // loadable
                  Array(9).fill(1),          // isFolder
                  9,                         // totalItems
                  ['home']                   // displayPath
            ]);

            // Réinitialiser le path
            this.currentState.path = [];
      }

      goback() {
            if (this.currentState.path.length === 0) {
                  // Si nous sommes déjà à la racine, ne rien faire
                  return;
            } else if (this.currentState.path.length === 1) {
                  // Si nous sommes au premier niveau, retourner à l'accueil
                  this.goHome();
            } else {
                  // Sinon, remonter d'un niveau dans l'arborescence
                  this.sendGetItemsMessage(1, ...this.currentState.path.slice(0, -1));
            }
      }

      sendGetItemsMessage(...args) {
            // Le premier argument est toujours la page
            const page = args[0];
            // Le reste des arguments constitue le nouveau path
            const newPath = args.slice(1);

            console.log('Sending get items message:', {
                  page,
                  newPath,
                  args
            });

            // Mise à jour du path réel
            this.currentState.path = newPath;

            // Envoi du message OSC
            this.oscHandler.sendOscMessage("/live/browser/get/items", args);
      }

      load(index) {
            const item = this.currentState.items[index];
            if (item && item.loadable) {
                  this.oscHandler.sendOscMessage("/live/browser/load", [
                        this.currentState.currentPage,
                        ...this.currentState.path,
                        index
                  ]);
            }
      }

      preview(index) {
            const item = this.currentState.items[index];
            if (item && item.loadable) {
                  this.oscHandler.sendOscMessage("/live/browser/preview", [
                        this.currentState.currentPage,
                        ...this.currentState.path,
                        index
                  ]);
            }
      }

      onTouched(type, index) {
            console.log("onTouched appelé avec:", { type, index });
            if (type === "bb") {
                  this.goback();
            } else if (type === "bs") {
                  console.log("onTouched", type, index);
                  const item = this.currentState.items[index];
                  if (item) {
                        if (item.loadable) {
                              if (this.currentState.isPreviewEnabled) {
                                    // Si c'est le même item que celui précédemment prévisualisé
                                    if (index === this.lastPreviewedItem.index &&
                                          this.currentState.currentPage === this.lastPreviewedItem.page) {
                                          // On charge l'item
                                          this.load(index);
                                          // On réinitialise le dernier item prévisualisé
                                          this.lastPreviewedItem = { index: null, page: null };
                                    } else {
                                          // Sinon on preview le nouvel item
                                          this.preview(index);
                                          // On mémorise cet item
                                          this.lastPreviewedItem = {
                                                index: index,
                                                page: this.currentState.currentPage
                                          };
                                    }
                              } else {
                                    this.load(index);
                              }
                        } else if (item.isFolder) {
                              this.sendGetItemsMessage(1, ...this.currentState.path, index);
                        }
                  }
            } else if (type === "bl") {
                  const item = this.currentState.items[index];
                  if (item && item.isFolder) {
                        this.sendGetItemsMessage(1, ...this.currentState.path, index);
                  }
            } else if (type === "sm") {
                  // Gestion du changement de mode
                  if (index === 2) {
                        console.log("Mode device");
                        this.oscHandler.sendOscMessage("/live/browser/hotswap/disable");
                        this.emit('modeChange', 'device');
                  } else if (index === 0) {
                        console.log("Mode track");
                        this.oscHandler.sendOscMessage("/live/browser/hotswap/disable");
                        this.emit('modeChange', 'track');
                  } else if (index === 1) {
                        console.log("Mode volume");
                        this.oscHandler.sendOscMessage("/live/browser/hotswap/disable");
                        this.emit('modeChange', 'volume');
                  } else if (index === 3) {
                        console.log("Mode learn");
                        this.oscHandler.sendOscMessage("/live/browser/hotswap/disable");
                        this.emit('modeChange', 'learn');
                  }
            } else if (type === "bp") {
                  this.currentState.isPreviewEnabled = index === 1;
                  // Sauvegarder immédiatement l'état dans le state partagé
                  this.state.isPreviewEnabled = this.currentState.isPreviewEnabled;
                  // Réinitialiser le dernier item prévisualisé lors du changement de mode
                  this.lastPreviewedItem = { index: null, page: null };
                  console.log("isPreviewEnabled set to", this.currentState.isPreviewEnabled);
            }
      }

      onButtonPressed(buttonIndex) {
            console.log(`onButtonPressed appelé avec buttonIndex=${buttonIndex}`);

            if (buttonIndex === 0) {
                  this.pageDown();
            } else if (buttonIndex === 1) {
                  this.pageUp();
            }
      }

      async quit() {
            console.log("Sortie du mode browser");
            const previousMode = this.globalState.modeBeforeBrowser || 'volume';  // 'volume' comme fallback
            this.emit('modeChange', previousMode);
      }

      test() {
            this.oscHandler.sendOscMessage("/live/browser/load", [1, 1, 1, 0, -1, 0, 0, 0]);
      }

}

module.exports = LiveBrowserMode;
