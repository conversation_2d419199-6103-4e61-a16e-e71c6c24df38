package main

import (
	// Import pour embed
	_ "embed"
	"log"
	"os"
	"os/signal"
	communication "oscbridge/Communication"
	live "oscbridge/Live"
	livedevicemode "oscbridge/Live/LiveDeviceMode"
	livelearnmode "oscbridge/Live/LiveLearnMode"
	livetrackmode "oscbridge/Live/LiveTrackMode"
	liveVolumeMode "oscbridge/Live/liveVolumeMode"
	debugPkg "oscbridge/debug"
	"sync"
	"syscall"
	"time"

	"github.com/getlantern/systray" // Import de systray
)

//go:embed icon.png
var iconData []byte // Variable pour stocker les données de l'icône embarquée

// Configuration des constantes
const (
	localPort    = 11001 // Port sur lequel on écoute
	remotePort   = 11000 // Port vers lequel on envoie
	debugEnabled = true  // Activer le mode debug pour les logs OSC détaillés
)

// Variables globales pour gérer les composants principaux
var (
	oscBridge    *communication.RawOscBridge
	manager      *live.LiveModeManager
	comManager   *communication.CommunicationManager
	debugManager *debugPkg.DebugManager
	appWaitGroup sync.WaitGroup        // WaitGroup pour attendre la fin des goroutines principales
	quitChan     = make(chan struct{}) // Canal pour signaler l'arrêt
)

func main() {
	// Démarrer la boucle systray. Bloquant.
	// onReady est appelé quand systray est prêt, onExit quand il se termine.
	systray.Run(onReady, onExit)
	// Le programme se termine quand systray.Quit() est appelé et que onExit a fini.
	log.Println("Systray.Run terminé, sortie du programme principal.")
}

// onReady est exécuté lorsque systray est initialisé et prêt.
func onReady() {
	log.Println("Systray prêt. Configuration de l'icône et du menu...")

	// Définir l'icône
	if len(iconData) > 0 {
		systray.SetTemplateIcon(iconData, iconData)
	} else {
		log.Println("Attention: Fichier icon.png non trouvé ou vide. Utilisation d'un titre texte.")
		systray.SetTitle("OSC")
	}
	systray.SetTooltip("OSC Bridge Go")

	// Ajouter l'option "Quitter"
	mQuit := systray.AddMenuItem("Quitter", "Fermer l'application OSC Bridge")

	// Lancer la logique principale de l'application dans une goroutine séparée.
	// Lui passer le canal de clic du menu Quitter.
	appWaitGroup.Add(1)                    // Seulement pour runApplicationCore maintenant
	go runApplicationCore(mQuit.ClickedCh) // Passer le canal

	// La goroutine séparée pour le menu n'est plus nécessaire.

	log.Println("Icône et menu Systray configurés. Logique applicative démarrée.")
}

// onExit est exécuté lorsque systray.Quit() est appelé.
func onExit() {
	log.Println("Début de la sortie de systray (onExit).")
	// Attendre la fin de runApplicationCore
	appWaitGroup.Wait()
	log.Println("Fin de la sortie de systray (onExit). Application arrêtée.")
}

// runApplicationCore contient la logique principale de l'application
// Attend maintenant le canal du menu quitter en argument.
func runApplicationCore(quitMenuChan <-chan struct{}) { // Ajout du paramètre
	defer appWaitGroup.Done() // S'assurer que le WaitGroup est décrémenté à la fin

	var err error // Déclarer err ici pour la portée

	// Initialiser le gestionnaire de débogage
	log.Println("Initialisation du gestionnaire de débogage au clavier...")
	debugManager = debugPkg.NewDebugManager()
	if err = debugManager.Start(); err != nil {
		log.Printf("Erreur démarrage gestionnaire de débogage: %v", err)
	} else {
		log.Println("Gestionnaire de débogage démarré.")
	}

	// Créer RawOscBridge
	log.Println("Création du RawOscBridge...")
	oscBridge, err = communication.NewRawOscBridge(localPort, remotePort, debugEnabled)
	if err != nil {
		log.Printf("Impossible de créer le bridge OSC: %v. Arrêt.", err)
		triggerShutdown() // Assure la fermeture de quitChan pour sortir proprement
		return            // Done sera appelé par defer
	}
	log.Printf("RawOscBridge créé (Local: %d, Remote: %d)", localPort, remotePort)

	// Créer LiveModeManager
	log.Println("Création du LiveModeManager...")
	manager, err = live.NewLiveModeManager(oscBridge)
	if err != nil {
		log.Printf("Erreur création LiveModeManager: %v. Arrêt.", err)
		triggerShutdown()
		return
	}
	log.Println("LiveModeManager créé.")

	// Initialiser CommunicationManager
	log.Println("Création du CommunicationManager...")
	comManager = communication.NewCommunicationManager()
	if err := comManager.Initialize(); err != nil {
		log.Printf("Erreur initialisation CommunicationManager: %v", err)
		comManager = nil
	} else {
		log.Println("CommunicationManager initialisé.")
		comManager.SetMessageHandler(func(message string) {
			log.Printf("Message série reçu: %s", message)
		})
	}

	// Initialiser et démarrer les modes
	if manager != nil {
		initializeModes(manager, comManager)
		log.Println("Modes initialisés.")

		log.Println("Démarrage des modes...")
		if err := manager.InitializeAndStartMode(); err != nil {
			log.Printf("Erreur démarrage des modes: %v. Arrêt.", err)
			triggerShutdown()
			return
		}
		log.Println("Modes démarrés avec succès.")
		setHardwareReceiver()
		
		// Connecter le HardwareManager au LiveModeManager pour les boutons transversaux
		if comManager != nil && comManager.GetHardwareManager() != nil {
			comManager.GetHardwareManager().SetModeManager(manager)
			log.Println("HardwareManager connecté au LiveModeManager pour les boutons transversaux.")
			
			// Configurer les écouteurs d'événements globaux (B10, B11, B12)
			manager.SetupGlobalHardwareListeners(comManager.GetHardwareManager())
		}
	}

	// Configurer les gestionnaires de touches
	if debugManager != nil && manager != nil {
		debugPkg.SetupAllHandlers(debugManager, manager)
		debugPkg.PrintKeyboardHelp()
	}

	log.Printf("Initialisation terminée. Mode actif: %s. En attente...", manager.GetCurrentActiveModeName())

	// Attendre le signal d'arrêt (menu, OS, ou interne)
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	select {
	case <-quitMenuChan: // Écoute directe du clic menu
		log.Println("Signal d'arrêt reçu via le menu systray (quitMenuChan).")
		// Fermer quitChan pour signaler l'arrêt si nécessaire ailleurs,
		// bien que ce ne soit plus utilisé pour synchroniser la goroutine de menu.
		triggerShutdown()
	case sig := <-sigChan:
		log.Printf("Signal OS reçu: %v. Déclenchement de l'arrêt.", sig)
		triggerShutdown()
	case <-quitChan: // Écoute de quitChan pour les arrêts internes (ex: erreur init)
		log.Println("Signal d'arrêt reçu via quitChan (probablement erreur interne).")
	}

	// Exécuter la séquence d'arrêt
	shutdownApplication()

	// Après l'arrêt logique, demander à systray de quitter DANS UNE GOROUTINE SÉPARÉE
	// pour ne pas bloquer cette goroutine (runApplicationCore) et permettre
	// à son defer appWaitGroup.Done() de s'exécuter.
	log.Println("Demande d'arrêt de systray via goroutine...")
	go systray.Quit()

	// La goroutine runApplicationCore va maintenant pouvoir atteindre sa fin,
	// ce qui exécutera 'defer appWaitGroup.Done()'
}

// setHardwareReceiver configure le récepteur d'événements matériels
func setHardwareReceiver() {
	if comManager != nil && manager != nil && comManager.GetHardwareManager() != nil {
		activeModeName := manager.GetCurrentActiveModeName()
		if activeMode, exists := manager.GetMode(activeModeName); exists {
			if hwReceiver, ok := activeMode.(communication.HardwareEventReceiver); ok {
				comManager.SetActiveMode(hwReceiver)
				log.Printf("Mode %s défini comme récepteur des événements matériels", activeModeName)
			} else {
				log.Printf("Le mode %s n'implémente pas l'interface HardwareEventReceiver", activeModeName)
			}
		}
	}
}

// triggerShutdown ferme le canal quitChan de manière sûre (idempotente)
func triggerShutdown() {
	// Utiliser un Mutex ou sync.Once serait plus robuste si appelé depuis plusieurs goroutines
	// mais ici, c'est appelé soit par le signal OS soit par le menu, puis shutdownApplication
	// qui appelle systray.Quit(), ce qui devrait arrêter la goroutine du menu.
	// Un simple check devrait suffire pour éviter la panique.
	select {
	case <-quitChan:
		// Déjà fermé ou en cours de fermeture
	default:
		close(quitChan)
	}
}

// shutdownApplication gère l'arrêt propre des composants de l'application.
func shutdownApplication() {
	log.Println("Début de la séquence d'arrêt de l'application...")

	// 1. Envoyer le message de fermeture au matériel d'abord
	if comManager != nil {
		log.Println("Envoi du message de fermeture au matériel...")
		if err := comManager.SendMessage("ex", true); err != nil {
			log.Printf("Erreur lors de l'envoi du message de fermeture série: %v", err)
		}
		// Petite pause pour laisser le temps à la communication série de s'effectuer
		time.Sleep(50 * time.Millisecond)
	}

	// 2. Fermer le gestionnaire de communication série AVANT de nettoyer les modes
	if comManager != nil {
		log.Println("Fermeture du CommunicationManager...")
		if err := comManager.Close(); err != nil {
			log.Printf("Erreur lors de la fermeture du CommunicationManager: %v", err)
		} else {
			log.Println("CommunicationManager fermé.")
		}
	}

	// 3. Fermer le bridge OSC AVANT de nettoyer les modes
	if oscBridge != nil {
		log.Println("Fermeture du RawOscBridge...")
		if err := oscBridge.Close(); err != nil {
			log.Printf("Erreur lors de la fermeture du bridge OSC: %v", err)
		} else {
			log.Println("RawOscBridge fermé.")
		}
	}

	// 4. Arrêter et nettoyer tous les modes APRÈS la fermeture des communications
	// Les modes ne devraient plus pouvoir utiliser comManager ou oscBridge.
	if manager != nil {
		log.Println("Nettoyage du LiveModeManager...")
		manager.Cleanup()
		log.Println("LiveModeManager nettoyé.")
	}

	// 5. Arrêter le gestionnaire de débogage
	if debugManager != nil {
		log.Println("Arrêt du gestionnaire de débogage...")
		debugManager.Stop()
		log.Println("Gestionnaire de débogage arrêté.")
	}

	log.Println("Séquence d'arrêt de l'application terminée.")
}

func initializeModes(manager *live.LiveModeManager, comManager *communication.CommunicationManager) {
	trackManager := manager.GetTrackManager()

	// Mode Volume
	liveVolumeMode := liveVolumeMode.NewLiveVolumeMode(trackManager, comManager)
	manager.RegisterMode("volume", liveVolumeMode)
	log.Println("Mode Volume enregistré")

	// Mode Piste
	trackMode := livetrackmode.NewLiveTrackMode(trackManager, comManager)
	manager.RegisterMode("track", trackMode)
	log.Println("Mode Piste enregistré")

	// Mode Device
	liveDeviceMode := livedevicemode.NewLiveDeviceMode(trackManager, comManager)
	manager.RegisterMode("device", liveDeviceMode)
	log.Println("Mode Device enregistré")

	// Mode Browser
	browserMode := live.NewLiveBrowserMode(trackManager, comManager)
	manager.RegisterMode("browser", browserMode)
	log.Println("Mode Browser enregistré")

	// Mode Learn
	liveLearnMode := livelearnmode.NewLiveLearnMode(trackManager, comManager)
	manager.RegisterMode("learn", liveLearnMode)
	log.Println("Mode Learn enregistré")
}
