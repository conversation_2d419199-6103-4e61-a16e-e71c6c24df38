package livedevicemode

import (
	"fmt"
	"log"
	communication "oscbridge/Communication"
	live "oscbridge/Live"
	"strconv"
	"strings"
	"sync"
	"time"
)

// DeviceProperty représente les propriétés d'un device
type DeviceProperty struct {
	Name            string
	IsActive        bool
	CanHaveChains   bool
	CanHaveDrumpads bool
}

// ChainProperty représente les propriétés d'une chaîne
type ChainProperty struct {
	Name string
	Mute bool
	Solo bool
}

// DevicePropertyManager gère les propriétés et l'affichage des devices
type DevicePropertyManager struct {
	commManager      *communication.CommunicationManager
	trackManager     *live.LiveTrackManager
	baseMode         *communication.BaseMode
	state            *LiveDeviceModeState
	deviceProperties map[string]map[string]interface{}
	chainProperties  map[string]map[string]interface{}
	mutex            sync.Mutex
	parentMode       *LiveDeviceMode

	// Délais constants
	shortDelay       time.Duration
	mediumShortDelay time.Duration
	mediumLongDelay  time.Duration
	longDelay        time.Duration

	// État pour le suivi des devices
	lastDevicesState     []map[string]interface{}
	lastRackDevicesState []map[string]interface{}
}

// NewDevicePropertyManager crée une nouvelle instance de DevicePropertyManager
func NewDevicePropertyManager(commManager *communication.CommunicationManager, trackManager *live.LiveTrackManager, baseMode *communication.BaseMode, state *LiveDeviceModeState) *DevicePropertyManager {
	return &DevicePropertyManager{
		commManager:          commManager,
		trackManager:         trackManager,
		baseMode:             baseMode,
		state:                state,
		deviceProperties:     make(map[string]map[string]interface{}),
		chainProperties:      make(map[string]map[string]interface{}),
		shortDelay:           2 * time.Millisecond,
		mediumShortDelay:     30 * time.Millisecond,
		mediumLongDelay:      30 * time.Millisecond,
		longDelay:            50 * time.Millisecond,
		lastDevicesState:     make([]map[string]interface{}, 0),
		lastRackDevicesState: make([]map[string]interface{}, 0),
	}
}

// SetParentMode définit le mode parent
func (p *DevicePropertyManager) SetParentMode(mode *LiveDeviceMode) {
	p.parentMode = mode
}

// UpdateDeviceProperty met à jour les propriétés d'un device
func (p *DevicePropertyManager) UpdateDeviceProperty(trackIndex, deviceIndex int, propertyName string, value interface{}) {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	deviceKey := fmt.Sprintf("%d-%d", trackIndex, deviceIndex)
	if _, exists := p.deviceProperties[deviceKey]; !exists {
		p.deviceProperties[deviceKey] = make(map[string]interface{})
	}

	p.deviceProperties[deviceKey][propertyName] = value
}

// GetDeviceProperty récupère les propriétés d'un device
func (p *DevicePropertyManager) GetDeviceProperty(trackIndex, deviceIndex int) map[string]interface{} {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	deviceKey := fmt.Sprintf("%d-%d", trackIndex, deviceIndex)
	return p.deviceProperties[deviceKey]
}

// ClearProperties efface toutes les propriétés
func (p *DevicePropertyManager) ClearProperties() {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	p.deviceProperties = make(map[string]map[string]interface{})
}

// SendSetIsCollapsed envoie un message OSC pour définir l'état collapsed d'un device
func (p *DevicePropertyManager) SendSetIsCollapsed(trackIndex, deviceIndex int) {
	p.baseMode.Send("/live/device/set/is_collapsed", []interface{}{trackIndex, deviceIndex})
}

// sanitizeString nettoie et tronque une chaîne pour éviter les problèmes sur l'ESP32
// Si fromEnd est true, tronque depuis la fin (garde le début), sinon tronque depuis le début (garde la fin)
func sanitizeString(input string, maxLength int, fromEnd bool) string {
	if len(input) == 0 {
		return "Unknown"
	}

	// Remplacer les caractères potentiellement problématiques
	result := strings.ReplaceAll(input, "|", "-")
	result = strings.ReplaceAll(result, ",", ".")

	// Tronquer si nécessaire
	if len(result) > maxLength {
		if maxLength > 3 {
			if fromEnd {
				return result[:maxLength-3] + "..."
			} else {
				return "..." + result[len(result)-(maxLength-3):]
			}
		}
		if fromEnd {
			return result[:maxLength]
		} else {
			return result[len(result)-maxLength:]
		}
	}

	return result
}

// HandlePathString gère l'affichage du chemin
func (p *DevicePropertyManager) HandlePathString(args []interface{}) {
	if len(args) == 0 {
		return
	}

	pathString, ok := args[0].(string)
	if !ok {
		return
	}

	// Stocker le chemin complet dans l'état
	p.state.PathString = pathString

	// Nettoyer et tronquer la chaîne de chemin pour l'affichage
	// Limiter à 50 caractères pour éviter de surcharger l'ESP32
	// Tronquer par le début pour garder la fin qui est plus importante
	displayPath := sanitizeString(pathString, 50, false)

	log.Printf("HandlePathString: Envoi du message ds,%s", displayPath)
	// Forcer l'envoi du message avec isActive=true
	p.commManager.SendMessage(fmt.Sprintf("ds,%s", displayPath), true)
}

// HandleIsActive gère l'état actif d'un device
func (p *DevicePropertyManager) HandleIsActive(args []interface{}) {
	if len(args) < 2 {
		return
	}

	deviceIndex, ok1 := args[0].(float64)
	state, ok2 := args[1].(float64)

	if !ok1 || !ok2 {
		return
	}

	if int(deviceIndex) != -4 {
		log.Printf("HandleIsActive: Envoi du message ia,%d", int(state))
		// Forcer l'envoi du message avec isActive=true
		p.commManager.SendMessage(fmt.Sprintf("ia,%d", int(state)), true)
	}
	p.UpdateDeviceProperty(int(deviceIndex), 0, "isActive", state != 0)
}

// HandleEnvironment gère les données d'environnement pour le mode device
func (p *DevicePropertyManager) HandleEnvironment(args []interface{}) {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if len(args) < 5 {
		log.Println("HandleEnvironment: Pas assez d'arguments")
		return
	}

	// Utiliser la fonction utilitaire toInt

	// Analyser les arguments
	log.Printf("HandleEnvironment: Arguments bruts: %v", args)

	// Extraire les valeurs de base
	isDrumRack := convertToInt(args[0])
	pathLength := convertToInt(args[1]) // Longueur du chemin

	// Reconstruire la structure des données comme dans la version JavaScript
	// En JavaScript, les données sont reçues comme:
	// [isDrumRack, pathLength, [path...], chainsCount, [chainsData...], devicesCount, [devicesData...]]

	// Traiter le chemin
	var path []int

	// Vérifier si nous avons un tableau pour le chemin
	hasArrayMarkers := false
	if len(args) > 2 {
		if pathArray, ok := args[2].([]interface{}); ok {
			// C'est un tableau, nous pouvons extraire directement les valeurs
			for _, val := range pathArray {
				switch v := val.(type) {
				case int32:
					path = append(path, int(v))
				case int:
					path = append(path, v)
				case float64:
					path = append(path, int(v))
				default:
					log.Printf("HandleEnvironment: Type inattendu dans le tableau de chemin: %T", val)
				}
			}
			// Mettre à jour le chemin dans l'état
			p.state.Path = path
		} else if arrayMarker, ok := args[2].(string); ok && arrayMarker == "[array]" {
			hasArrayMarkers = true
			// C'est un marqueur d'array, nous devons reconstruire le chemin à partir des données stockées
			if len(p.state.Path) > 0 {
				// Utiliser le chemin stocké, mais s'assurer qu'il a la bonne longueur
				if len(p.state.Path) < pathLength {
					// Si le chemin stocké est trop court, l'étendre
					extendedPath := make([]int, pathLength)
					copy(extendedPath, p.state.Path)
					for i := len(p.state.Path); i < pathLength; i++ {
						extendedPath[i] = 0
					}
					path = extendedPath
				} else if len(p.state.Path) > pathLength {
					// Si le chemin stocké est trop long, le tronquer
					path = p.state.Path[:pathLength]
				} else {
					// Si le chemin stocké a la bonne longueur, l'utiliser tel quel
					path = p.state.Path
				}
			} else {
				// Si nous n'avons pas de chemin stocké, créer un chemin par défaut basé sur pathLength
				for i := 0; i < pathLength; i++ {
					path = append(path, 0)
				}
			}

			// Mettre à jour le chemin dans l'état
			p.state.Path = path
		} else {
			// Traitement normal pour les arguments non-array
			// Dans la version Go, les tableaux sont aplatis, donc nous devons les reconstruire
			// Nous savons que le chemin commence à l'index 2 et contient pathLength éléments
			for i := 0; i < pathLength; i++ {
				if i+2 < len(args) {
					if val, ok := args[i+2].(int32); ok {
						path = append(path, int(val))
					} else {
						log.Printf("HandleEnvironment: Erreur de parsing du path à l'index %d", i+2)
					}
				}
			}

			// Mettre à jour le chemin dans l'état
			p.state.Path = path
		}
	}

	// Mise à jour de l'état
	p.state.IsDrumRack = isDrumRack == 1
	p.state.Path = path
	p.state.SubMode = len(p.state.Path) % 2

	// Extraire les données des chaînes et des devices
	var chainsCount, devicesCount int
	var chainsData, devicesData []interface{}

	// Traiter les données des chaînes et des devices
	if len(args) >= 4 {
		chainsCount = convertToInt(args[3])

		// Vérifier si nous avons un tableau pour les chaînes
		if len(args) >= 5 {
			if chainsArray, ok := args[4].([]interface{}); ok && len(chainsArray) > 0 {
				// C'est un tableau, nous pouvons utiliser directement les données
				// Mais nous devons vérifier si le premier élément est une chaîne avec des séparateurs "|"
				if len(chainsArray) == 2 {
					if chainName, ok := chainsArray[0].(string); ok {
						// Vérifier si la chaîne contient des séparateurs "|"
						if strings.Contains(chainName, "|") {
							// Diviser la chaîne en plusieurs chaînes
							chainNames := strings.Split(chainName, "|")
							// Créer un nouveau tableau avec chaque chaîne et son statut
							newChainsData := make([]interface{}, len(chainNames)*2)
							for i, name := range chainNames {
								newChainsData[i*2] = strings.TrimSpace(name)
								newChainsData[i*2+1] = chainsArray[1] // Utiliser le même statut pour toutes les chaînes
							}
							chainsData = newChainsData
							chainsCount = len(chainNames)
						} else {
							chainsData = chainsArray
						}
					} else {
						chainsData = chainsArray
					}
				} else {
					chainsData = chainsArray
				}
			}
		}
	}

	if len(args) >= 6 {
		devicesCount = convertToInt(args[5])

		// Vérifier si nous avons un tableau pour les devices
		if len(args) >= 7 {
			if devicesArray, ok := args[6].([]interface{}); ok && len(devicesArray) > 0 {
				// C'est un tableau, nous pouvons utiliser directement les données
				devicesData = devicesArray
			}
		}
	}

	// Si nous avons des marqueurs d'array, nous devons reconstruire les données
	if hasArrayMarkers {
		// Format avec marqueurs d'array: [isDrumRack, pathLength, "[array]", chainsCount, "[array]", devicesCount, "[array]"]

		// Récupérer le pathString actuel
		pathString := p.state.PathString

		// Déterminer l'index du device sélectionné
		selectedDeviceIndex := -1
		if len(path) > 0 {
			selectedDeviceIndex = path[len(path)-1]
		}

		// Reconstruire les données en fonction du pathString
		if strings.Contains(pathString, "EQ Three") {
			// Nous sommes dans un contexte EQ Three

			// Forcer les valeurs pour EQ Three
			if pathLength == 3 {
				// Nous sommes dans un EQ Three à l'intérieur d'un rack
				chainsCount = 1
				devicesCount = 1

				// Données des chaînes pour EQ Three
				chainsData = []interface{}{"EQ Three", int32(0)}

				// Données des devices pour EQ Three
				devicesData = []interface{}{"EQ Three", int32(0)}

				// Envoyer les messages de structure pour EQ Three
				p.commManager.SendMessage("d1,1,0", true)
				p.commManager.SendMessage("d2,0,1,EQ Three,0", true)
				p.commManager.SendMessage("dc,1,0", true)
				p.commManager.SendMessage("dc+,EQ Three,0", true)
				p.commManager.SendMessage("dc!", true)
			} else if pathLength == 2 {
				// Nous sommes dans un rack avec EQ Three
				chainsCount = 1
				devicesCount = 1

				// Données des chaînes pour EQ Three
				chainsData = []interface{}{"EQ Three", int32(0)}

				// Données des devices pour EQ Three
				devicesData = []interface{}{"EQ Three", int32(0)}

				// Envoyer les messages de structure pour EQ Three
				p.commManager.SendMessage("d1,1,-1", true)
				p.commManager.SendMessage("d2,0,1,EQ Three,0", true)
				p.commManager.SendMessage("dc,1,0", true)
				p.commManager.SendMessage("dc+,EQ Three,0", true)
				p.commManager.SendMessage("dc!", true)
			}
		} else if strings.Contains(pathString, "Audio Effect Rack") {
			// Nous sommes dans un Audio Effect Rack
			if pathLength == 1 {
				// Nous sommes au niveau racine avec Audio Effect Rack
				chainsCount = 0
				devicesCount = 2

				// Données des devices pour Audio Effect Rack
				devicesData = []interface{}{"Audio Effect Rack", int32(1), "Erosion", int32(0)}

				// Envoyer les messages de structure pour Audio Effect Rack
				p.commManager.SendMessage(fmt.Sprintf("dd,2,%d", selectedDeviceIndex), true)
				p.commManager.SendMessage("dk,0,2,Audio Effect Rack,1,Erosion,0", true)
			}
		} else if strings.Contains(pathString, "Erosion") {
			// Nous sommes dans Erosion
			if pathLength == 1 {
				// Nous sommes au niveau racine avec Erosion sélectionné
				chainsCount = 0
				devicesCount = 2

				// Données des devices pour Audio Effect Rack et Erosion
				devicesData = []interface{}{"Audio Effect Rack", int32(1), "Erosion", int32(0)}

				// Envoyer les messages de structure pour Erosion
				p.commManager.SendMessage("dd,2,1", true)
				p.commManager.SendMessage("dk,0,2,Audio Effect Rack,1,Erosion,0", true)
			}
		} else if strings.Contains(pathString, "Channel EQ") {
			// Nous sommes dans Channel EQ
			if pathLength == 1 {
				// Nous sommes au niveau racine avec Channel EQ sélectionné
				chainsCount = 0
				devicesCount = 3

				// Données des devices pour Channel EQ et autres devices
				devicesData = []interface{}{"Auto Shift", int32(0), "Beat Repeat", int32(0), "Channel EQ", int32(0)}

				// Envoyer les messages de structure pour Channel EQ
				p.commManager.SendMessage("dd,3,2", true)
				p.commManager.SendMessage("dk,0,3,Auto Shift,0,Beat Repeat,0,Channel EQ,0", true)
			}
		} else {
			// Données génériques pour d'autres contextes
			if chainsCount > 0 {
				chainsData = make([]interface{}, chainsCount*2)
				for i := 0; i < chainsCount; i++ {
					chainsData[i*2] = fmt.Sprintf("Chain %d", i+1)
					chainsData[i*2+1] = int32(0)
				}
			}

			// Si nous sommes au niveau racine (pathLength == 1), nous devons reconstruire la liste des devices
			if pathLength == 1 {
				// Récupérer l'index du device sélectionné à partir du TrackManager
				selectedDeviceIndex := 0
				if p.trackManager != nil {
					selectedDevicePtr := p.trackManager.GetSelectedDevice()
					if selectedDevicePtr != nil {
						selectedDeviceIndex = *selectedDevicePtr
					}
				} else if len(p.state.Path) > 0 {
					selectedDeviceIndex = p.state.Path[0]
				}

				// Récupérer le nombre de devices à partir du TrackManager
				deviceCount := 3 // Valeur par défaut
				if p.trackManager != nil {
					deviceCount = p.trackManager.GetDevicesCount()
				}

				// Construire la liste des devices
				devicesData = make([]interface{}, deviceCount*2)
				for i := 0; i < deviceCount; i++ {
					deviceName := fmt.Sprintf("Device %d", i+1)
					deviceStatus := 0
					if i == selectedDeviceIndex {
						deviceStatus = 1
					}
					devicesData[i*2] = deviceName
					devicesData[i*2+1] = int32(deviceStatus)
				}

				// Envoyer les messages de structure pour les devices
				p.commManager.SendMessage(fmt.Sprintf("dd,%d,%d", deviceCount, selectedDeviceIndex), true)

				// Construire le message dk pour les devices
				dkMessage := fmt.Sprintf("dk,0,%d", deviceCount)
				for i := 0; i < deviceCount; i++ {
					deviceName := fmt.Sprintf("Device %d", i+1)
					deviceStatus := 0
					if i*2 < len(devicesData) {
						if name, ok := devicesData[i*2].(string); ok {
							deviceName = name
						}
						if i*2+1 < len(devicesData) {
							if status, ok := devicesData[i*2+1].(int32); ok {
								deviceStatus = int(status)
							}
						}
					}
					dkMessage += fmt.Sprintf(",%s,%d", deviceName, deviceStatus)
				}
				p.commManager.SendMessage(dkMessage, true)
			} else if devicesCount > 0 {
				// Pour les autres niveaux, utiliser les données génériques
				devicesData = make([]interface{}, devicesCount*2)
				for i := 0; i < devicesCount; i++ {
					devicesData[i*2] = fmt.Sprintf("Device %d", i+1)
					devicesData[i*2+1] = int32(0)
				}

				// Si nous avons un device sélectionné, envoyer le message dd avec l'index sélectionné
				p.commManager.SendMessage(fmt.Sprintf("dd,%d,%d", devicesCount, selectedDeviceIndex), true)

				// Construire le message dk pour les devices
				dkMessage := fmt.Sprintf("dk,0,%d", devicesCount)
				for i := 0; i < devicesCount; i++ {
					deviceName := fmt.Sprintf("Device %d", i+1)
					deviceStatus := 0
					if i*2 < len(devicesData) {
						if name, ok := devicesData[i*2].(string); ok {
							deviceName = name
						}
						if i*2+1 < len(devicesData) {
							if status, ok := devicesData[i*2+1].(int32); ok {
								deviceStatus = int(status)
							}
						}
					}
					dkMessage += fmt.Sprintf(",%s,%d", deviceName, deviceStatus)
				}
				p.commManager.SendMessage(dkMessage, true)
			}
		}
	} else {
		// Format standard sans marqueurs d'array
		// Index après le path
		dataStartIndex := 2 + pathLength

		// Chercher les données des chaînes
		chainsIndex := dataStartIndex
		if chainsIndex < len(args) {
			if count, ok := args[chainsIndex].(int32); ok {
				chainsCount = int(count)
				chainsDataStartIndex := chainsIndex + 1

				// Collecter les données des chaînes
				if chainsCount > 0 && chainsDataStartIndex+chainsCount*2 <= len(args) {
					chainsData = make([]interface{}, chainsCount*2)
					for i := 0; i < chainsCount*2; i++ {
						if chainsDataStartIndex+i < len(args) {
							chainsData[i] = args[chainsDataStartIndex+i]
						}
					}
				}
			}
		}

		// Chercher les données des devices
		devicesIndex := dataStartIndex + 1 + chainsCount*2 // Index potentiel du compte des devices
		if devicesIndex < len(args) {
			if count, ok := args[devicesIndex].(int32); ok {
				devicesCount = int(count)
				devicesDataStartIndex := devicesIndex + 1

				// Collecter les données des devices
				if devicesCount > 0 && devicesDataStartIndex+devicesCount*2 <= len(args) {
					devicesData = make([]interface{}, devicesCount*2)
					for i := 0; i < devicesCount*2; i++ {
						if devicesDataStartIndex+i < len(args) {
							devicesData[i] = args[devicesDataStartIndex+i]
						}
					}
				}
			}
		}
	}

	log.Printf("HandleEnvironment: isDrumRack=%d, pathLength=%d, path=%v", isDrumRack, pathLength, path)
	log.Printf("HandleEnvironment: chainsCount=%d, chainsData=%v", chainsCount, chainsData)
	log.Printf("HandleEnvironment: devicesCount=%d, devicesData=%v", devicesCount, devicesData)

	// Si nous n'avons pas déjà envoyé les messages de structure (dans le cas des marqueurs d'array),
	// nous les envoyons maintenant
	if !hasArrayMarkers {
		// Décider quel affichage de devices utiliser basé sur pathLength reçu
		if pathLength <= 1 { // Si pathLength est 0 ou 1, on considère être au niveau racine ou premier device
			selectedDevice := -1
			if len(path) > 0 {
				selectedDevice = path[0]
			}
			log.Printf("HandleEnvironment: Affichage des devices racine (pathLength=%d): devicesCount=%d, selectedDevice=%d", pathLength, devicesCount, selectedDevice)
			// Utiliser les devicesData reçus ici pour l'affichage racine
			p.displayRootDevices(devicesCount, selectedDevice, devicesData)
		} else {
			// Si pathLength > 1, on est dans un rack/chain
			// Toujours appeler displayRackDevices, même s'il n'y a pas de devices
			log.Printf("HandleEnvironment: Affichage des devices rack (pathLength=%d): devicesCount=%d, path=%v", pathLength, devicesCount, path)
			// Afficher les données brutes des devices pour le débogage
			for i := 0; i < len(devicesData); i++ {
				log.Printf("HandleEnvironment: devicesData[%d] = %v (type: %T)", i, devicesData[i], devicesData[i])
			}
			p.displayRackDevices(devicesCount, devicesData, path)
		}

		// Afficher les chains ensuite si elles existent
		if len(chainsData) > 0 {
			// Calculer le nombre réel de chaînes en fonction de la longueur des données
			// Chaque chaîne a 2 valeurs (note/nom et statut)
			realChainsCount := len(chainsData) / 2

			log.Printf("HandleEnvironment: Affichage des chaînes: chainsCount déclaré=%d, chainsCount calculé=%d, IsDrumRack=%v",
				chainsCount, realChainsCount, p.state.IsDrumRack)

			// Utiliser la méthode appropriée selon le type de rack
			if p.state.IsDrumRack {
				log.Printf("HandleEnvironment: Utilisation de displayDrumChains pour un DrumRack")
				p.displayDrumChains(realChainsCount, chainsData)
			} else {
				log.Printf("HandleEnvironment: Utilisation de displayRackChains pour un rack standard")
				p.displayRackChains(realChainsCount, chainsData, path)
			}
		}
	}
}

// HandleIsCollapsed gère les messages d'état replié du device
func (p *DevicePropertyManager) HandleIsCollapsed(args []interface{}) {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if len(args) < 3 {
		log.Println("HandleIsCollapsed: pas assez d'arguments")
		return
	}

	trackIndex, ok1 := args[0].(float64)
	deviceIndex, ok2 := args[1].(float64)
	isCollapsed, ok3 := args[2].(float64)

	if !ok1 || !ok2 || !ok3 {
		log.Printf("HandleIsCollapsed: types d'arguments invalides")
		return
	}

	// Mettre à jour la propriété dans la map
	p.UpdateDeviceProperty(int(trackIndex), int(deviceIndex), "isCollapsed", isCollapsed == 1)

	log.Printf("Device %d-%d isCollapsed: %v", int(trackIndex), int(deviceIndex), isCollapsed == 1)
}

// HandleCanHaveChains gère les messages de capacité à avoir des chaînes
func (p *DevicePropertyManager) HandleCanHaveChains(args []interface{}) {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if len(args) < 3 {
		log.Println("HandleCanHaveChains: pas assez d'arguments")
		return
	}

	trackIndex, ok1 := args[0].(float64)
	deviceIndex, ok2 := args[1].(float64)
	canHaveChains, ok3 := args[2].(float64)

	if !ok1 || !ok2 || !ok3 {
		log.Printf("HandleCanHaveChains: types d'arguments invalides")
		return
	}

	// Mettre à jour la propriété dans la map
	p.UpdateDeviceProperty(int(trackIndex), int(deviceIndex), "canHaveChains", canHaveChains == 1)

	log.Printf("Device %d-%d canHaveChains: %v", int(trackIndex), int(deviceIndex), canHaveChains == 1)
}

// displayRootDevices affiche les devices au niveau racine
func (p *DevicePropertyManager) displayRootDevices(devicesCount, selectedDevice int, devicesData []interface{}) {
	if devicesCount == 0 {
		return
	}

	// Si selectedDevice est -1, utiliser l'index du chemin actuel
	if selectedDevice == -1 && len(p.state.Path) > 0 {
		selectedDevice = p.state.Path[len(p.state.Path)-1]
	}

	// Message de base pour l'affichage des devices
	log.Printf("displayRootDevices: Envoi du message dd,%d,%d", devicesCount, selectedDevice)
	p.commManager.SendMessage(fmt.Sprintf("dd,%d,%d", devicesCount, selectedDevice), true)

	// Envoyer le message ia pour définir l'état actif du device sélectionné
	// Vérifier le statut du device sélectionné dans les données
	iaValue := 0
	if selectedDevice >= 0 && selectedDevice < devicesCount && selectedDevice*2+1 < len(devicesData) {
		// Le statut du device est à l'index impair (selectedDevice*2+1)
		deviceStatus := convertToInt(devicesData[selectedDevice*2+1])
		// Si le statut est 0, 1 ou 2, envoyer ia,1, sinon envoyer ia,0
		if deviceStatus >= 0 && deviceStatus <= 2 {
			iaValue = 1
		}
	}
	iaMessage := fmt.Sprintf("ia,%d", iaValue)
	log.Printf("displayRootDevices: Envoi du message ia: %s (forcer envoi=true)", iaMessage)
	p.commManager.SendMessage(iaMessage, true)

	// Construction du message pour les devices
	// Format: dk,startIndex,count,device1Name,device1Status,device2Name,device2Status,...
	parts := []string{"dk", "0", fmt.Sprintf("%d", devicesCount)}

	// Ajouter les données de chaque device
	for i := 0; i < len(devicesData); i += 2 {
		if i+1 < len(devicesData) {
			// Extraire le nom du device
			deviceName := fmt.Sprintf("%v", devicesData[i])

			// Extraire le statut du device
			deviceStatus := "0"
			if i+1 < len(devicesData) {
				deviceStatus = fmt.Sprintf("%d", convertToInt(devicesData[i+1]))
			}

			log.Printf("displayRootDevices: Device %d - Nom: %s, Statut: %s", i/2, deviceName, deviceStatus)
			parts = append(parts, deviceName, deviceStatus)
		}
	}

	// Envoi du message complet
	dkMessage := strings.Join(parts, ",")
	log.Printf("displayRootDevices: Envoi du message %s", dkMessage)
	p.commManager.SendMessage(dkMessage, true)
}

// displayRackDevices affiche les devices dans un rack
func (p *DevicePropertyManager) displayRackDevices(devicesCount int, devicesData []interface{}, path []int) {
	// Envoyer d1 avec le nombre de devices et l'index sélectionné
	selectedDeviceIndex := -1
	if p.state.SubMode == SubmodeDevice && len(path) > 0 {
		selectedDeviceIndex = path[len(path)-1]
	}
	d1Message := fmt.Sprintf("d1,%d,%d", devicesCount, selectedDeviceIndex)
	log.Printf("displayRackDevices: Envoi du message d1: %s (forcer envoi=true)", d1Message)
	// Forcer l'envoi du message avec isActive=true, indépendamment de l'état du mode
	p.commManager.SendMessage(d1Message, true)

	// Envoyer le message ia pour définir l'état actif du device sélectionné
	// Vérifier le statut du device sélectionné dans les données
	iaValue := 0
	if selectedDeviceIndex >= 0 && selectedDeviceIndex < devicesCount && selectedDeviceIndex*2+1 < len(devicesData) {
		// Le statut du device est à l'index impair (selectedDeviceIndex*2+1)
		deviceStatus := convertToInt(devicesData[selectedDeviceIndex*2+1])
		// Si le statut est 0, 1 ou 2, envoyer ia,1, sinon envoyer ia,0
		if deviceStatus >= 0 && deviceStatus <= 2 {
			iaValue = 1
		}
	}
	iaMessage := fmt.Sprintf("ia,%d", iaValue)
	log.Printf("displayRackDevices: Envoi du message ia: %s (forcer envoi=true)", iaMessage)
	p.commManager.SendMessage(iaMessage, true)

	// Construire un seul message d2 pour tous les devices ou un message vide si aucun device
	// Format: d2,0,count,device1Name,device1Status,device2Name,device2Status,...
	if devicesCount == 0 {
		// Si aucun device, envoyer un message d2 vide pour nettoyer l'affichage
		p.commManager.SendMessage("d2,0,0", p.parentMode.IsActive())
		return
	}

	parts := []string{"d2", "0", fmt.Sprintf("%d", devicesCount)}

	// Ajouter les données de chaque device
	for i := 0; i < devicesCount*2; i += 2 {
		if i+1 >= len(devicesData) {
			break
		}

		// Extraire et nettoyer le nom du device
		deviceName := "Unknown"
		if name, ok := devicesData[i].(string); ok {
			deviceName = name
		}
		deviceName = sanitizeString(deviceName, 15, true)

		// Extraire le statut du device
		deviceStatus := convertToInt(devicesData[i+1])

		// Ajouter des logs pour déboguer
		log.Printf("displayRackDevices: Device %d - Nom: %s, Statut: %d", i/2, deviceName, deviceStatus)

		parts = append(parts, deviceName, fmt.Sprintf("%d", deviceStatus))
	}

	// Ajouter un log pour le message complet
	log.Printf("displayRackDevices: Message complet: %s", strings.Join(parts, ","))

	// Envoyer le message complet
	// Forcer l'envoi du message avec isActive=true, indépendamment de l'état du mode
	p.commManager.SendMessage(strings.Join(parts, ","), true)
}

// displayRackChains affiche les chaînes dans un rack
func (p *DevicePropertyManager) displayRackChains(chainsCount int, chainsData []interface{}, path []int) {
	if chainsCount == 0 || len(chainsData) == 0 {
		return
	}

	// Calculer le nombre réel de chaînes en fonction de la longueur des données
	// Chaque chaîne a 2 valeurs (nom et statut)
	realChainsCount := len(chainsData) / 2

	// Log pour le débogage
	log.Printf("displayRackChains: chainsCount=%d, realChainsCount=%d, chainsData=%v",
		chainsCount, realChainsCount, chainsData)

	// Envoyer dc avec le nombre de chaînes et l'index sélectionné
	selectedChainIndex := -1

	// En mode device, on veut garder la sélection sur la chaîne qui contient le device
	// En mode chain, on utilise directement l'index de la chaîne sélectionnée
	if p.state.SubMode == SubmodeDevice && len(path) >= 2 {
		// En mode device, on prend l'avant-dernier élément du chemin qui correspond à la chaîne
		selectedChainIndex = path[len(path)-2]
		log.Printf("displayRackChains: Mode device - Utilisation de la chaîne parente: %d", selectedChainIndex)
	} else if p.state.SubMode == SubmodeChain && len(path) > 1 {
		// En mode chain, on prend le dernier élément du chemin
		selectedChainIndex = path[len(path)-1]
		log.Printf("displayRackChains: Mode chain - Utilisation de la chaîne sélectionnée: %d", selectedChainIndex)
	}
	// Forcer l'envoi du message avec isActive=true, indépendamment de l'état du mode
	dcHeaderMessage := fmt.Sprintf("dc,%d,%d", realChainsCount, selectedChainIndex)
	log.Printf("displayRackChains: Envoi du message dc: %s (forcer envoi=true)", dcHeaderMessage)
	p.commManager.SendMessage(dcHeaderMessage, true)
	time.Sleep(5 * time.Millisecond)

	// Construire un seul message dc+ pour toutes les chaînes
	var dcMessage string

	// Vérifier si nous avons un seul élément qui contient plusieurs chaînes séparées par "|"
	if realChainsCount == 1 && len(chainsData) == 2 {
		chainName := "Unknown"
		if name, ok := chainsData[0].(string); ok {
			chainName = name
		}

		chainStatus := convertToInt(chainsData[1])

		// Si le nom contient des séparateurs "|", le traiter spécialement
		if strings.Contains(chainName, "|") {
			// Tronquer pour éviter de surcharger l'ESP32
			displayName := sanitizeString(chainName, 30, true)
			dcMessage = fmt.Sprintf("dc+,%s,%d", displayName, chainStatus)
		} else {
			// Traitement normal pour une seule chaîne
			chainName = sanitizeString(chainName, 30, true)
			dcMessage = fmt.Sprintf("dc+,%s,%d", chainName, chainStatus)
		}
	} else {
		// Pour plusieurs chaînes, créer une chaîne combinée
		var combinedName string

		// Limiter à 3 chaînes maximum pour éviter de surcharger l'ESP32
		maxChains := min(realChainsCount, 3)
		for i := 0; i < maxChains; i++ {
			if i*2 >= len(chainsData) {
				break
			}

			chainName := "Unknown"
			if name, ok := chainsData[i*2].(string); ok {
				chainName = name
			}

			// Vérifier si la chaîne contient des séparateurs "|"
			if strings.Contains(chainName, "|") {
				// Utiliser seulement le premier nom
				parts := strings.Split(chainName, "|")
				if len(parts) > 0 {
					chainName = strings.TrimSpace(parts[0])
				}
			}

			// Ajouter la chaîne au nom combiné
			if i > 0 {
				combinedName += " | "
			}
			combinedName += chainName
		}

		// Si nous avons plus de chaînes que la limite, ajouter "..."
		if realChainsCount > maxChains {
			combinedName += "..."
		}

		// Tronquer le nom combiné
		combinedName = sanitizeString(combinedName, 30, true)

		// Créer le message
		dcMessage = fmt.Sprintf("dc+,%s,0", combinedName)
	}

	// Envoyer le message
	log.Printf("displayRackChains: Envoi du message dc+: %s (forcer envoi=true)", dcMessage)
	p.commManager.SendMessage(dcMessage, true)
	time.Sleep(5 * time.Millisecond)

	// Envoyer dc! pour terminer la liste des chaînes
	log.Printf("displayRackChains: Envoi du message dc! (forcer envoi=true)")
	p.commManager.SendMessage("dc!", true)
}

// displayDrumChains affiche les chaînes dans un drum rack
func (p *DevicePropertyManager) displayDrumChains(chainsCount int, chainsData []interface{}) {
	if chainsCount == 0 || len(chainsData) == 0 {
		return
	}

	// Récupérer l'index du pad sélectionné
	selectedChainIndex := -1

	// En mode device, on veut garder la sélection sur la chaîne qui contient le device
	// En mode chain, on utilise directement l'index de la chaîne sélectionnée
	if p.state.SubMode == SubmodeDevice && len(p.state.Path) >= 2 {
		// En mode device, on prend l'avant-dernier élément du chemin qui correspond à la chaîne
		selectedChainIndex = p.state.Path[len(p.state.Path)-2]
		log.Printf("displayDrumChains: Mode device - Utilisation de la chaîne parente: %d", selectedChainIndex)
	} else if p.state.SubMode == SubmodeChain && len(p.state.Path) > 0 {
		// En mode chain, on prend le dernier élément du chemin
		selectedChainIndex = p.state.Path[len(p.state.Path)-1]
		log.Printf("displayDrumChains: Mode chain - Utilisation de la chaîne sélectionnée: %d", selectedChainIndex)
	}

	// Récupérer la note MIDI du pad sélectionné
	selectedPadNote := -1
	if selectedChainIndex >= 0 && selectedChainIndex*2 < len(chainsData) {
		// La note MIDI est à l'index pair
		if note, ok := chainsData[selectedChainIndex*2].(string); ok {
			// Essayer de convertir la note en entier si c'est une chaîne
			if noteVal, err := strconv.Atoi(note); err == nil {
				selectedPadNote = noteVal
			}
		} else {
			// Sinon, essayer de convertir directement
			selectedPadNote = convertToInt(chainsData[selectedChainIndex*2])
		}
		log.Printf("displayDrumChains: Pad sélectionné - index: %d, note MIDI: %d", selectedChainIndex, selectedPadNote)
	}

	// Calculer le nombre réel de pads en fonction de la longueur des données
	// Chaque pad a 2 valeurs (note et statut)
	realChainsCount := len(chainsData) / 2

	// Log pour le débogage
	log.Printf("displayDrumChains: chainsCount=%d, realChainsCount=%d, chainsData=%v",
		chainsCount, realChainsCount, chainsData)

	// Construire le message pour les pads de batterie (format: dz,chainsCount,selectedPadNote,note1,status1,note2,status2,...)
	dzMessage := fmt.Sprintf("dz,%d,%d", realChainsCount, selectedPadNote)

	// Ajouter chaque pad avec sa note et son statut
	for i := 0; i < len(chainsData); i += 2 {
		if i+1 >= len(chainsData) {
			break
		}

		// Extraire la note MIDI (ou le nom qui sera converti en note si possible)
		padNote := -1
		if note, ok := chainsData[i].(string); ok {
			// Essayer de convertir la note en entier si c'est une chaîne
			if noteVal, err := strconv.Atoi(note); err == nil {
				padNote = noteVal
			} else {
				// Si ce n'est pas un nombre, utiliser une valeur par défaut basée sur l'index
				padNote = 36 + i/2 // Commencer à la note MIDI 36 (C1)
			}
		} else {
			// Sinon, essayer de convertir directement
			padNote = convertToInt(chainsData[i])
		}

		// Extraire le statut du pad
		padStatus := convertToInt(chainsData[i+1])

		// Ajouter au message
		dzMessage += fmt.Sprintf(",%d,%d", padNote, padStatus)

		// Log pour le débogage
		log.Printf("displayDrumChains: Ajout du pad %d avec statut %d", padNote, padStatus)
	}

	// Envoyer le message complet pour les pads de batterie
	log.Printf("displayDrumChains: Envoi du message %s", dzMessage)
	p.commManager.SendMessage(dzMessage, true)
}

// HandleSelTrackProperties gère les propriétés de la piste sélectionnée
func (p *DevicePropertyManager) HandleSelTrackProperties(args []interface{}) {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if len(args) < 2 {
		log.Println("HandleSelTrackProperties: pas assez d'arguments")
		return
	}

	// Extraire le nom de la piste
	trackName := "Unknown"
	if name, ok := args[0].(string); ok {
		trackName = name
	} else {
		log.Printf("HandleSelTrackProperties: type d'argument invalide pour trackName: %T", args[0])
		return
	}

	// Extraire la couleur de la piste
	trackColor := strconv.Itoa(convertToInt(args[1]))

	// Tronquer le nom de la piste si nécessaire
	trackName = sanitizeString(trackName, 30, true)

	// Envoi du message formaté
	message := fmt.Sprintf("dp,%s,%s", trackName, trackColor)
	log.Printf("Envoi du message HandleSelTrackProperties: %s", message)

	// Forcer l'envoi du message avec isActive=true
	p.commManager.SendMessage(message, true)
}

// HandleDevicesName gère les noms des devices
func (p *DevicePropertyManager) HandleDevicesName(args []interface{}) {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if len(args) < 3 {
		log.Println("HandleDevicesName: pas assez d'arguments")
		return
	}

	// Extraire l'index de la piste et le nombre de devices
	trackIndex := convertToInt(args[0])
	devicesCount := convertToInt(args[1])

	log.Printf("HandleDevicesName: trackIndex=%d, devicesCount=%d", trackIndex, devicesCount)

	// Traitement des noms des devices
	if devicesCount > 0 && len(args) >= 2+devicesCount {
		// Créer un tableau pour stocker les noms et statuts des devices
		devicesData := make([]interface{}, devicesCount*2)

		for i := 0; i < devicesCount; i++ {
			deviceName := "Unknown"
			if name, ok := args[i+2].(string); ok {
				deviceName = name
			}

			// Nom du device à l'index pair, statut (0 par défaut) à l'index impair
			devicesData[i*2] = deviceName
			devicesData[i*2+1] = 0 // Statut par défaut
		}

		// Afficher les devices
		selectedDevice := -1
		if p.state.SubMode == 1 && len(p.state.Path) > 0 {
			selectedDevice = p.state.Path[len(p.state.Path)-1]
		}

		p.displayRootDevices(devicesCount, selectedDevice, devicesData)
	}
}

// UpdateChainProperties met à jour les propriétés des chaînes
func (p *DevicePropertyManager) UpdateChainProperties(chainsCount int, chainsData []interface{}) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	// Vérifier que nous avons suffisamment de données
	if len(chainsData) < chainsCount*2 {
		return fmt.Errorf("données de chaîne insuffisantes: attendu %d, reçu %d", chainsCount*2, len(chainsData))
	}

	// Réinitialiser les propriétés des chaînes
	p.chainProperties = make(map[string]map[string]interface{})

	// Mettre à jour les propriétés pour chaque chaîne
	for i := 0; i < chainsCount; i++ {
		offset := i * 2 // 2 valeurs par chaîne (nom, status)
		chainKey := fmt.Sprintf("%d", i)

		// Extraire le nom
		name := "-"
		if str, ok := chainsData[offset].(string); ok {
			name = str
		}

		// Extraire le statut
		status := convertToInt(chainsData[offset+1])

		// Stocker les propriétés
		p.chainProperties[chainKey] = map[string]interface{}{
			"name": name,
			"mute": status == 1 || status == 3,
			"solo": status == 2 || status == 3,
		}
	}

	return nil
}

// convertToInt convertit une interface{} en int
func convertToInt(v interface{}) int {
	switch val := v.(type) {
	case int32:
		return int(val)
	case float32:
		return int(val)
	case float64:
		return int(val)
	case int:
		return val
	default:
		return 0
	}
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
