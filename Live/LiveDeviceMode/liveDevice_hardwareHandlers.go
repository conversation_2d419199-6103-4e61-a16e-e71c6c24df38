package livedevicemode

import (
	"encoding/json"
	"fmt"
	"log"
	"math"
	communication "oscbridge/Communication"
	live "oscbridge/Live"
	"oscbridge/Live/types"
)

// HandleHardwareEvent traite les événements matériels reçus
func (m *LiveDeviceMode) HandleHardwareEvent(event communication.HardwareEvent) {
	log.Printf("Mode device: Événement matériel reçu: Type=%s", event.Type)

	switch event.Type {
	case "touch":
		m.handleTouchEvent(event.TouchEvent)
	case "button":
		m.handleButtonEvent(event.ButtonEvent)
	case "encoder":
		m.handleEncoderEvent(event.EncoderEvent)
	}
}

// handleTouchEvent traite les événements tactiles
func (m *LiveDeviceMode) handleTouchEvent(event *communication.TouchEvent) {
	if event == nil {
		return
	}

	log.Printf("Mode device: Toucher de type %s sur l'index %d",
		event.TouchType, event.Index)

	switch event.TouchType {
	case "mv": // Move device
		// Récupérer l'index2 depuis la structure TouchEvent
		log.Printf("Move device: source=%d, target=%d", event.Index, event.Index2)
		m.handleMoveDevice(event.Index, event.Index2)

	case "sd": // Select device
		m.handleSelectDevice(event.Index)

	case "rd": // Rack device
		m.handleRackDevice(event.Index)

	case "rc": // Rack chain
		m.handleRackChain(event.Index)

	case "dp": // Drum pad
		m.handleDrumPad(event.Index)

	case "ba": // Back
		m.handleBack()

	case "ld": // Lock/Unlock device
		m.handleLockDevice(event.Index)

	case "ia": // Toggle device active
		m.handleToggleDeviceActive()

	case "sm": // Switch mode
		m.handleSwitchMode(event.Index)

	case "le": // Learn mode
		m.handleLearnMode(event.Index)

	case "on": // Toggle device on/off
		m.handleToggleDeviceOnOff(event.Index)

	case "de": // Delete device
		m.handleDeleteDevice(event.Index)

	case "ho": // Hotswap
		m.handleHotswap(event.Index)

	case "st": // Toggle parameter/chain parameter
		m.handleToggleParameter(event.Index)

	case "bs": // Browser select - gestion des événements du browser en mode device
		log.Printf("Événement browser select reçu en mode device: index=%d", event.Index)
		// Si on reçoit un événement bs en mode device, on peut le transférer au mode browser
		// ou simplement l'ignorer car on n'est pas dans le bon mode

	default:
		log.Printf("Type de toucher non géré: %s", event.TouchType)
	}
}

// handleButtonEvent traite les événements de bouton
func (m *LiveDeviceMode) handleButtonEvent(event *communication.ButtonEvent) {
	if event == nil {
		return
	}

	log.Printf("Mode device: Bouton %d état %d", event.Index, event.State)
	
	// Ne traiter que les boutons appuyés (state = 1), ignorer les relâchements (state = 0)
	if event.State != 1 {
		log.Printf("Mode device: Bouton %d relâché, ignoré", event.Index)
		return
	}
	
	log.Printf("Mode device: Bouton %d appuyé, traitement", event.Index)

	switch event.Index {
	case 0: // B00 - Switch to Learn Mode
		m.BaseMode.Emit("modeChange", "learn")
		log.Printf("Mode Device: B00 - Switch to Learn Mode")
	case 1: // B01 - Switch to Track Mode (quickview off)
		m.BaseMode.Emit("modeChange", "track")
		log.Printf("Mode Device: B01 - Switch to Track Mode (quickview off)")
	case 2: // B02 - Switch to Volume Mode
		m.BaseMode.Emit("modeChange", "volume")
		log.Printf("Mode Device: B02 - Switch to Volume Mode")
	case 3: // B03 - Return to page 1
		if m.parameterManager != nil {
			parametersCount := m.trackManager.GetParametersCount()
			if m.parameterManager.SetPage(1, parametersCount, SlotsPerPage, m.state.IsLocked) {
				m.state.CurrentPage = 1
				totalPages := (parametersCount + SlotsPerPage - 1) / SlotsPerPage
				log.Printf("Mode Device: B03 - Return to page 1/%d", totalPages)
				m.commManager.SendMessage(fmt.Sprintf("pd%d/%d", m.state.CurrentPage, totalPages), m.isActive)
			}
		}
	case 4: // B04 - Switch to Browse Mode
		m.BaseMode.Emit("modeChange", "browser")
		log.Printf("Mode Device: B04 - Switch to Browse Mode")
	// B05 est maintenant géré comme bouton transversal dans HardwareManager
	case 6: // B06 - (non défini)
		log.Printf("Mode Device: B06 - Non défini")
	case 7: // B07 - (non défini)
		log.Printf("Mode Device: B07 - Non défini")
	case 8: // B08 - Page down
		m.pageDown()
		log.Printf("Mode Device: B08 - Page down")
	case 9: // B09 - Page up
		m.pageUp()
		log.Printf("Mode Device: B09 - Page up")
	case 13: // B13 - Toggle lock
		if m.state.IsLocked {
			m.DisableLock()
			log.Printf("Mode Device: B13 - Unlock")
		} else {
			m.EnableLock()
			log.Printf("Mode Device: B13 - Lock")
		}
	default:
		log.Printf("Mode Device: Bouton non géré: %d", event.Index)
	}
}

// handleEncoderEvent traite les événements d'encodeur
func (m *LiveDeviceMode) handleEncoderEvent(event *communication.EncoderEvent) {
	if event == nil {
		return
	}

	log.Printf("Mode device: Encodeur %d tourné avec la valeur %d, submode=%d",
		event.Index, event.Value, m.state.SubMode)

	// Vérifier que l'index est valide (0-7 pour les 8 encodeurs)
	if event.Index < 0 || event.Index >= SlotsPerPage {
		log.Printf("Index d'encodeur invalide: %d", event.Index)
		return
	}

	// Calculer le delta normalisé (les valeurs d'encodeur sont sur une base 1000)
	rawDelta := float64(event.Value) / 1000.0

	// Récupérer le buffer actuel pour cet encodeur
	currentBuffer, exists := m.state.EncoderBuffers[event.Index]
	if !exists {
		currentBuffer = 0.0
	}

	// Ajouter le delta au buffer
	currentBuffer += rawDelta
	m.state.EncoderBuffers[event.Index] = currentBuffer

	// Utiliser un seuil uniforme, Python gérera les différences selon le type de paramètre
	const bufferThreshold = 0.005
	var deltaToSend float64
	var shouldSend bool = false

	if math.Abs(currentBuffer) >= bufferThreshold {
		// Calculer combien de "pas" de threshold sont dans le buffer
		steps := int(currentBuffer / bufferThreshold)
		if steps == 0 && currentBuffer < 0 {
			steps = -1 // Assurer au moins un pas négatif si le buffer est négatif
		}

		// Calculer le delta à envoyer (multiple du threshold)
		deltaToSend = float64(steps) * bufferThreshold

		// Mettre à jour le buffer en soustrayant ce qui a été envoyé
		currentBuffer -= deltaToSend
		m.state.EncoderBuffers[event.Index] = currentBuffer

		shouldSend = true

		log.Printf("Encodeur %d: buffer=%.6f, steps=%d, deltaToSend=%.6f, newBuffer=%.6f",
			event.Index, currentBuffer+deltaToSend, steps, deltaToSend, currentBuffer)
	} else {
		// Pas assez de mouvement pour déclencher un changement
		log.Printf("Encodeur %d: buffer=%.6f (en dessous du seuil)", event.Index, currentBuffer)
		return
	}

	if !shouldSend {
		return
	}

	// Traiter selon le submode
	if m.state.SubMode == SubmodeDevice {
		// Mode device : tous les encodeurs contrôlent les paramètres du device
		m.handleDeviceParameterEncoder(event.Index, deltaToSend)
	} else {
		// Mode chain : les 4 premiers encodeurs contrôlent volume/pan/mute/solo
		m.handleChainParameterEncoder(event.Index, deltaToSend)
	}
}

// handleDeviceParameterEncoder gère les changements des encodeurs de paramètres de device
func (m *LiveDeviceMode) handleDeviceParameterEncoder(encoderIndex int, delta float64) {
	// Calculer l'index du paramètre basé sur la page actuelle
	// IMPORTANT: Le paramètre 0 est le on/off du device, donc on commence au paramètre 1
	var parameterIndex int
	if m.state.IsLocked {
		// En mode lock, encodeur 0 -> paramètre 1, encodeur 1 -> paramètre 2, etc.
		parameterIndex = encoderIndex + 1
	} else {
		// En mode normal, calculer basé sur la page actuelle
		startParam := (m.state.CurrentPage - 1) * SlotsPerPage
		parameterIndex = startParam + encoderIndex + 1 // +1 pour éviter le paramètre 0
	}

	log.Printf("Device parameter encoder: encoderIndex=%d, parameterIndex=%d, delta=%f, locked=%v",
		encoderIndex, parameterIndex, delta, m.state.IsLocked)

	// Envoyer le message OSC pour ajuster le paramètre
	m.BaseMode.Send("/live/device/adjust/parameter", parameterIndex, delta)
}

// handleChainParameterEncoder gère les changements des encodeurs de paramètres de chain
func (m *LiveDeviceMode) handleChainParameterEncoder(encoderIndex int, delta float64) {
	// En mode chain, seuls les 4 premiers encodeurs sont utilisés
	if encoderIndex >= 4 {
		log.Printf("Encodeur %d non utilisé en mode chain", encoderIndex)
		return
	}

	log.Printf("Chain parameter encoder: encoderIndex=%d, delta=%f", encoderIndex, delta)

	// Convertir le chemin en JSON pour l'envoyer à Python
	pathJSON, err := json.Marshal(m.state.Path)
	if err != nil {
		log.Printf("Erreur lors de la conversion du chemin en JSON: %v", err)
		return
	}

	// Déterminer le type de paramètre et traiter selon le type
	switch encoderIndex {
	case 0: // Volume
		m.BaseMode.Send("/live/chain/adjust/parameter", string(pathJSON), 0, delta)
	case 1: // Pan
		m.BaseMode.Send("/live/chain/adjust/parameter", string(pathJSON), 1, delta)
	case 2: // Mute (paramètre quantifié - toggle)
		// Envoyer le delta directement, Python gérera le buffer avec seuil 0.5
		m.BaseMode.Send("/live/chain/adjust/parameter", string(pathJSON), 2, delta)
	case 3: // Solo (paramètre quantifié - toggle)
		// Envoyer le delta directement, Python gérera le buffer avec seuil 0.5
		m.BaseMode.Send("/live/chain/adjust/parameter", string(pathJSON), 3, delta)
	}
}

// handleMoveDevice gère le déplacement d'un device
func (m *LiveDeviceMode) handleMoveDevice(sourceIndex, targetIndex int) {
	var moveParams []interface{}

	if len(m.state.Path) == 0 {
		// Au niveau racine, on envoie simplement source et cible
		moveParams = []interface{}{sourceIndex, targetIndex}
	} else {
		if m.state.SubMode == SubmodeChain {
			// En mode chain, on ajoute l'index au path actuel
			sourcePath := append(m.state.Path, sourceIndex)
			targetPath := append(m.state.Path, targetIndex)

			// Combiner les chemins source et cible
			moveParams = make([]interface{}, len(sourcePath)+len(targetPath))
			for i, v := range sourcePath {
				moveParams[i] = v
			}
			for i, v := range targetPath {
				moveParams[len(sourcePath)+i] = v
			}
		} else { // Mode device
			// En mode device, on utilise le path actuel sans le dernier élément
			basePath := m.state.Path[:len(m.state.Path)-1]

			// Construire le chemin complet : basePath + source, basePath + target
			totalLen := len(basePath)*2 + 2 // *2 car on a le basePath deux fois, +2 pour source et target
			moveParams = make([]interface{}, totalLen)

			// Copier le premier basePath et ajouter sourceIndex
			for i, v := range basePath {
				moveParams[i] = v
			}
			moveParams[len(basePath)] = sourceIndex

			// Copier le second basePath et ajouter targetIndex
			for i, v := range basePath {
				moveParams[len(basePath)+1+i] = v
			}
			moveParams[totalLen-1] = targetIndex
		}
	}

	log.Printf("Sending move device command: params=%v, path=%v, submode=%d",
		moveParams, m.state.Path, m.state.SubMode)

	// Convertir les paramètres en JSON pour éviter les problèmes de parsing côté Python
	jsonParams, err := json.Marshal(moveParams)
	if err != nil {
		log.Printf("Erreur lors de la conversion des paramètres en JSON: %v", err)
		return
	}

	// Envoyer les paramètres en tant que JSON string
	m.BaseMode.Send("/live/device/move_by_path", string(jsonParams))
}

// handleSelectDevice gère la sélection d'un device
func (m *LiveDeviceMode) handleSelectDevice(index int) {
	if len(m.state.Path) > 0 && index == m.state.Path[0] {
		// Mettre à jour le chemin dans l'état avant d'envoyer le message
		m.state.Path = []int{index, 0}
		m.state.SubMode = len(m.state.Path) % 2
		m.BaseMode.Send("/live/select/chain", []interface{}{index, 0})
	} else {
		// Mettre à jour le chemin dans l'état avant d'envoyer le message
		m.state.Path = []int{index}
		m.state.SubMode = len(m.state.Path) % 2
		m.BaseMode.Send("/live/select/device", []interface{}{index})
	}
}

// handleRackDevice gère la sélection d'un device dans un rack
func (m *LiveDeviceMode) handleRackDevice(index int) {
	log.Printf("Current path for rd: %v", m.state.Path)

	// Cas 1: Si nous sommes en mode device et que l'index correspond au dernier élément du chemin
	// C'est le cas où on touche un rack déjà sélectionné -> on entre dans le rack
	if len(m.state.Path) > 0 && m.state.SubMode == SubmodeDevice && index == m.state.Path[len(m.state.Path)-1] {
		// Mode device et l'index correspond au dernier élément (rack déjà sélectionné)
		// On entre dans le rack en sélectionnant la première chaîne
		newPath := append(m.state.Path, 0)
		log.Printf("rd: sending chain path (entering rack): %v", newPath)

		// Mettre à jour le chemin dans l'état avant d'envoyer le message
		m.state.Path = newPath
		m.state.SubMode = len(m.state.Path) % 2

		// Convertir []int en []interface{}
		params := make([]interface{}, len(newPath))
		for i, v := range newPath {
			params[i] = v
		}
		m.BaseMode.Send("/live/select/chain", params)
		return
	}

	// Cas 2: Si nous sommes déjà dans un rack (en mode chain) et l'index correspond au device
	// directement parent (dernier device dans le chemin)
	if len(m.state.Path) > 1 && m.state.SubMode == SubmodeChain {
		if index == m.state.Path[len(m.state.Path)-2] {
			// Nous sommes déjà dans ce rack, donc nous restons sur la même chaîne
			// mais basculons en mode device (pour voir les devices de la chaîne)
			log.Printf("rd: already in this rack, switching to device mode")

			// Ajouter un niveau supplémentaire au chemin pour entrer dans le device
			newPath := append(m.state.Path, 0)

			// Mettre à jour le chemin dans l'état
			m.state.Path = newPath
			m.state.SubMode = len(m.state.Path) % 2

			params := make([]interface{}, len(m.state.Path))
			for i, v := range m.state.Path {
				params[i] = v
			}
			m.BaseMode.Send("/live/select/device", params)
			return
		}
	}

	// Cas standard: sélectionner un autre device
	var newPath []int
	if m.state.SubMode == SubmodeChain {
		// En mode chain, on ajoute l'index au path actuel
		newPath = append(m.state.Path, index)
	} else if len(m.state.Path) > 0 {
		// En mode device, on remplace le dernier élément par l'index
		newPath = append(m.state.Path[:len(m.state.Path)-1], index)
	} else {
		// Si le chemin est vide, créer un nouveau chemin avec juste l'index
		newPath = []int{index}
	}

	log.Printf("rd: sending path (%d mode): %v", m.state.SubMode, newPath)

	// Mettre à jour le chemin dans l'état avant d'envoyer le message
	m.state.Path = newPath
	m.state.SubMode = len(m.state.Path) % 2

	// Convertir []int en []interface{}
	params := make([]interface{}, len(newPath))
	for i, v := range newPath {
		params[i] = v
	}
	m.BaseMode.Send("/live/select/device", params)
}

// handleRackChain gère la sélection d'une chaîne dans un rack
func (m *LiveDeviceMode) handleRackChain(index int) {
	log.Printf("Current path for rc: %v", m.state.Path)

	if len(m.state.Path) == 0 {
		log.Printf("rc: path is empty, cannot select chain")
		return
	}

	var newPath []int
	if m.state.SubMode == SubmodeChain && len(m.state.Path) > 0 {
		newPath = append(m.state.Path[:len(m.state.Path)-1], index)
	} else if m.state.SubMode == SubmodeDevice && len(m.state.Path) > 1 {
		newPath = append(m.state.Path[:len(m.state.Path)-2], index)
	} else {
		// Cas de fallback si le path est trop court
		newPath = []int{index}
	}

	log.Printf("rc: sending path (%d mode): %v", m.state.SubMode, newPath)

	// Mettre à jour le chemin dans l'état avant d'envoyer le message
	m.state.Path = newPath
	m.state.SubMode = len(m.state.Path) % 2

	// Convertir []int en []interface{}
	params := make([]interface{}, len(newPath))
	for i, v := range newPath {
		params[i] = v
	}
	m.BaseMode.Send("/live/select/chain", params)
}

// handleDrumPad gère la sélection d'un pad de batterie
func (m *LiveDeviceMode) handleDrumPad(index int) {
	if len(m.state.Path) == 0 {
		log.Printf("dp: path is empty, cannot select drum pad")
		return
	}

	var newPath []int
	if m.state.SubMode == SubmodeChain && len(m.state.Path) > 0 {
		newPath = append(m.state.Path[:len(m.state.Path)-1], index)
	} else if m.state.SubMode == SubmodeDevice && len(m.state.Path) > 1 {
		newPath = append(m.state.Path[:len(m.state.Path)-2], index)
	} else {
		// Cas de fallback si le path est trop court
		newPath = []int{index}
	}

	log.Printf("dp: sending path (%d mode): %v", m.state.SubMode, newPath)

	// Mettre à jour le chemin dans l'état avant d'envoyer le message
	m.state.Path = newPath
	m.state.SubMode = len(m.state.Path) % 2

	// Convertir []int en []interface{}
	params := make([]interface{}, len(newPath))
	for i, v := range newPath {
		params[i] = v
	}
	m.BaseMode.Send("/live/select/chain_by_note", params)
}

// handleBack gère le retour en arrière dans la hiérarchie
func (m *LiveDeviceMode) handleBack() {
	log.Printf("Current path for back: %v", m.state.Path)

	if len(m.state.Path) == 0 {
		log.Printf("Back: path is empty, nothing to do")
		return
	}

	var newPath []int

	if m.state.SubMode == SubmodeChain && len(m.state.Path) > 0 {
		// En mode chain, on enlève le dernier élément
		newPath = m.state.Path[:len(m.state.Path)-1]
	} else if m.state.SubMode == SubmodeDevice && len(m.state.Path) > 1 {
		// En mode device, on enlève les deux derniers éléments
		newPath = m.state.Path[:len(m.state.Path)-2]
	} else {
		// Si le path est trop court, on le vide
		newPath = []int{}
	}

	log.Printf("Back: sending path: %v", newPath)

	// Mettre à jour le chemin dans l'état avant d'envoyer le message
	m.state.Path = newPath
	m.state.SubMode = len(m.state.Path) % 2

	// Convertir []int en []interface{}
	params := make([]interface{}, len(newPath))
	for i, v := range newPath {
		params[i] = v
	}
	m.BaseMode.Send("/live/select/device", params)
}

// handleLockDevice gère le verrouillage/déverrouillage du device
func (m *LiveDeviceMode) handleLockDevice(index int) {
	if index == 1 {
		m.EnableLock()
	} else if index == 0 {
		m.DisableLock()
	}
}

// handleToggleDeviceActive gère l'activation/désactivation du device
func (m *LiveDeviceMode) handleToggleDeviceActive() {
	deviceIndex := -3
	if m.state.IsLocked {
		deviceIndex = -4
	}
	m.BaseMode.Send("/live/device/set/isActiveToggle", []interface{}{deviceIndex})
}

// handleSwitchMode gère le changement de mode
func (m *LiveDeviceMode) handleSwitchMode(index int) {
	var mode string
	switch index {
	case 1:
		mode = "volume"
	case 0:
		mode = "track"
	case 3:
		mode = "learn"
	case 4:
		mode = "browser"
	}
	if mode != "" {
		log.Printf("Mode %s", mode)
		// Utiliser Emit au lieu de Send pour être cohérent avec le code JavaScript
		m.BaseMode.Emit("modeChange", mode)
	}
}

// handleLearnMode gère l'entrée en mode learn
func (m *LiveDeviceMode) handleLearnMode(index int) {
	if m.state.SubMode == SubmodeDevice {
		// Mode device - comportement actuel
		// Calcul correct de l'index du paramètre avec l'offset de 1
		paramIndex := index + 1 + ((m.state.CurrentPage - 1) * SlotsPerPage)

		// Déterminer l'index du device en fonction du verrouillage
		var deviceIndex int
		if m.state.IsLocked {
			deviceIndex = -4 // Device verrouillé
		} else {
			deviceIndex = -3 // Device sélectionné
			// Utiliser l'index réel du device si disponible
			selectedDevice := m.trackManager.GetSelectedDevice()
			if selectedDevice != nil {
				deviceIndex = *selectedDevice
			}
		}

		// Récupérer les informations du paramètre
		paramInfo := m.parameterManager.GetParameterInfo(paramIndex, m.state.IsLocked)

		// Créer les données d'apprentissage
		learnData := &types.LearnData{
			ParamType:   types.ParamTypeDevice,
			TrackIndex:  -2, // Piste sélectionnée
			DeviceIndex: &deviceIndex,
			ParamIndex:  &paramIndex,
		}

		// Ajouter les informations supplémentaires si disponibles
		if paramInfo != nil {
			// Convertir les valeurs en pointeurs pour les ajouter à LearnData
			isQuantized := paramInfo.IsQuantized
			minValue := paramInfo.MinValue
			maxValue := paramInfo.MaxValue

			// Ajouter ces informations au learnData
			learnData.IsQuantized = &isQuantized
			learnData.MinValue = &minValue
			learnData.MaxValue = &maxValue

			log.Printf("Mode learn (device): paramIndex=%d, deviceIndex=%d, isQuantized=%v, minValue=%f, maxValue=%f",
				paramIndex, deviceIndex, isQuantized, minValue, maxValue)
		} else {
			log.Printf("Mode learn (device): paramIndex=%d, deviceIndex=%d (pas d'infos supplémentaires)",
				paramIndex, deviceIndex)
		}

		// Mettre à jour les données d'apprentissage dans le mode manager
		if service := m.BaseMode.GetService(); service != nil {
			if modeManager, ok := service.(*live.LiveModeManager); ok {
				modeManager.SetLearnData(learnData)
				log.Printf("Mode learn (device): learnData défini avec succès")
				m.BaseMode.Emit("modeChange", "learn")
			} else {
				log.Println("Mode Device: Impossible de caster le service en LiveModeManager")
			}
		} else {
			log.Println("Mode Device: Service non disponible pour définir les données d'apprentissage")
		}

	} else if m.state.SubMode == SubmodeChain {
		var paramType int
		switch index {
		case 0:
			paramType = types.ParamTypeChainVolume // volume
		case 1:
			paramType = types.ParamTypeChainPan // pan
		case 2:
			paramType = types.ParamTypeChainMute // mute
		case 3:
			paramType = types.ParamTypeChainSolo // solo
		}

		if paramType > 0 {
			// Créer les données d'apprentissage
			learnData := &types.LearnData{
				ParamType:  paramType,
				TrackIndex: -2, // Piste sélectionnée
				ChainPath:  m.state.Path,
			}

			log.Printf("Mode learn (chain): paramType=%d, path=%v", paramType, m.state.Path)

			// Mettre à jour les données d'apprentissage dans le mode manager
			if service := m.BaseMode.GetService(); service != nil {
				if modeManager, ok := service.(*live.LiveModeManager); ok {
					modeManager.SetLearnData(learnData)
					log.Printf("Mode learn (chain): learnData défini avec succès")
					m.BaseMode.Emit("modeChange", "learn")
				} else {
					log.Println("Mode Device: Impossible de caster le service en LiveModeManager")
				}
			} else {
				log.Println("Mode Device: Service non disponible pour définir les données d'apprentissage")
			}
		}
	}
}

// handleToggleDeviceOnOff gère l'activation/désactivation d'un device par son chemin
func (m *LiveDeviceMode) handleToggleDeviceOnOff(index int) {
	var newPath []int

	if m.state.SubMode == SubmodeChain {
		// En mode chain, on ajoute l'index au path actuel
		newPath = append(m.state.Path, index)
	} else if len(m.state.Path) > 0 {
		// En mode device, on remplace le dernier élément par l'index
		newPath = append(m.state.Path[:len(m.state.Path)-1], index)
	} else {
		// Si le path est vide, on crée un nouveau path avec juste l'index
		newPath = []int{index}
	}

	log.Printf("on: sending path: %v", newPath)

	// Convertir le chemin en JSON pour éviter les problèmes de parsing côté Python
	jsonPath, err := json.Marshal(newPath)
	if err != nil {
		log.Printf("Erreur lors de la conversion du chemin en JSON: %v", err)
		return
	}

	// Envoyer le chemin en tant que JSON string
	m.BaseMode.Send("/live/device/set/isActiveTogByPath", string(jsonPath))
}

// handleDeleteDevice gère la suppression d'un device
func (m *LiveDeviceMode) handleDeleteDevice(index int) {
	var newPath []int

	if len(m.state.Path) == 0 {
		// Au niveau racine, on envoie simplement l'index
		newPath = []int{index}
	} else {
		if m.state.SubMode == SubmodeChain {
			// En mode chain, on ajoute l'index au path actuel
			newPath = append(m.state.Path, index)
		} else {
			// En mode device, on remplace le dernier élément par l'index
			newPath = append(m.state.Path[:len(m.state.Path)-1], index)
		}
	}

	log.Printf("Sending delete device command: path=%v, submode=%d",
		newPath, m.state.SubMode)

	// Convertir le chemin en JSON pour éviter les problèmes de parsing côté Python
	jsonPath, err := json.Marshal(newPath)
	if err != nil {
		log.Printf("Erreur lors de la conversion du chemin en JSON: %v", err)
		return
	}

	// Envoyer le chemin en tant que JSON string
	m.BaseMode.Send("/live/device/delete_by_path", string(jsonPath))
}

// handleHotswap gère l'activation du hotswap
func (m *LiveDeviceMode) handleHotswap(index int) {
	var newPath []int

	// Le code Python attend un chemin avec une longueur impaire:
	// [rack_index, chain_index, ..., device_index]
	// Nous devons nous assurer que le chemin est correctement formaté

	if m.state.SubMode == SubmodeChain {
		// En mode chain, on ajoute l'index au path actuel
		// Si le path actuel a une longueur paire, on doit ajouter un élément pour avoir une longueur impaire
		if len(m.state.Path)%2 == 0 {
			// Ajouter un index de chaîne (0 par défaut) pour avoir un format valide
			newPath = append(m.state.Path, 0, index)
		} else {
			// Le path a déjà une longueur impaire, on peut simplement ajouter l'index
			newPath = append(m.state.Path, index)
		}
	} else if len(m.state.Path) > 0 {
		// En mode device, on remplace le dernier élément par l'index
		// On doit s'assurer que le résultat a une longueur impaire
		if len(m.state.Path)%2 == 0 {
			// Si le path a une longueur paire, on doit s'assurer que le résultat est impair
			// On enlève le dernier élément et on ajoute l'index
			newPath = append(m.state.Path[:len(m.state.Path)-1], index)
		} else {
			// Si le path a une longueur impaire, on doit s'assurer que le résultat reste impair
			// On remplace le dernier élément par l'index
			newPath = append(m.state.Path[:len(m.state.Path)-1], index)
			// Note: les deux branches font la même chose, mais sont conservées pour la clarté
			// et pour faciliter des modifications futures si nécessaire
		}
	} else {
		// Si le path est vide, on crée un nouveau path avec juste l'index
		// Un seul index est valide car c'est un device au niveau racine
		newPath = []int{index}
	}

	// Vérifier que le chemin a une longueur impaire comme attendu par le code Python
	if len(newPath)%2 == 0 {
		log.Printf("Attention: Le chemin pour le hotswap a une longueur paire (%d), ce qui peut causer des problèmes", len(newPath))
	}

	log.Printf("ho: sending path for hotswap: %v", newPath)

	// Convertir []int en []interface{} pour l'envoi OSC
	params := make([]interface{}, len(newPath))
	for i, v := range newPath {
		params[i] = v
	}

	// Envoyer les paramètres individuellement
	m.BaseMode.Send("/live/browser/hotswap/enable", params...)
	// Utiliser Emit au lieu de Send pour être cohérent avec le code JavaScript
	m.BaseMode.Emit("modeChange", "browser")
}

// handleToggleParameter gère le basculement des paramètres
func (m *LiveDeviceMode) handleToggleParameter(index int) {
	if m.state.SubMode == SubmodeDevice {
		// Mode device : envoi du paramètre
		paramIndex := index + 1 + ((m.state.CurrentPage - 1) * SlotsPerPage)
		if m.parameterManager != nil {
			nextValue := m.parameterManager.ToggleQuantizedValue(paramIndex)
			if nextValue != -1 {
				log.Printf("Toggle parameter: path=%v, submode=%d, paramIndex=%d, nextValue=%d",
					m.state.Path, m.state.SubMode, paramIndex, nextValue)

				// Envoyer path, paramIndex, nextValue
				// Le chemin est envoyé comme premier argument en JSON
				var pathToSend []int
				if len(m.state.Path) == 0 {
					// Si le chemin est vide, on utilise [0] comme chemin par défaut
					pathToSend = []int{0}
				} else {
					pathToSend = m.state.Path
				}

				// Convertir le chemin en JSON pour éviter les problèmes de parsing côté Python
				jsonPath, err := json.Marshal(pathToSend)
				if err != nil {
					log.Printf("Erreur lors de la conversion du chemin en JSON: %v", err)
					return
				}

				// Envoyer le chemin en tant que JSON string
				m.BaseMode.Send("/live/device/set/parameter/value", string(jsonPath), paramIndex, nextValue)
			}
		}
	} else if m.state.SubMode == SubmodeChain && index <= 3 {
		// Mode chain - comportement inchangé
		if m.parameterManager != nil {
			pathParams := make([]interface{}, len(m.state.Path))
			for i, v := range m.state.Path {
				pathParams[i] = v
			}
			m.parameterManager.SendChainParameterUpdate(pathParams, index)
		}
	}
}

// pageUp passe à la page suivante des paramètres
func (m *LiveDeviceMode) pageUp() {
	log.Printf("pageUp: Tentative de passage à la page suivante (page actuelle: %d)", m.state.CurrentPage)
	if m.parameterManager != nil {
		parametersCount := m.trackManager.GetParametersCount()
		log.Printf("pageUp: parametersCount=%d, SlotsPerPage=%d", parametersCount, SlotsPerPage)
		if m.parameterManager.SetPage(m.state.CurrentPage+1, parametersCount, SlotsPerPage, m.state.IsLocked) {
			m.state.CurrentPage++
			totalPages := (parametersCount + SlotsPerPage - 1) / SlotsPerPage
			log.Printf("pageUp: Switched to page %d/%d", m.state.CurrentPage, totalPages)
			m.commManager.SendMessage(fmt.Sprintf("pd%d/%d", m.state.CurrentPage, totalPages), m.isActive)
		} else {
			log.Printf("pageUp: Impossible de passer à la page %d", m.state.CurrentPage+1)
		}
	} else {
		log.Printf("pageUp: parameterManager est nil")
	}
}

// pageDown passe à la page précédente des paramètres
func (m *LiveDeviceMode) pageDown() {
	log.Printf("pageDown: Tentative de passage à la page précédente (page actuelle: %d)", m.state.CurrentPage)
	if m.parameterManager != nil {
		parametersCount := m.trackManager.GetParametersCount()
		log.Printf("pageDown: parametersCount=%d, SlotsPerPage=%d", parametersCount, SlotsPerPage)
		if m.parameterManager.SetPage(m.state.CurrentPage-1, parametersCount, SlotsPerPage, m.state.IsLocked) {
			m.state.CurrentPage--
			totalPages := (parametersCount + SlotsPerPage - 1) / SlotsPerPage
			log.Printf("pageDown: Switched to page %d/%d", m.state.CurrentPage, totalPages)
			m.commManager.SendMessage(fmt.Sprintf("pd%d/%d", m.state.CurrentPage, totalPages), m.isActive)
		} else {
			log.Printf("pageDown: Impossible de passer à la page %d", m.state.CurrentPage-1)
		}
	} else {
		log.Printf("pageDown: parameterManager est nil")
	}
}
