package livedevicemode

import (
	"fmt"
	"log"
)

// registerOSCHandlers enregistre tous les handlers OSC pour le mode device
func (m *LiveDeviceMode) registerOSCHandlers() {
	// Handlers pour les paramètres
	m.backgroundHandlers["/live/device/get/parameter/value"] = m.handleParameterValue
	m.backgroundHandlers["/live/device/get/isActive"] = m.handleIsActive
	m.backgroundHandlers["/live/deviceMode/get/parameters/bulk"] = m.handleBulkParameters
	m.backgroundHandlers["/live/chain/get/parameters/bulk"] = m.handleBulkChainParameters
	m.backgroundHandlers["/live/track/get/devices/name"] = m.handleDevicesName

	// Handlers pour les propriétés du device
	m.backgroundHandlers["/live/device_unlock"] = m.handleDeviceUnlock
	m.backgroundHandlers["/live/device_lock_reference"] = m.handleDeviceLockReference
	m.backgroundHandlers["/live/device/get/is_collapsed"] = m.handleIsCollapsed
	m.backgroundHandlers["/live/device/get/can_have_chains"] = m.handleCanHaveChains
	m.backgroundHandlers["/live/deviceMode/get/environment/"] = m.handleEnvironment
	m.backgroundHandlers["/live/deviceMode/get/path_string"] = m.handlePathString
	m.backgroundHandlers["/live/deviceMode/selTrack/properties"] = m.handleSelTrackProperties

	// Handlers pour les propriétés de la chaîne
	m.backgroundHandlers["/live/chain/get/volume"] = m.handleChainVolume
	m.backgroundHandlers["/live/chain/get/pan"] = m.handleChainPan
	m.backgroundHandlers["/live/chain/get/mute"] = m.handleChainMute
	m.backgroundHandlers["/live/chain/get/solo"] = m.handleChainSolo

	// Enregistrer tous les handlers background avec leurs descriptions
	handlerDescriptions := map[string]string{
		"/live/device/get/parameter/value":     "Gère les valeurs des paramètres du device",
		"/live/device/get/isActive":            "Gère l'état actif du device",
		"/live/deviceMode/get/parameters/bulk": "Gère les paramètres groupés du device",
		"/live/chain/get/parameters/bulk":      "Gère les paramètres groupés de la chaîne",
		"/live/device_unlock":                  "Gère le déverrouillage du device",
		"/live/device_lock_reference":          "Gère la référence de verrouillage du device",
		"/live/device/get/is_collapsed":        "Gère l'état replié du device",
		"/live/device/get/can_have_chains":     "Gère la capacité à avoir des chaînes",
		"/live/deviceMode/get/environment/":    "Gère l'environnement du device",
		"/live/deviceMode/get/path_string":     "Gère le chemin du device",
		"/live/chain/get/volume":               "Gère le volume de la chaîne",
		"/live/chain/get/pan":                  "Gère le panoramique de la chaîne",
		"/live/chain/get/mute":                 "Gère le mute de la chaîne",
		"/live/chain/get/solo":                 "Gère le solo de la chaîne",
		"/live/deviceMode/selTrack/properties": "Gère les propriétés de la piste sélectionnée",
	}

	for address, handler := range m.backgroundHandlers {
		description := handlerDescriptions[address]
		m.BaseMode.RegisterHandler(address, handler, description)
	}
}

// handleParameterValue gère les messages de valeur de paramètre
func (m *LiveDeviceMode) handleParameterValue(args []interface{}) {
	if m.parameterManager != nil {
		// Appel avec tous les paramètres requis
		m.parameterManager.HandleParameterMessage("/live/device/get/parameter/value", args, m.state.CurrentPage, SlotsPerPage, m.state.IsLocked)
	}
}

// handleBulkParameters gère les messages de paramètres groupés
func (m *LiveDeviceMode) handleBulkParameters(args []interface{}) {
	if m.parameterManager != nil {
		m.parameterManager.HandleBulkDeviceParameters(args)
	}
}

// handleBulkChainParameters gère les messages de paramètres de chaîne groupés
func (m *LiveDeviceMode) handleBulkChainParameters(args []interface{}) {
	log.Printf("handleBulkChainParameters: Arguments bruts: %v", args)

	if len(args) < 2 {
		log.Printf("handleBulkChainParameters: arguments insuffisants, besoin d'au moins 2 arguments")
		return
	}

	// Vérifier si nous avons des tableaux imbriqués
	// Format attendu: [[chainIndices...], [volume, pan, mute, solo]]
	// chainIndices, okChainIndices := args[0].([]interface{}) // Supprimé car non utilisé ici
	_, okChainIndices := args[0].([]interface{}) // Vérifier le type sans assigner
	paramValues, okParamValues := args[1].([]interface{})

	if !okChainIndices || !okParamValues {
		log.Printf("handleBulkChainParameters: format d'arguments incorrect, tableaux attendus")

		// Si ce n'est pas le bon format, essayer de déléguer au parameterManager
		if m.parameterManager != nil {
			m.parameterManager.HandleBulkChainParameters(args)
		}
		return
	}

	// Vérifier que nous avons au moins 4 valeurs de paramètres (volume, pan, mute, solo)
	if len(paramValues) < 4 {
		log.Printf("handleBulkChainParameters: pas assez de valeurs de paramètres: %d", len(paramValues))
		log.Printf("handleBulkChainParameters: valeurs reçues: %v", paramValues)

		// Même avec moins de 4 valeurs, on essaie de traiter ce qu'on a
		if m.parameterManager != nil {
			m.parameterManager.HandleBulkChainParameters(args)
		}
		return
	}

	// Déléguer au parameterManager pour le traitement
	if m.parameterManager != nil {
		m.parameterManager.HandleBulkChainParameters(args)
	}
}

// handleDeviceUnlock gère le déverrouillage du device
func (m *LiveDeviceMode) handleDeviceUnlock(args []interface{}) {
	m.DisableLock()
	log.Println("Device déverrouillé")
}

// handleDeviceLockReference gère la référence de verrouillage du device
func (m *LiveDeviceMode) handleDeviceLockReference(args []interface{}) {
	if len(args) < 3 {
		log.Printf("handleDeviceLockReference: arguments insuffisants, besoin d'au moins 3 arguments")
		return
	}

	log.Printf("handleDeviceLockReference: Arguments reçus: %v", args)

	deviceName, ok1 := args[0].(string)
	trackName, ok2 := args[1].(string)

	// Le troisième argument peut être un entier ou une chaîne
	var trackColorStr string

	// Essayer de convertir le trackColor en fonction de son type
	switch colorVal := args[2].(type) {
	case string:
		trackColorStr = colorVal
	case int:
		trackColorStr = fmt.Sprintf("%d", colorVal)
	case int32:
		trackColorStr = fmt.Sprintf("%d", colorVal)
	case int64:
		trackColorStr = fmt.Sprintf("%d", colorVal)
	case float32:
		trackColorStr = fmt.Sprintf("%d", int(colorVal))
	case float64:
		trackColorStr = fmt.Sprintf("%d", int(colorVal))
	default:
		log.Printf("handleDeviceLockReference: type de trackColor non géré: %T", args[2])
		return
	}

	if !ok1 || !ok2 || trackColorStr == "" {
		log.Printf("handleDeviceLockReference: erreur de conversion des arguments")
		return
	}

	log.Printf("handleDeviceLockReference: Envoi des messages ld et dp avec deviceName=%s, trackName=%s, trackColor=%s",
		deviceName, trackName, trackColorStr)

	m.commManager.SendMessage("ld"+deviceName, m.isActive)
	m.commManager.SendMessage("dp,"+trackName+","+trackColorStr, m.isActive)
}

// handleIsActive gère les messages d'état actif du device
func (m *LiveDeviceMode) handleIsActive(args []interface{}) {
	if m.propertyManager != nil {
		m.propertyManager.HandleIsActive(args)
	}
}

// handleEnvironment gère les messages d'environnement
func (m *LiveDeviceMode) handleEnvironment(args []interface{}) {
	if m.propertyManager != nil {
		m.propertyManager.HandleEnvironment(args)
	}
}

// handlePathString gère les messages de chemin
func (m *LiveDeviceMode) handlePathString(args []interface{}) {
	if m.propertyManager != nil {
		m.propertyManager.HandlePathString(args)
	}
}

// handleIsCollapsed gère les messages d'état replié du device
func (m *LiveDeviceMode) handleIsCollapsed(args []interface{}) {
	if m.propertyManager != nil {
		m.propertyManager.HandleIsCollapsed(args)
	}
}

// handleCanHaveChains gère les messages de capacité à avoir des chaînes
func (m *LiveDeviceMode) handleCanHaveChains(args []interface{}) {
	if m.propertyManager != nil {
		m.propertyManager.HandleCanHaveChains(args)
	}
}

// Handlers pour les paramètres de chaîne
func (m *LiveDeviceMode) handleChainVolume(args []interface{}) {
	if m.parameterManager != nil {
		m.parameterManager.HandleChainParameterMessage("volume", args)
	}
}

func (m *LiveDeviceMode) handleChainPan(args []interface{}) {
	if m.parameterManager != nil {
		m.parameterManager.HandleChainParameterMessage("pan", args)
	}
}

func (m *LiveDeviceMode) handleChainMute(args []interface{}) {
	if m.parameterManager != nil {
		m.parameterManager.HandleChainParameterMessage("mute", args)
	}
}

func (m *LiveDeviceMode) handleChainSolo(args []interface{}) {
	if m.parameterManager != nil {
		m.parameterManager.HandleChainParameterMessage("solo", args)
	}
}

// handleSelTrackProperties gère les propriétés de la piste sélectionnée
func (m *LiveDeviceMode) handleSelTrackProperties(args []interface{}) {
	if m.propertyManager != nil {
		m.propertyManager.HandleSelTrackProperties(args)
	}
}

// handleDevicesName gère les noms des devices
func (m *LiveDeviceMode) handleDevicesName(args []interface{}) {
	if m.propertyManager != nil {
		m.propertyManager.HandleDevicesName(args)
	}
}
