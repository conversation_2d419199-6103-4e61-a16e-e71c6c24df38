package livedevicemode

import (
	"encoding/json"
	"fmt"
	"log"
	"math"
	communication "oscbridge/Communication"
	"oscbridge/Live/utils"
	"strings"
	"sync"
	"time"
)

// ParameterInfo contient les informations sur un paramètre de device
type ParameterInfo struct {
	Name         string
	IsQuantized  bool
	MinValue     float64
	MaxValue     float64
	CurrentValue float64
	ValueString  string
}

// DeviceParameterManager gère l'affichage et la mise à jour des paramètres de device
type DeviceParameterManager struct {
	commManager         *communication.CommunicationManager
	baseMode            *communication.BaseMode
	state               *LiveDeviceModeState
	volumeConverter     *utils.VolumeConverter
	parameterInfo       map[int]*ParameterInfo
	parameterInfoLocked map[int]*ParameterInfo
	messageQueue        []string
	isProcessingQueue   bool
	messageDelay        time.Duration
	parentMode          *LiveDeviceMode

	// Pour la gestion des pages
	isFirstPageChange  bool
	lastPageChangeTime time.Time
	pageChangeTimeout  *time.Timer

	// Mutex pour protéger les accès concurrents
	mutex sync.Mutex
}

// Fonctions utilitaires pour la conversion de types
func toInt(v interface{}) (int, bool) {
	switch val := v.(type) {
	case int32:
		return int(val), true
	case float32:
		return int(val), true
	case float64:
		return int(val), true
	case int:
		return val, true
	default:
		log.Printf("Type non géré pour la conversion en int: %T", v)
		return 0, false
	}
}

func toFloat64(v interface{}) (float64, bool) {
	switch val := v.(type) {
	case float32:
		return float64(val), true
	case float64:
		return val, true
	case int32:
		return float64(val), true
	case int:
		return float64(val), true
	default:
		log.Printf("Type non géré pour la conversion en float64: %T", v)
		return 0, false
	}
}

// NewDeviceParameterManager crée une nouvelle instance de DeviceParameterManager
func NewDeviceParameterManager(commManager *communication.CommunicationManager, baseMode *communication.BaseMode, state *LiveDeviceModeState) *DeviceParameterManager {
	return &DeviceParameterManager{
		commManager:         commManager,
		baseMode:            baseMode,
		state:               state,
		volumeConverter:     utils.NewVolumeConverter(),
		parameterInfo:       make(map[int]*ParameterInfo),
		parameterInfoLocked: make(map[int]*ParameterInfo),
		messageQueue:        make([]string, 0),
		messageDelay:        3 * time.Millisecond,
		isFirstPageChange:   true,
	}
}

// SetParentMode définit le mode parent
func (m *DeviceParameterManager) SetParentMode(mode *LiveDeviceMode) {
	m.parentMode = mode
}

// QueueMessage ajoute un message à la file d'attente s'il n'y est pas déjà
func (m *DeviceParameterManager) QueueMessage(message string) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// Vérifier si le message est déjà dans la queue
	for _, msg := range m.messageQueue {
		if msg == message {
			return
		}
	}

	m.messageQueue = append(m.messageQueue, message)

	if !m.isProcessingQueue {
		go m.processMessageQueue()
	}
}

// processMessageQueue traite les messages dans la file d'attente
func (m *DeviceParameterManager) processMessageQueue() {
	m.mutex.Lock()
	if len(m.messageQueue) == 0 {
		m.isProcessingQueue = false
		m.mutex.Unlock()
		return
	}

	message := m.messageQueue[0]
	m.messageQueue = m.messageQueue[1:]
	m.isProcessingQueue = true
	m.mutex.Unlock()

	isActive := m.parentMode.IsActive()
	log.Printf("[DEBUG] Envoi du message: %s (mode actif: %v)", message, isActive)
	m.commManager.SendMessage(message, isActive)

	time.Sleep(m.messageDelay)
	m.processMessageQueue()
}

// HandleParameterMessage gère les messages de paramètres reçus
func (m *DeviceParameterManager) HandleParameterMessage(address string, args []interface{}, currentPage int, slotsPerPage int, isLocked bool) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if len(args) < 4 {
		log.Printf("HandleParameterMessage: pas assez d'arguments (%d)", len(args))
		return
	}

	trackIndex, ok1 := toInt(args[0])
	if !ok1 {
		log.Printf("HandleParameterMessage: erreur de conversion de trackIndex")
		return
	}

	paramIndex, ok2 := toInt(args[1])
	if !ok2 {
		log.Printf("HandleParameterMessage: erreur de conversion de paramIndex")
		return
	}

	value, ok3 := toFloat64(args[2])
	if !ok3 {
		log.Printf("HandleParameterMessage: erreur de conversion de value")
		return
	}

	valueString, ok4 := args[3].(string)
	if !ok4 {
		log.Printf("HandleParameterMessage: erreur de conversion de valueString")
		return
	}

	targetMap := m.parameterInfo
	if isLocked {
		targetMap = m.parameterInfoLocked
	}

	shouldSendMessage := (isLocked && trackIndex == -4) || (!isLocked && trackIndex == -3)

	if address == "/live/device/get/parameter/value" {
		log.Printf("HandleParameterMessage: trackIndex=%d, paramIndex=%d, value=%f, valueString=%s", trackIndex, paramIndex, value, valueString)

		if paramInfo, exists := targetMap[paramIndex]; exists {
			paramInfo.CurrentValue = value
			paramInfo.ValueString = valueString
			log.Printf("HandleParameterMessage: Paramètre trouvé dans la map, mise à jour effectuée")

			// On ignore le paramètre 0
			if paramIndex > 0 && shouldSendMessage {
				startParam := ((currentPage - 1) * slotsPerPage) + 1
				endParam := startParam + slotsPerPage

				if paramIndex >= startParam && paramIndex < endParam {
					slotIndex := paramIndex - startParam
					slotLetter := string(rune('A' + slotIndex))

					normalizedValue := math.Round(
						((value - paramInfo.MinValue) / (paramInfo.MaxValue - paramInfo.MinValue)) * 100,
					)
					formattedValue := fmt.Sprintf("%03.0f", normalizedValue)

					// Envoyer directement le message au lieu de le mettre en file d'attente
					message := fmt.Sprintf("pv,%s,%s,%s", slotLetter, formattedValue, valueString)
					log.Printf("HandleParameterMessage: Envoi direct du message: %s", message)
					m.commManager.SendMessage(message, true)
				} else {
					log.Printf("HandleParameterMessage: Paramètre %d hors de la plage d'affichage [%d-%d]", paramIndex, startParam, endParam-1)
				}
			}
		} else {
			log.Printf("HandleParameterMessage: Paramètre %d non trouvé dans la map", paramIndex)
		}
	}
}

// HandleBulkDeviceParameters gère les paramètres groupés du device
func (m *DeviceParameterManager) HandleBulkDeviceParameters(args []interface{}) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if len(args) < 3 {
		log.Printf("HandleBulkDeviceParameters: pas assez d'arguments (%d)", len(args))
		return
	}

	// Extraction des arguments de base
	trackIndex, ok1 := toInt(args[0])
	paramCount, ok2 := toInt(args[1])
	if !ok1 || !ok2 {
		log.Printf("HandleBulkDeviceParameters: types d'arguments invalides pour trackIndex ou paramCount")
		return
	}

	// Sélection de la map cible
	targetMap := m.parameterInfo
	if trackIndex == -4 {
		targetMap = m.parameterInfoLocked
	}

	// Effacer les données existantes
	for k := range targetMap {
		delete(targetMap, k)
	}

	// Traitement des données en masse
	bulkData := args[2:]
	for i := 0; i < paramCount; i++ {
		baseIndex := i * 7
		if baseIndex+6 >= len(bulkData) {
			log.Printf("HandleBulkDeviceParameters: index hors limites pour les données en masse: %d", baseIndex)
			break
		}

		paramIndex, ok := toInt(bulkData[baseIndex])
		if !ok {
			continue
		}

		name, ok := bulkData[baseIndex+1].(string)
		if !ok {
			continue
		}

		isQuantized, ok := bulkData[baseIndex+2].(bool)
		if !ok {
			continue
		}

		minValue, ok := toFloat64(bulkData[baseIndex+3])
		if !ok {
			continue
		}

		maxValue, ok := toFloat64(bulkData[baseIndex+4])
		if !ok {
			continue
		}

		currentValue, ok := toFloat64(bulkData[baseIndex+5])
		if !ok {
			continue
		}

		valueString, ok := bulkData[baseIndex+6].(string)
		if !ok {
			continue
		}

		targetMap[paramIndex] = &ParameterInfo{
			Name:         name,
			IsQuantized:  isQuantized,
			MinValue:     minValue,
			MaxValue:     maxValue,
			CurrentValue: currentValue,
			ValueString:  valueString,
		}
	}

	// Envoi des messages
	if trackIndex == -4 || (trackIndex == -3 && len(m.parameterInfoLocked) == 0) {
		param0Value := 0
		if param0Info, exists := targetMap[0]; exists && param0Info.CurrentValue == 1 {
			param0Value = 1
		}

		// Envoyer directement les messages critiques
		m.commManager.SendMessage(fmt.Sprintf("ia,%d", param0Value), true)
		m.commManager.SendMessage("pan0", true)

		// Construction du message nn (noms des paramètres) et di (valeurs)
		nnParts := []string{"nn"}
		diParts := []string{"di"}
		maxParams := 8

		// Parcours des paramètres pour construire les messages (ignorer le paramètre 0)
		for i := 1; i < paramCount && len(nnParts) <= maxParams; i++ {
			baseIndex := i * 7
			if baseIndex+6 >= len(bulkData) {
				continue
			}

			paramIndex, _ := toInt(bulkData[baseIndex])

			// Si le paramètre n'est pas à l'index attendu, le chercher
			if paramIndex != i {
				found := false
				for j := 0; j < paramCount; j++ {
					jBaseIndex := j * 7
					if jBaseIndex+6 >= len(bulkData) {
						continue
					}
					jParamIndex, ok := toInt(bulkData[jBaseIndex])
					if ok && jParamIndex == i {
						baseIndex = jBaseIndex
						found = true
						break
					}
				}

				// Si paramètre non trouvé, ajouter un espace vide
				if !found && len(nnParts) < maxParams+1 {
					nnParts = append(nnParts, " ")
					diParts = append(diParts, "0", "nul")
					continue
				}
			}

			// Traitement du nom
			name, ok := bulkData[baseIndex+1].(string)
			if !ok || name == "" {
				name = " "
			}

			// Ajout du nom avec puce si le paramètre est quantifié
			isQuantized, ok := bulkData[baseIndex+2].(bool)
			if ok && isQuantized {
				name = "|| " + name
			}

			// Ajouter le nom si dans la limite
			if len(nnParts) < maxParams+1 {
				nnParts = append(nnParts, name)
			} else {
				continue
			}

			// Récupération des valeurs
			currentValue, ok1 := toFloat64(bulkData[baseIndex+5])
			minValue, ok2 := toFloat64(bulkData[baseIndex+3])
			maxValue, ok3 := toFloat64(bulkData[baseIndex+4])
			valueString, ok4 := bulkData[baseIndex+6].(string)

			// Traitement des valeurs selon le paramètre
			if paramIndex == 1 && ok1 && ok4 { // Paramètre 1 (Mode)
				normalizedValue := math.Round(((currentValue - minValue) / (maxValue - minValue)) * 100)
				diParts = append(diParts, fmt.Sprintf("%.0f", normalizedValue), valueString)
			} else { // Autres paramètres
				// Valeur normalisée
				if ok1 && ok2 && ok3 {
					normalizedValue := math.Round(((currentValue - minValue) / (maxValue - minValue)) * 100)
					diParts = append(diParts, fmt.Sprintf("%.0f", normalizedValue))
				} else {
					diParts = append(diParts, "0")
				}

				// Valeur textuelle
				if ok4 && valueString != "" {
					diParts = append(diParts, valueString)
				} else {
					diParts = append(diParts, "nul")
				}
			}
		}

		// Compléter les messages avec des espaces vides si nécessaire
		for len(nnParts) < 9 { // 8 paramètres + le mot "nn"
			nnParts = append(nnParts, " ")
		}
		for len(diParts) < 17 { // 8 paramètres * 2 (valeur + texte) + le mot "di"
			diParts = append(diParts, "0", "nul")
		}

		// Construire et envoyer les messages
		nnMessage := strings.Join(nnParts, ",")
		diMessage := strings.Join(diParts, ",")
		totalPages := (paramCount + 8 - 1) / 8

		// Envoyer directement les messages - FORCER isActive à true
		m.commManager.SendMessage(nnMessage, true)
		m.commManager.SendMessage(diMessage, true)
		m.commManager.SendMessage(fmt.Sprintf("pd%d/%d", 1, totalPages), true)
	}
}

// SetPage met à jour l'affichage pour la page spécifiée
func (m *DeviceParameterManager) SetPage(page, parametersCount, slotsPerPage int, isLocked bool) bool {
	log.Printf("SetPage: Début avec page=%d, parametersCount=%d, slotsPerPage=%d, isLocked=%v",
		page, parametersCount, slotsPerPage, isLocked)

	m.mutex.Lock()
	defer m.mutex.Unlock()

	maxPage := int(math.Ceil(float64(parametersCount-1) / float64(slotsPerPage)))
	log.Printf("SetPage: maxPage calculé = %d", maxPage)

	targetMap := m.parameterInfo
	if isLocked {
		targetMap = m.parameterInfoLocked
	}
	log.Printf("SetPage: Taille de la map cible = %d", len(targetMap))

	if page >= 1 && page <= maxPage {
		currentTime := time.Now()

		// Nettoyer le timeout existant s'il y en a un
		if m.pageChangeTimeout != nil {
			m.pageChangeTimeout.Stop()
		}

		// Si c'est le premier changement de page ou si plus de 200ms se sont écoulées
		if m.isFirstPageChange || currentTime.Sub(m.lastPageChangeTime) > 200*time.Millisecond {
			// Appeler directement completePageUpdate (le mutex est déjà verrouillé)
			m.completePageUpdate(page, targetMap, slotsPerPage)
			m.isFirstPageChange = false
		} else {
			// Créer une copie des données nécessaires pour la mise à jour différée
			// afin d'éviter les problèmes de concurrence
			targetMapCopy := make(map[int]*ParameterInfo)
			for k, v := range targetMap {
				targetMapCopy[k] = &ParameterInfo{
					Name:         v.Name,
					IsQuantized:  v.IsQuantized,
					MinValue:     v.MinValue,
					MaxValue:     v.MaxValue,
					CurrentValue: v.CurrentValue,
					ValueString:  v.ValueString,
				}
			}

			// Programmer la mise à jour complète après 150ms
			m.pageChangeTimeout = time.AfterFunc(150*time.Millisecond, func() {
				// Acquérir le mutex dans la goroutine
				m.mutex.Lock()
				defer m.mutex.Unlock()

				// Préparer les messages
				startParam := ((page - 1) * slotsPerPage) + 1
				diParts := []string{"di"}
				nnParts := []string{"nn"}

				for i := 0; i < slotsPerPage; i++ {
					paramIndex := startParam + i
					paramInfo, exists := targetMapCopy[paramIndex]

					if exists && paramInfo != nil {
						// Nom du paramètre avec puce si quantifié
						paramName := paramInfo.Name
						if paramName == "" {
							paramName = " "
						}
						displayName := paramName
						if paramInfo.IsQuantized {
							displayName = "|| " + paramName
						}
						nnParts = append(nnParts, displayName)

						// Valeur normalisée et chaîne de valeur
						if paramInfo.CurrentValue != 0 || paramInfo.ValueString != "" {
							normalizedValue := math.Round(
								((paramInfo.CurrentValue - paramInfo.MinValue) /
									(paramInfo.MaxValue - paramInfo.MinValue)) * 100,
							)
							diParts = append(diParts, fmt.Sprintf("%.0f", normalizedValue))

							valueStr := paramInfo.ValueString
							if valueStr == "" {
								valueStr = "nul"
							}
							diParts = append(diParts, valueStr)
						} else {
							diParts = append(diParts, "0", "nul")
						}
					} else {
						nnParts = append(nnParts, " ")
						diParts = append(diParts, "0", "nul")
					}
				}

				// Envoi direct des messages
				isActive := m.parentMode.IsActive()
				m.commManager.SendMessage(strings.Join(nnParts, ","), isActive)
				m.commManager.SendMessage(strings.Join(diParts, ","), isActive)

				m.isFirstPageChange = true
			})
		}

		m.lastPageChangeTime = currentTime
		log.Printf("SetPage: Changement de page réussi, retourne true")
		return true
	}
	log.Printf("SetPage: Page %d hors limites [1-%d], retourne false", page, maxPage)
	return false
}

// completePageUpdate met à jour l'affichage complet de la page
// Note: Cette fonction suppose que le mutex est déjà verrouillé par l'appelant
func (m *DeviceParameterManager) completePageUpdate(page int, targetMap map[int]*ParameterInfo, slotsPerPage int) {
	startParam := ((page - 1) * slotsPerPage) + 1
	diParts := []string{"di"}
	nnParts := []string{"nn"}

	for i := 0; i < slotsPerPage; i++ {
		paramIndex := startParam + i
		paramInfo, exists := targetMap[paramIndex]

		if exists && paramInfo != nil {
			// Nom du paramètre avec puce si quantifié
			paramName := paramInfo.Name
			if paramName == "" {
				paramName = " "
			}
			displayName := paramName
			if paramInfo.IsQuantized {
				displayName = "|| " + paramName
			}
			nnParts = append(nnParts, displayName)

			// Valeur normalisée et chaîne de valeur
			if paramInfo.CurrentValue != 0 || paramInfo.ValueString != "" {
				normalizedValue := math.Round(
					((paramInfo.CurrentValue - paramInfo.MinValue) /
						(paramInfo.MaxValue - paramInfo.MinValue)) * 100,
				)
				diParts = append(diParts, fmt.Sprintf("%.0f", normalizedValue))

				valueStr := paramInfo.ValueString
				if valueStr == "" {
					valueStr = "nul"
				}
				diParts = append(diParts, valueStr)
			} else {
				diParts = append(diParts, "0", "nul")
			}
		} else {
			nnParts = append(nnParts, " ")
			diParts = append(diParts, "0", "nul")
		}
	}

	// Envoi direct des messages
	isActive := m.parentMode.IsActive()
	m.commManager.SendMessage(strings.Join(nnParts, ","), isActive)
	m.commManager.SendMessage(strings.Join(diParts, ","), isActive)
}

// HandleUnlock gère le déverrouillage du mode
func (m *DeviceParameterManager) HandleUnlock() {
	m.mutex.Lock()

	// Effacer les paramètres verrouillés
	for k := range m.parameterInfoLocked {
		delete(m.parameterInfoLocked, k)
	}

	var param0Value int
	var paramCount int
	var isActive bool

	// Récupérer les informations nécessaires pendant que le mutex est verrouillé
	if len(m.parameterInfo) > 0 {
		if param0Info, exists := m.parameterInfo[0]; exists && param0Info.CurrentValue == 1 {
			param0Value = 1
		}
		paramCount = len(m.parameterInfo)
		isActive = m.parentMode.IsActive()
	}

	// Libérer le mutex avant d'appeler SetPage pour éviter un deadlock potentiel
	m.mutex.Unlock()

	if paramCount > 0 {
		log.Printf("HandleUnlock: Envoi du message ia,%d (mode actif: %v)", param0Value, isActive)
		m.commManager.SendMessage(fmt.Sprintf("ia,%d", param0Value), isActive)
		m.SetPage(1, paramCount, 8, false)
	}
}

// HandleChainParameterMessage gère les messages de paramètres de chaîne
func (m *DeviceParameterManager) HandleChainParameterMessage(paramType string, args []interface{}) {
	if m.state.IsLocked {
		return // On n'envoie rien si on est en mode lock
	}

	if len(args) < 3 {
		log.Printf("HandleChainParameterMessage: pas assez d'arguments (%d)", len(args))
		return
	}

	value, ok := toFloat64(args[2])
	if !ok {
		log.Printf("HandleChainParameterMessage: erreur de conversion de value")
		return
	}

	var slotLetter string
	var normalizedValue float64
	var valueString string

	switch paramType {
	case "volume":
		slotLetter = "A"
		normalizedValue = math.Round(value * 100)
		dbValue := m.volumeConverter.ToDb(value)
		if dbValue == "- inf dB" {
			valueString = "- INF dB"
		} else {
			valueString = dbValue + " dB"
		}

	case "pan":
		slotLetter = "B"
		normalizedValue = math.Round((value + 1) * 50) // Mappe -1,1 vers 0,100
		panValue := math.Round(math.Abs(value * 50))
		if value == 0 {
			valueString = "C"
		} else if value < 0 {
			valueString = fmt.Sprintf("%.0fL", panValue)
		} else {
			valueString = fmt.Sprintf("%.0fR", panValue)
		}

	case "mute":
		slotLetter = "C"
		normalizedValue = value * 100
		valueString = map[float64]string{0: "Off", 1: "On"}[value]

	case "solo":
		slotLetter = "D"
		normalizedValue = value * 100
		valueString = map[float64]string{0: "Off", 1: "On"}[value]

	default:
		return
	}

	formattedValue := fmt.Sprintf("%03.0f", normalizedValue)
	m.QueueMessage(fmt.Sprintf("pv,%s,%s,%s", slotLetter, formattedValue, valueString))
}

// HandleBulkChainParameters gère la réception en masse des paramètres de chaîne
func (m *DeviceParameterManager) HandleBulkChainParameters(values []interface{}) {
	if m.state.IsLocked {
		return // On n'envoie rien si on est en mode lock
	}

	// Log des valeurs reçues pour le débogage
	log.Printf("HandleBulkChainParameters: valeurs reçues: %v", values)

	// Variables pour stocker les valeurs des paramètres
	var volume, pan, mute, solo float64 = 0.85, 0.0, 0.0, 0.0 // Valeurs par défaut

	if len(values) >= 2 {
		// Essayer d'extraire le deuxième argument comme un slice d'interfaces
		paramValuesSlice, okParamValuesSlice := values[1].([]interface{})

		if okParamValuesSlice && len(paramValuesSlice) >= 4 {
			log.Printf("HandleBulkChainParameters: Format avec []interface{} détecté pour paramValues: %v", paramValuesSlice)

			// Extraire les valeurs en utilisant toFloat64
			if vol, ok := toFloat64(paramValuesSlice[0]); ok {
				volume = vol
			} else {
				log.Printf("HandleBulkChainParameters: Erreur conversion volume: %v (%T)", paramValuesSlice[0], paramValuesSlice[0])
			}

			if p, ok := toFloat64(paramValuesSlice[1]); ok {
				pan = p
			} else {
				log.Printf("HandleBulkChainParameters: Erreur conversion pan: %v (%T)", paramValuesSlice[1], paramValuesSlice[1])
			}

			// Gérer mute/solo (Live envoie 0.0 ou 1.0)
			if mVal, ok := toFloat64(paramValuesSlice[2]); ok {
				if mVal == 1.0 {
					mute = 1.0
				} else {
					mute = 0.0
				}
			} else {
				log.Printf("HandleBulkChainParameters: Erreur conversion mute: %v (%T)", paramValuesSlice[2], paramValuesSlice[2])
			}

			if sVal, ok := toFloat64(paramValuesSlice[3]); ok {
				if sVal == 1.0 {
					solo = 1.0
				} else {
					solo = 0.0
				}
			} else {
				log.Printf("HandleBulkChainParameters: Erreur conversion solo: %v (%T)", paramValuesSlice[3], paramValuesSlice[3])
			}

			log.Printf("HandleBulkChainParameters: valeurs extraites après conversion: volume=%.4f, pan=%.4f, mute=%.1f, solo=%.1f", volume, pan, mute, solo)

		} else {
			// Log si l'assertion de type échoue ou si le slice n'a pas assez d'éléments
			paramValuesLen := 0
			if okParamValuesSlice { // Only check len if type assertion was ok
				paramValuesLen = len(paramValuesSlice)
			}
			log.Printf("HandleBulkChainParameters: Le deuxième argument n'est pas []interface{} ou n'a pas assez de valeurs (type: %T, len: %d)", values[1], paramValuesLen)
			// Les valeurs par défaut seront utilisées
		}
	} else {
		log.Printf("HandleBulkChainParameters: pas assez d'arguments reçus (%d)", len(values))
		// Les valeurs par défaut seront utilisées
	}

	// Préparation du message des noms (nn)
	nnMessage := "nn,Volume,Pan,Mute,Solo, , , , "
	log.Printf("HandleBulkChainParameters: Préparation du message nn: %s", nnMessage)

	// Préparation du message des valeurs (di)
	diParts := []string{"di"}

	// Volume avec conversion dB
	dbValueStr := m.volumeConverter.ToDb(volume)
	if dbValueStr == "- inf dB" {
		dbValueStr = "-INF dB" // Assurer la consistance du format
	} else if dbValueStr != "0.0 dB" { // Ne pas ajouter dB si c'est exactement 0.0
		dbValueStr += " dB"
	}
	volumePercent := math.Round(volume * 100)
	diParts = append(diParts, fmt.Sprintf("%.0f", volumePercent), dbValueStr)
	log.Printf("HandleBulkChainParameters: Volume ajouté: %.0f, %s", volumePercent, dbValueStr)

	// Pan
	panPercent := math.Round((pan + 1) * 50)
	panValue := math.Round(math.Abs(pan * 50))
	var panString string
	if pan == 0 {
		panString = "C"
	} else if pan < 0 {
		panString = fmt.Sprintf("%.0fL", panValue)
	} else {
		panString = fmt.Sprintf("%.0fR", panValue)
	}
	diParts = append(diParts, fmt.Sprintf("%.0f", panPercent), panString)
	log.Printf("HandleBulkChainParameters: Pan ajouté: %.0f, %s", panPercent, panString)

	// Mute et Solo
	muteValue := math.Round(mute * 100)                       // Sera 0 ou 100
	muteString := map[float64]string{0: "Off", 1: "On"}[mute] // Utilise la valeur normalisée 0.0 ou 1.0
	diParts = append(diParts, fmt.Sprintf("%.0f", muteValue), muteString)
	log.Printf("HandleBulkChainParameters: Mute ajouté: %.0f, %s", muteValue, muteString)

	soloValue := math.Round(solo * 100)                       // Sera 0 ou 100
	soloString := map[float64]string{0: "Off", 1: "On"}[solo] // Utilise la valeur normalisée 0.0 ou 1.0
	diParts = append(diParts, fmt.Sprintf("%.0f", soloValue), soloString)
	log.Printf("HandleBulkChainParameters: Solo ajouté: %.0f, %s", soloValue, soloString)

	// Remplir le reste avec des valeurs nulles
	for i := 0; i < 4; i++ {
		diParts = append(diParts, "0", "nul")
	}

	diMessage := strings.Join(diParts, ",")
	log.Printf("HandleBulkChainParameters: Message di complet: %s", diMessage)

	// Envoi direct des messages
	isActive := m.parentMode.IsActive()
	log.Printf("HandleBulkChainParameters: Envoi des messages avec isActive=%v", isActive)
	m.commManager.SendMessage(nnMessage, isActive)
	m.commManager.SendMessage(diMessage, isActive)
}

// GetParameterInfo retourne les informations d'un paramètre
func (m *DeviceParameterManager) GetParameterInfo(parameterIndex int, isLocked bool) *ParameterInfo {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	targetMap := m.parameterInfo
	if isLocked {
		targetMap = m.parameterInfoLocked
	}
	return targetMap[parameterIndex]
}

// ToggleQuantizedValue bascule la valeur d'un paramètre quantifié
func (m *DeviceParameterManager) ToggleQuantizedValue(parameterIndex int) float64 {
	paramInfo := m.GetParameterInfo(parameterIndex, m.state.IsLocked)

	if paramInfo == nil || !paramInfo.IsQuantized {
		return -1
	}

	if paramInfo.CurrentValue >= paramInfo.MaxValue {
		return paramInfo.MinValue
	}
	return math.Min(paramInfo.MaxValue, paramInfo.CurrentValue+1)
}

// HandleEncoderChange gère les modifications d'encodeur
func (m *DeviceParameterManager) HandleEncoderChange(paramIndex int, direction int) {
	paramInfo := m.GetParameterInfo(paramIndex, m.state.IsLocked)
	if paramInfo == nil {
		log.Printf("Aucune information de paramètre trouvée pour l'index %d", paramIndex)
		return
	}

	if paramInfo.IsQuantized {
		// Implémentation simplifiée pour les paramètres quantifiés
		var newValue float64
		if direction > 0 {
			newValue = math.Min(paramInfo.MaxValue, paramInfo.CurrentValue+1)
		} else {
			newValue = math.Max(paramInfo.MinValue, paramInfo.CurrentValue-1)
		}

		if newValue != paramInfo.CurrentValue {
			m.SendParameterUpdate(nil, paramIndex, newValue)
		}
	} else {
		// Paramètres continus
		minStep := (paramInfo.MaxValue - paramInfo.MinValue) / 1000
		maxStep := (paramInfo.MaxValue - paramInfo.MinValue) / 40
		step := minStep
		if math.Abs(float64(direction)) > 3 {
			step = maxStep
		} else if math.Abs(float64(direction)) > 1 {
			step = (minStep + maxStep) / 2
		}
		newValue := paramInfo.CurrentValue + float64(direction)*step
		newValue = math.Max(paramInfo.MinValue, math.Min(paramInfo.MaxValue, newValue))

		m.SendParameterUpdate(nil, paramIndex, newValue)
	}
}

// SendParameterUpdate envoie une mise à jour de paramètre à Live
// Note: Cette fonction envoie le chemin comme une chaîne JSON car l'envoi direct
// de tableaux ne fonctionne pas correctement avec le pont OSC actuel.
func (m *DeviceParameterManager) SendParameterUpdate(path []interface{}, paramIndex int, newValue float64) {
	if len(path) > 0 {
		// Convertir le chemin en JSON
		intPath := make([]int, 0, len(path))
		for _, v := range path {
			switch val := v.(type) {
			case int:
				intPath = append(intPath, val)
			case float64:
				intPath = append(intPath, int(val))
			case int32:
				intPath = append(intPath, int(val))
			default:
				log.Printf("[ERREUR] SendParameterUpdate: Type non géré dans le chemin: %T", v)
				return
			}
		}

		// Créer une chaîne JSON pour le chemin
		pathJSON, err := json.Marshal(intPath)
		if err != nil {
			log.Printf("Erreur lors de la conversion du chemin en JSON: %v", err)
			return
		}

		// Envoyer avec le chemin JSON
		m.baseMode.Send("/live/device/set/parameter/value", []interface{}{string(pathJSON), paramIndex, newValue})
	} else {
		// Paramètres sans chemin (sélectionné ou verrouillé)
		deviceIndex := -3 // Selected device
		if m.state.IsLocked {
			deviceIndex = -4 // Locked device
		}
		m.baseMode.Send("/live/device/set/parameter/value", []interface{}{deviceIndex, paramIndex, newValue})
	}
}

// SendChainParameterUpdate envoie une mise à jour de paramètre de chaîne
func (m *DeviceParameterManager) SendChainParameterUpdate(path []interface{}, index int) {
	if len(path) == 0 {
		log.Printf("SendChainParameterUpdate: path est vide, impossible d'envoyer la commande")
		return
	}

	// Convertir le chemin en JSON
	intPath := make([]int, 0, len(path))
	for _, v := range path {
		switch val := v.(type) {
		case int:
			intPath = append(intPath, val)
		case float64:
			intPath = append(intPath, int(val))
		case int32:
			intPath = append(intPath, int(val))
		default:
			log.Printf("Type non géré dans le chemin pour SendChainParameterUpdate: %T", v)
			return
		}
	}

	// Créer une chaîne JSON pour le chemin
	pathJSON, err := json.Marshal(intPath)
	if err != nil {
		log.Printf("Erreur lors de la conversion du chemin en JSON: %v", err)
		return
	}

	// Déterminer l'adresse et les arguments selon l'index
	var address string
	var args []interface{}

	switch index {
	case 0: // Volume
		address = "/live/chain/set/volume"
		args = []interface{}{string(pathJSON), float64(0.85)}
	case 1: // Pan
		address = "/live/chain/set/pan"
		args = []interface{}{string(pathJSON), float64(0)}
	case 2: // Mute
		address = "/live/chain/set/togglemute"
		args = []interface{}{string(pathJSON)}
	case 3: // Solo
		address = "/live/chain/set/togglesolo"
		args = []interface{}{string(pathJSON)}
	default:
		log.Printf("SendChainParameterUpdate: Index de chaîne non géré: %d", index)
		return
	}

	m.baseMode.Send(address, args)
}
