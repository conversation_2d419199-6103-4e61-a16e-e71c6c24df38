package live

import (
	"fmt"
	"log"
	communication "oscbridge/Communication"
	"sync"
	"time"
)

// Constantes pour les adresses OSC utilisées par le TrackManager
const (
	trackCountAddress          = "/live/song/get/num_tracks"
	selectedTrackDeviceAddress = "/live/view/get/selected_device"
	returnTracksNameAddress    = "/live/return_tracks_name"
	setSelectedTrackAddress    = "/live/view/set/selected_track"
	disconnectAddress          = "/live/disconnect"
)

// LiveTrackManager gère l'état des pistes et sert d'intermédiaire pour tous les modes
type LiveTrackManager struct {
	*communication.EventEmitter // Intégration de l'émetteur d'événements

	manager *LiveModeManager

	// État interne
	trackCount       int
	returnTrackCount int
	selectedTrack    *int // Utiliser un pointeur pour représenter null
	selectedDevice   *int // Utiliser un pointeur pour représenter null
	devicesCount     int
	parametersCount  int
	returnTracksName []string // Supposons que ce sont des strings

	// Pour les mises à jour périodiques
	updateInterval time.Duration
	ticker         *time.Ticker
	stopTickerChan chan struct{}

	mu sync.RWMutex // Mutex pour protéger l'accès concurrent à l'état
}

// NewLiveTrackManager crée une nouvelle instance du gestionnaire de pistes
func NewLiveTrackManager(manager *LiveModeManager) *LiveTrackManager {
	tm := &LiveTrackManager{
		EventEmitter:   communication.NewEventEmitter(), // Initialiser l'émetteur intégré
		manager:        manager,
		stopTickerChan: make(chan struct{}), // Créer le canal, mais ne pas le fermer
	}
	// tm.setupHandlers() // On appellera cela après l'initialisation complète si nécessaire
	return tm
}

// Initialize configure les handlers et démarre les mises à jour.
// Séparé du constructeur pour éviter les problèmes d'initialisation circulaire si nécessaire.
func (tm *LiveTrackManager) Initialize(interval time.Duration) {
	tm.updateInterval = interval
	tm.setupHandlers()
	tm.startPeriodicUpdate()
}

// setupHandlers enregistre les gestionnaires OSC nécessaires
func (tm *LiveTrackManager) setupHandlers() {
	tm.manager.On(trackCountAddress, tm.handleTrackCountUpdate)
	tm.manager.On(selectedTrackDeviceAddress, tm.handleSelectedTrackDeviceUpdate)
	tm.manager.On(returnTracksNameAddress, tm.handleReturnTracksNameChange)
	tm.manager.On(disconnectAddress, tm.handleDisconnect)
}

// handleTrackCountUpdate gère la mise à jour du nombre de pistes
func (tm *LiveTrackManager) handleTrackCountUpdate(args []interface{}) {
	log.Printf("[TrackManager] Received %s avec args: %v", trackCountAddress, args)
	if len(args) < 2 {
		log.Printf("[TrackManager] Erreur: Nombre d'arguments insuffisant pour %s: %d", trackCountAddress, len(args))
		return
	}

	newTrackCount, ok1 := args[0].(int32) // OSC utilise souvent int32
	newReturnTrackCount, ok2 := args[1].(int32)

	if !ok1 || !ok2 {
		log.Printf("[TrackManager] Erreur: Types d'arguments incorrects pour %s: %T, %T", trackCountAddress, args[0], args[1])
		return
	}

	tm.mu.Lock()
	tm.trackCount = int(newTrackCount)
	tm.returnTrackCount = int(newReturnTrackCount)
	trackCount := tm.trackCount             // Copie pour l'émission
	returnTrackCount := tm.returnTrackCount // Copie pour l'émission
	tm.mu.Unlock()

	log.Printf("[TrackManager] Track count updated: Tracks=%d, Returns=%d", trackCount, returnTrackCount)
	tm.Emit("trackCountUpdate", []interface{}{trackCount, returnTrackCount})
}

// handleSelectedTrackDeviceUpdate gère la mise à jour de la piste/device sélectionné
func (tm *LiveTrackManager) handleSelectedTrackDeviceUpdate(args []interface{}) {
	log.Printf("[TrackManager] Received %s avec args: %v", selectedTrackDeviceAddress, args)
	if len(args) < 4 {
		log.Printf("[TrackManager] Erreur: Nombre d'arguments insuffisant pour %s: %d", selectedTrackDeviceAddress, len(args))
		return
	}

	selTrack, ok1 := args[0].(int32)
	selDevice, ok2 := args[1].(int32)
	devCount, ok3 := args[2].(int32)
	paramCount, ok4 := args[3].(int32)

	if !ok1 || !ok2 || !ok3 || !ok4 {
		log.Printf("[TrackManager] Erreur: Types d'arguments incorrects pour %s: %T, %T, %T, %T", selectedTrackDeviceAddress, args[0], args[1], args[2], args[3])
		return
	}

	tm.mu.Lock()
	// Mettre à jour les pointeurs
	selectedTrackVal := int(selTrack)
	selectedDeviceVal := int(selDevice)
	tm.selectedTrack = &selectedTrackVal
	tm.selectedDevice = &selectedDeviceVal
	tm.devicesCount = int(devCount)
	tm.parametersCount = int(paramCount)
	// Copie pour l'émission (les pointeurs copiés pointeront vers les mêmes valeurs)
	st := tm.selectedTrack
	sd := tm.selectedDevice
	dc := tm.devicesCount
	pc := tm.parametersCount
	tm.mu.Unlock()

	// Préparer les valeurs pour le log (gérer les pointeurs nil)
	logSelectedTrack := "nil"
	if st != nil {
		logSelectedTrack = fmt.Sprintf("%d", *st)
	}
	logSelectedDevice := "nil"
	if sd != nil {
		logSelectedDevice = fmt.Sprintf("%d", *sd)
	}

	log.Printf("[TrackManager] Selected Track/Device updated: SelectedTrack=%s, SelectedDevice=%s, DeviceCountOnSelectedTrack=%d, ParamCountOnSelectedDevice=%d",
		logSelectedTrack, logSelectedDevice, dc, pc)

	tm.Emit("selectedTrackDeviceUpdate", []interface{}{st, sd, dc, pc})
}

// handleReturnTracksNameChange gère le changement des noms des pistes retour
func (tm *LiveTrackManager) handleReturnTracksNameChange(args []interface{}) {
	log.Printf("[TrackManager] Received %s avec args: %v", returnTracksNameAddress, args)
	newNames := make([]string, 0, len(args))
	validArgs := true
	for i, arg := range args {
		if name, ok := arg.(string); ok {
			newNames = append(newNames, name)
		} else {
			log.Printf("[TrackManager] Erreur: Type d'argument incorrect pour %s à l'index %d: %T", returnTracksNameAddress, i, arg)
			// Décider si on continue avec les noms valides ou si on abandonne
			// Pour l'instant, on continue mais on marque comme invalide pour ne pas logger des données partielles
			validArgs = false
		}
	}

	// Seulement mettre à jour et logger si tous les arguments étaient valides
	if validArgs {
		tm.mu.Lock()
		tm.returnTracksName = newNames
		namesCopy := make([]string, len(tm.returnTracksName)) // Faire une copie pour l'émission
		copy(namesCopy, tm.returnTracksName)
		tm.mu.Unlock()

		log.Printf("[TrackManager] Return Tracks Name updated: %v", namesCopy)
		tm.Emit("returnTracksNameChange", []interface{}{namesCopy})
	} else {
		log.Printf("[TrackManager] Mise à jour des noms de pistes retour ignorée en raison d'arguments invalides.")
	}
	// log.Println("Return tracks name0:", namesCopy[0]) // Accès sécurisé nécessaire
}

// startPeriodicUpdate démarre l'envoi périodique des requêtes d'état
func (tm *LiveTrackManager) startPeriodicUpdate() {
	tm.stopPeriodicUpdate() // Arrêter l'ancien ticker s'il existe

	if tm.updateInterval <= 0 {
		log.Println("Intervalle de mise à jour invalide, les mises à jour périodiques sont désactivées.")
		return
	}

	tm.ticker = time.NewTicker(tm.updateInterval)
	tm.stopTickerChan = make(chan struct{}) // Recréer le canal d'arrêt

	// Envoyer les requêtes initiales immédiatement
	tm.requestTrackCount()
	tm.requestSelectedTrackDevice()
	tm.requestReturnTracksName()

	go func() {
		log.Println("Démarrage des mises à jour périodiques du TrackManager")
		for {
			select {
			case <-tm.ticker.C:
				tm.requestTrackCount()
				tm.requestSelectedTrackDevice()
				tm.requestReturnTracksName()
			case <-tm.stopTickerChan:
				log.Println("Arrêt des mises à jour périodiques du TrackManager")
				tm.ticker.Stop()
				return
			}
		}
	}()
}

// stopPeriodicUpdate arrête l'envoi périodique des requêtes
func (tm *LiveTrackManager) stopPeriodicUpdate() {
	// Utiliser tm.mu pourrait être nécessaire si ticker/stopTickerChan sont modifiés ailleurs,
	// mais ici, c'est principalement contrôlé par start/stop/cleanup.
	if tm.ticker != nil {
		// Fermer le canal signale à la goroutine de s'arrêter
		// Vérifier si le canal n'est pas déjà fermé pour éviter la panique
		select {
		case <-tm.stopTickerChan:
			// Déjà fermé ou nil, ne rien faire
		default:
			close(tm.stopTickerChan)
		}
		tm.ticker = nil // Marquer comme arrêté
	}
}

// requestTrackCount demande le nombre de pistes
func (tm *LiveTrackManager) requestTrackCount() {
	err := tm.manager.Send(trackCountAddress)
	if err != nil {
		log.Printf("Erreur lors de l'envoi de la requête %s: %v", trackCountAddress, err)
	}
}

// requestSelectedTrackDevice demande la piste et le device sélectionnés
func (tm *LiveTrackManager) requestSelectedTrackDevice() {
	err := tm.manager.Send(selectedTrackDeviceAddress)
	if err != nil {
		log.Printf("Erreur lors de l'envoi de la requête %s: %v", selectedTrackDeviceAddress, err)
	}
}

// requestReturnTracksName demande les noms des pistes retour
func (tm *LiveTrackManager) requestReturnTracksName() {
	err := tm.manager.Send(returnTracksNameAddress)
	if err != nil {
		log.Printf("Erreur lors de l'envoi de la requête %s: %v", returnTracksNameAddress, err)
	}
}

// --- Getters (protégés par RWMutex) ---

func (tm *LiveTrackManager) GetTrackCount() int {
	tm.mu.RLock()
	defer tm.mu.RUnlock()
	return tm.trackCount
}

func (tm *LiveTrackManager) GetReturnTrackCount() int {
	tm.mu.RLock()
	defer tm.mu.RUnlock()
	return tm.returnTrackCount
}

func (tm *LiveTrackManager) GetDevicesCount() int {
	tm.mu.RLock()
	defer tm.mu.RUnlock()
	return tm.devicesCount
}

func (tm *LiveTrackManager) GetParametersCount() int {
	tm.mu.RLock()
	defer tm.mu.RUnlock()
	return tm.parametersCount
}

func (tm *LiveTrackManager) GetSelectedTrack() *int {
	tm.mu.RLock()
	defer tm.mu.RUnlock()
	// Retourne une copie du pointeur (pointe toujours vers la même valeur)
	// ou nil si le pointeur interne est nil.
	if tm.selectedTrack == nil {
		return nil
	}
	val := *tm.selectedTrack
	return &val // Retourne un pointeur vers une copie de la valeur
}

func (tm *LiveTrackManager) GetSelectedDevice() *int {
	tm.mu.RLock()
	defer tm.mu.RUnlock()
	if tm.selectedDevice == nil {
		return nil
	}
	val := *tm.selectedDevice
	return &val // Retourne un pointeur vers une copie de la valeur
}

// SetSelectedTrack envoie une requête pour changer la piste sélectionnée
func (tm *LiveTrackManager) SetSelectedTrack(index int) error {
	if index < 0 {
		return fmt.Errorf("l'index doit être un nombre positif")
	}
	// Envoyer en int32 comme c'est courant en OSC
	return tm.manager.Send(setSelectedTrackAddress, int32(index))
}

func (tm *LiveTrackManager) GetReturnTracksName() []string {
	tm.mu.RLock()
	defer tm.mu.RUnlock()
	// Retourner une copie pour éviter les modifications externes
	namesCopy := make([]string, len(tm.returnTracksName))
	copy(namesCopy, tm.returnTracksName)
	return namesCopy
}

// Cleanup nettoie les ressources (ticker, listeners OSC)
func (tm *LiveTrackManager) Cleanup() {
	log.Println("Nettoyage du LiveTrackManager...")
	tm.stopPeriodicUpdate()

	// Se désabonner des messages OSC
	tm.manager.Off(trackCountAddress, tm.handleTrackCountUpdate)
	tm.manager.Off(selectedTrackDeviceAddress, tm.handleSelectedTrackDeviceUpdate)
	tm.manager.Off(returnTracksNameAddress, tm.handleReturnTracksNameChange)
	tm.manager.Off(disconnectAddress, tm.handleDisconnect)

	// Nettoyer l'émetteur d'événements intégré
	tm.RemoveAllListeners()
}

// handleDisconnect gère la déconnexion de Live
// Accepter les arguments même s'ils ne sont pas utilisés pour correspondre à la signature attendue par On/Off
func (tm *LiveTrackManager) handleDisconnect(args []interface{}) {
	log.Println("Live déconnecté - Réinitialisation du système TrackManager")

	// Ignorer les arguments (args) car ce message n'en a probablement pas
	_ = args

	tm.stopPeriodicUpdate() // Arrêter les mises à jour

	tm.mu.Lock()
	// Réinitialiser l'état interne
	tm.trackCount = 0
	tm.returnTrackCount = 0
	tm.selectedTrack = nil
	tm.selectedDevice = nil
	tm.devicesCount = 0
	tm.parametersCount = 0
	tm.returnTracksName = []string{}
	tm.mu.Unlock()

	// Émettre un événement pour que le LiveModeManager ou d'autres puissent réagir
	tm.Emit("liveDisconnected", nil)

	// Redémarrer immédiatement le cycle de mise à jour/requête initiale
	// Attendre un court instant peut être judicieux pour laisser le temps à Live de redémarrer complètement
	// time.Sleep(1 * time.Second)
	log.Println("Tentative de redémarrage des mises à jour périodiques après déconnexion...")
	tm.startPeriodicUpdate()
}

// RemoveListener supprime un listener pour un événement spécifique
func (tm *LiveTrackManager) RemoveListener(event string, handler func([]interface{})) {
	tm.EventEmitter.Off(event, handler)
}
