package types

// Parameter types for learn mode
const (
	ParamTypeVolume      = 1
	ParamTypePan         = 2
	ParamTypeSend        = 3
	ParamTypeDevice      = 4
	ParamTypeChainVolume = 5
	ParamTypeChainPan    = 6
	ParamTypeMute        = 7
	ParamTypeSolo        = 8
	ParamTypeChainMute   = 9
	ParamTypeChainSolo   = 10
)

// LearnData représente les données d'apprentissage pour le mode learn
type LearnData struct {
	ParamType   int
	TrackIndex  int
	SendIndex   *int     // Pointeur pour permettre la valeur nil
	DeviceIndex *int     // Pointeur pour permettre la valeur nil
	ParamIndex  *int     // Pointeur pour permettre la valeur nil
	ChainPath   []int    // Chemin de la chaîne pour les paramètres de chaîne
	IsQuantized *bool    // Indique si le paramètre est quantifié (pour les paramètres de device)
	MinValue    *float64 // Valeur minimale du paramètre (pour les paramètres de device)
	MaxValue    *float64 // Valeur maximale du paramètre (pour les paramètres de device)
}
