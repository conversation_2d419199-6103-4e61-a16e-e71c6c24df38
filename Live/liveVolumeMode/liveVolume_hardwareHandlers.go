package liveVolumeMode

import (
	"fmt"
	"log"
	"math"
	communication "oscbridge/Communication"
	live "oscbridge/Live"
	"oscbridge/Live/types"
	"strconv"
	"strings"
)

// HandleHardwareEvent traite les événements matériels reçus
func (m *LiveVolumeMode) HandleHardwareEvent(event communication.HardwareEvent) {
	log.Printf("Mode volume: Événement matériel reçu: Type=%s", event.Type)

	switch event.Type {
	case "touch":
		m.handleTouchEvent(event.TouchEvent)
	case "button":
		m.handleButtonEvent(event.ButtonEvent)
	case "encoder":
		m.handleEncoderEvent(event.EncoderEvent)
	}
}

// handleTouchEvent traite les événements tactiles
func (m *LiveVolumeMode) handleTouchEvent(event *communication.TouchEvent) {
	if event == nil {
		return
	}

	log.Printf("Mode volume: Toucher de type %s sur l'index %d",
		event.TouchType, event.Index)

	switch event.TouchType {
	case "st": // Sélection de piste
		startTrack := (m.state.CurrentPage - 1) * SlotsPerPage
		absoluteTrackIndex := startTrack + event.Index
		totalTracks := m.trackManager.GetTrackCount()

		if absoluteTrackIndex < totalTracks {
			log.Printf("Sélection de la piste %d", absoluteTrackIndex)
			m.trackManager.SetSelectedTrack(absoluteTrackIndex)
		} else {
			log.Printf("Pas de piste associée à l'index %d", absoluteTrackIndex)
		}

	case "lv": // Lock/Unlock volume mode
		if event.Index == 0 {
			m.DisableLock()
		} else if event.Index == 1 {
			m.EnableLock()
		}

	case "ad": // Add/Delete tracks
		switch event.Index {
		case 1:
			m.BaseMode.Send("/live/song/create_midi_track")
		case 2:
			m.BaseMode.Send("/live/song/create_audio_track")
		case 3:
			m.BaseMode.Send("/live/song/create_return_track")
		case 4:
			m.BaseMode.Send("/live/song/delete_selected_track")
		}

	case "sm": // Switch mode
		var targetMode string
		switch event.Index {
		case 0:
			targetMode = "track"
		case 2:
			targetMode = "device"
		case 3:
			targetMode = "learn"
		case 4:
			targetMode = "browser"
		}
		if targetMode != "" {
			m.BaseMode.Emit("modeChange", targetMode)
		}

	case "le": // Learn mode
		startTrack := (m.state.CurrentPage - 1) * SlotsPerPage
		absoluteTrackIndex := startTrack + event.Index
		totalTracks := m.trackManager.GetTrackCount()

		// Vérifier si c'est la piste main et si nous sommes en mode send
		if absoluteTrackIndex == totalTracks-1 && strings.HasPrefix(m.state.SubMode, SubmodeSendPrefix) {
			log.Println("Ignoré: tentative d'apprentissage de send sur la piste main")
			return
		}

		var paramType int
		var sendIndex *int

		// Déterminer le type de paramètre en fonction du sous-mode actuel
		switch m.state.SubMode {
		case SubmodeVolume:
			paramType = types.ParamTypeVolume
		case SubmodePan:
			paramType = types.ParamTypePan
		default:
			if strings.HasPrefix(m.state.SubMode, SubmodeSendPrefix) {
				paramType = types.ParamTypeSend
				idx, err := strconv.Atoi(m.state.SubMode[1:])
				if err == nil {
					sendIndex = &idx
				}
			}
		}

		// Créer les données d'apprentissage
		learnData := &types.LearnData{
			ParamType:   paramType,
			TrackIndex:  absoluteTrackIndex,
			SendIndex:   sendIndex,
			DeviceIndex: nil,
			ParamIndex:  nil,
		}

		// Mettre à jour les données d'apprentissage dans le mode manager
		if service := m.BaseMode.GetService(); service != nil {
			if modeManager, ok := service.(*live.LiveModeManager); ok {
				modeManager.SetLearnData(learnData)
			}
		}

		log.Printf("Mode learn: %+v", learnData)
		m.BaseMode.Emit("modeChange", "learn")

	case "so": // Solo toggle
		startTrack := (m.state.CurrentPage - 1) * SlotsPerPage
		absoluteTrackIndex := startTrack + event.Index
		m.BaseMode.Send("/live/track/set/solotoggle", absoluteTrackIndex)
		log.Printf("Toggle solo for track %d", absoluteTrackIndex)

	case "mu": // Mute toggle
		startTrack := (m.state.CurrentPage - 1) * SlotsPerPage
		absoluteTrackIndex := startTrack + event.Index
		m.BaseMode.Send("/live/track/set/mutetoggle", absoluteTrackIndex)
		log.Printf("Toggle mute for track %d", absoluteTrackIndex)

	case "vm": // Volume mode selection
		log.Printf("VM touch event: index=%d, current mode=%s", event.Index, m.state.SubMode)
		switch event.Index {
		case 0:
			m.SetSubMode(SubmodeVolume)
		case 1:
			m.SetSubMode(SubmodePan)
		case 2:
			// Mode send - Logique cyclique
			currentMode := m.state.SubMode
			if strings.HasPrefix(currentMode, SubmodeSendPrefix) {
				// Si on est déjà en mode send, on passe au suivant
				currentSendIndex, err := strconv.Atoi(currentMode[1:])
				if err != nil {
					log.Printf("Erreur de parsing du mode send: %v", err)
					return
				}
				sendModes := []string{}
				for _, mode := range m.state.AvailableSubmodes {
					if strings.HasPrefix(mode, SubmodeSendPrefix) {
						sendModes = append(sendModes, mode)
					}
				}
				log.Printf("Send modes disponibles: %v", sendModes)
				if len(sendModes) > 0 {
					nextSendIndex := (currentSendIndex + 1) % len(sendModes)
					nextMode := fmt.Sprintf("%s%d", SubmodeSendPrefix, nextSendIndex)
					log.Printf("Passage au mode send suivant: %s", nextMode)
					m.SetSubMode(nextMode)
				} else {
					log.Printf("Aucun mode send disponible")
				}
			} else {
				// Si on n'est pas en mode send, on commence par le premier
				log.Printf("Passage au premier mode send: %s0", SubmodeSendPrefix)
				m.SetSubMode(SubmodeSendPrefix + "0")
			}
		}

	case "fo": // Fold toggle
		startTrack := (m.state.CurrentPage - 1) * SlotsPerPage
		absoluteTrackIndex := startTrack + event.Index
		m.BaseMode.Send("/live/track/set/foldtoggle", absoluteTrackIndex)
		log.Printf("Toggle fold for track %d", absoluteTrackIndex)

	case "ar": // Arm toggle
		startTrack := (m.state.CurrentPage - 1) * SlotsPerPage
		absoluteTrackIndex := startTrack + event.Index
		m.BaseMode.Send("/live/track/set/armtoggle", absoluteTrackIndex)
		log.Printf("Toggle arm for track %d", absoluteTrackIndex)

	default:
		log.Printf("Type de toucher non géré: %s", event.TouchType)
	}
}

// handleButtonEvent traite les événements de bouton
func (m *LiveVolumeMode) handleButtonEvent(event *communication.ButtonEvent) {
	if event == nil {
		return
	}

	log.Printf("Mode volume: Bouton %d état %d", event.Index, event.State)
	
	// Ne traiter que les boutons appuyés (state = 1), ignorer les relâchements (state = 0)
	if event.State != 1 {
		log.Printf("Mode volume: Bouton %d relâché, ignoré", event.Index)
		return
	}
	
	log.Printf("Mode volume: Bouton %d appuyé, traitement", event.Index)
	
	// Traiter les boutons selon leur index
	switch event.Index {
	// Boutons spécifiques au mode volume
	case 0: // B00 - Switch to Learn Mode
		log.Println("Mode Volume: B00 - Switch to Learn Mode")
		m.BaseMode.Emit("modeChange", "learn")
		
	case 1: // B01 - Switch to Track Mode (QuickView off)
		log.Println("Mode Volume: B01 - Switch to Track Mode (QuickView off)")
		m.BaseMode.Emit("modeChange", "track")
		
	case 2: // B02 - Return to page 1
		log.Println("Mode Volume: B02 - Return to page 1")
		m.SetPage(1)
		
	case 3: // B03 - Switch to Device Mode
		log.Println("Mode Volume: B03 - Switch to Device Mode")
		m.BaseMode.Emit("modeChange", "device")
		
	case 4: // B04 - Switch to Browse Mode
		log.Println("Mode Volume: B04 - Switch to Browse Mode")
		m.BaseMode.Emit("modeChange", "browser")
		
	// B05 est maintenant géré comme bouton transversal dans HardwareManager
	
	case 6: // B06 - Previous submode
		log.Println("Mode Volume: B06 - Previous submode")
		m.ModeDown()
		
	case 7: // B07 - Next submode
		log.Println("Mode Volume: B07 - Next submode")
		m.ModeUp()
		
	case 8: // B08 - Page down
		log.Println("Mode Volume: B08 - Page down")
		m.PageDown()
		
	case 9: // B09 - Page up
		log.Println("Mode Volume: B09 - Page up")
		m.PageUp()
		
	// B10, B11, B12 sont maintenant gérés comme boutons transversaux dans HardwareManager
		
	case 13: // B13 - Lock/Unlock
		log.Println("Mode Volume: B13 - Lock/Unlock")
		if m.state.IsLocked {
			m.DisableLock()
		} else {
			m.EnableLock()
		}
		
	default:
		log.Printf("Mode volume: Bouton %d non géré", event.Index)
	}
}

// handleEncoderEvent traite les événements d'encodeur
func (m *LiveVolumeMode) handleEncoderEvent(event *communication.EncoderEvent) {
	if event == nil {
		return
	}

	log.Printf("Mode volume: Encodeur %d tourné avec la valeur %d",
		event.Index, event.Value)

	// Vérifier que l'index est valide (0-7 pour les 8 pistes par page)
	if event.Index < 0 || event.Index >= SlotsPerPage {
		log.Printf("Index d'encodeur invalide: %d", event.Index)
		return
	}

	// Calculer l'index absolu de la piste
	startTrack := (m.state.CurrentPage - 1) * SlotsPerPage
	absoluteTrackIndex := startTrack + event.Index

	// Vérifier que la piste existe dans les données
	trackData, exists := m.tracksData[absoluteTrackIndex]
	if !exists {
		log.Printf("Pas de données pour la piste %d", absoluteTrackIndex)
		return
	}

	// Calculer le delta normalisé (les valeurs d'encodeur sont sur une base 1000)
	// Convertir en delta sur base 1.0
	rawDelta := float64(event.Value) / 1000.0

	// Récupérer le buffer actuel pour cet encodeur
	currentBuffer, exists := m.state.EncoderBuffers[event.Index]
	if !exists {
		currentBuffer = 0.0
	}

	// Ajouter le delta au buffer
	newBuffer := currentBuffer + rawDelta

	// Vérifier si le buffer a atteint le seuil (0.005 ou -0.005)
	threshold := 0.005
	var deltaToSend float64 = 0.0
	var shouldSend bool = false

	if math.Abs(newBuffer) >= threshold {
		// Calculer combien de "pas" de threshold sont dans le buffer
		steps := int(newBuffer / threshold)
		if steps == 0 && newBuffer < 0 {
			steps = -1 // Assurer au moins un pas négatif si le buffer est négatif
		}

		// Calculer le delta à envoyer (multiple du threshold)
		deltaToSend = float64(steps) * threshold

		// Mettre à jour le buffer en soustrayant ce qui a été envoyé
		newBuffer -= deltaToSend

		shouldSend = true
	}

	// Mettre à jour le buffer
	m.state.EncoderBuffers[event.Index] = newBuffer

	log.Printf("Encodeur %d: Delta brut: %f, Buffer: %f, Delta à envoyer: %f, Envoi: %v",
		event.Index, rawDelta, newBuffer, deltaToSend, shouldSend)

	// Si aucun delta à envoyer, sortir
	if !shouldSend {
		return
	}

	// Utiliser deltaToSend comme valeur normalisée
	normalizedDelta := deltaToSend

	// Traiter selon le mode actuel
	switch {
	case m.state.SubMode == SubmodeVolume:
		// Envoyer le message OSC avec le delta pour mettre à jour le volume
		// Python se chargera de récupérer la valeur actuelle, d'appliquer le delta et de limiter la plage
		m.BaseMode.Send("/live/track/adjust/volume", absoluteTrackIndex, normalizedDelta)
		log.Printf("Envoi OSC: /live/track/adjust/volume %d %f", absoluteTrackIndex, normalizedDelta)

	case m.state.SubMode == SubmodePan:
		// Envoyer le message OSC avec le delta pour mettre à jour le pan
		// Python se chargera de récupérer la valeur actuelle, d'appliquer le delta et de limiter la plage
		m.BaseMode.Send("/live/track/adjust/panning", absoluteTrackIndex, normalizedDelta)
		log.Printf("Envoi OSC: /live/track/adjust/panning %d %f", absoluteTrackIndex, normalizedDelta)

	case strings.HasPrefix(m.state.SubMode, SubmodeSendPrefix):
		// Extraire l'index du send
		sendIndex, err := strconv.Atoi(m.state.SubMode[1:])
		if err != nil {
			log.Printf("Erreur de parsing du mode send: %v", err)
			return
		}

		// Vérifier que le send existe pour cette piste
		if sendIndex >= len(trackData.Sends) {
			log.Printf("Send %d non disponible pour la piste %d", sendIndex, absoluteTrackIndex)
			return
		}

		// Envoyer le message OSC avec le delta pour mettre à jour le send
		// Python se chargera de récupérer la valeur actuelle, d'appliquer le delta et de limiter la plage
		m.BaseMode.Send("/live/track/adjust/send", absoluteTrackIndex, sendIndex, normalizedDelta)
		log.Printf("Envoi OSC: /live/track/adjust/send %d %d %f", absoluteTrackIndex, sendIndex, normalizedDelta)
	}
}
