package liveVolumeMode

import (
	"fmt"
	"log"
	"math"
	communication "oscbridge/Communication"
	live "oscbridge/Live"
	"sync"
	"time"
)

// LiveVolumeMode gère le mode volume de Live
type LiveVolumeMode struct {
	*communication.BaseMode
	trackManager      *live.LiveTrackManager
	commManager       *communication.CommunicationManager
	displayManager    *LiveVolumeDisplayManager
	state             *LiveVolumeModeState
	tracksData        map[int]*TrackData
	isActive          bool
	lastSelectedTrack *int             // Pour stocker la dernière piste sélectionnée
	pageChangeState   *PageChangeState // Pour gérer les changements de page

	// Nouveaux champs pour la gestion simplifiée de la structure
	pendingTracksData map[int]*TrackData // Map temporaire pour construire la nouvelle structure
	pendingTrackCount int                // Nombre de pistes attendues dans la structure en cours
	structureMutex    sync.Mutex         // Mutex pour protéger pendingTracksData et le remplacement de tracksData

	// Champs pour la gestion des séquences de messages OSC
	graceTimer *time.Timer // Timer de grâce pour finaliser une structure incomplète

	// Nouveaux champs pour la gestion des handlers
	backgroundHandlers map[string]func([]interface{})
	activeHandlers     map[string]func([]interface{})
	hardwareHandler    func(communication.HardwareEvent)
}

// NewLiveVolumeMode crée une nouvelle instance du mode volume
func NewLiveVolumeMode(trackManager *live.LiveTrackManager, commManager *communication.CommunicationManager) *LiveVolumeMode {
	// Initialiser les sous-modes disponibles en fonction des pistes de retour
	availableSubmodes := []string{SubmodeVolume, SubmodePan}
	returnTrackCount := trackManager.GetReturnTrackCount()
	log.Printf("NewLiveVolumeMode: returnTrackCount=%d", returnTrackCount)

	// Ajouter les modes de sends en fonction du nombre de pistes de retour
	for i := 0; i < returnTrackCount; i++ {
		availableSubmodes = append(availableSubmodes, fmt.Sprintf("%s%d", SubmodeSendPrefix, i))
	}

	mode := &LiveVolumeMode{
		BaseMode:     communication.NewBaseMode(),
		trackManager: trackManager,
		commManager:  commManager,
		state: &LiveVolumeModeState{
			IsLocked:          false,
			SubMode:           SubmodeVolume,
			AvailableSubmodes: availableSubmodes,
			CurrentPage:       1,
			EncoderBuffers:    make(map[int]float64),
		},
		tracksData:      make(map[int]*TrackData),
		isActive:        false,
		pageChangeState: NewPageChangeState(),
	}

	mode.displayManager = NewLiveVolumeDisplayManager(commManager, trackManager, mode.state, mode)
	mode.registerEventHandlers() // Enregistrer les écouteurs d'événements

	log.Printf("NewLiveVolumeMode: Sous-modes disponibles initialisés: %v", availableSubmodes)

	return mode
}

// Initialize initialise le mode volume
func (m *LiveVolumeMode) Initialize(service communication.OscService) {
	log.Println("Initialisation du mode volume...")

	// Initialiser le mode de base
	m.BaseMode.Initialize(service)

	// Initialiser les maps de handlers
	m.backgroundHandlers = make(map[string]func([]interface{}))
	m.activeHandlers = make(map[string]func([]interface{}))

	// Démarrer les listeners en arrière-plan (qui restent actifs en permanence)
	log.Println("Démarrage des listeners en arrière-plan...")

	// Enregistrer les handlers OSC background
	m.registerOSCHandlers()

	// Enregistrer les écouteurs d'événements background
	m.registerEventHandlers()

	// Mettre à jour pour la piste sélectionnée actuelle
	if selectedTrack := m.trackManager.GetSelectedTrack(); selectedTrack != nil {
		m.onSelectedTrackChange([]interface{}{selectedTrack})
	}

	// Envoyer le message initial pour mettre à jour le mode volume
	m.BaseMode.Send("/live/update_volumeMode", []interface{}{})

	// Initialiser l'affichage
	m.displayManager.ClearEmptySlots()

	// Définir le sous-mode initial
	m.SetSubMode(SubmodeVolume)
	m.commManager.SendMessage("vi", true)
	m.displayManager.SendFullUpdate(m.tracksData)

	log.Println("Mode volume initialisé avec succès.")
}

// Activate active le mode volume et démarre les listeners actifs
func (m *LiveVolumeMode) Activate() {
	log.Println("\n=== Activating VolumeMode ===")
	m.BaseMode.Activate()
	m.isActive = true

	// Démarrer uniquement les listeners actifs
	log.Println("Démarrage des listeners actifs...")

	// Configurer le handler hardware
	m.hardwareHandler = m.HandleHardwareEvent
	m.commManager.AddHardwareEventHandler(m.hardwareHandler)

	// Initialiser les sous-modes disponibles
	log.Println("Activation: Mise à jour des sous-modes disponibles...")
	m.updateAvailableSubmodes()
	log.Printf("Activation: Sous-modes disponibles après mise à jour: %v", m.state.AvailableSubmodes)

	// Activer l'affichage du mode volume
	// Utiliser true au lieu de m.isActive pour s'assurer que le message est envoyé
	// même si m.isActive n'est pas encore à true (ce qui peut arriver pendant l'activation)
	m.commManager.SendMessage("mo,1", true)

	// IMPORTANT: Restaurer la dernière piste sélectionnée si elle existe
	// Cela doit être fait après toute autre initialisation pour éviter
	// que d'autres commandes d'initialisation n'écrasent la sélection
	if m.lastSelectedTrack != nil {
		log.Printf("Restauration de la sélection de piste: %d", *m.lastSelectedTrack)
		startTrack := (m.state.CurrentPage - 1) * SlotsPerPage
		relativeIndex := *m.lastSelectedTrack - startTrack

		// Vérifier si la piste est sur la page actuelle
		if relativeIndex >= 0 && relativeIndex < SlotsPerPage {
			slotLetter := string(rune('A' + relativeIndex))
			// Envoyer directement le message sans passer par UpdateSelectedTrack
			// pour éviter les effets de bord potentiels pendant l'initialisation
			// Utiliser true au lieu de m.isActive pour s'assurer que le message est envoyé
			m.commManager.SendMessage("st"+slotLetter, true)
			log.Printf("Restauration de la sélection: slot %s", slotLetter)
		}
	}

	// Mettre à jour l'affichage après avoir restauré la sélection
	// Forcer l'envoi même si le mode n'est pas encore marqué comme actif

	// S'assurer que le mode est bien actif avant de continuer
	m.isActive = true

	log.Println("=== VolumeMode Activation Complete ===")
}

// Deactivate désactive le mode volume (utilisé lors du changement de mode)
func (m *LiveVolumeMode) Deactivate() {
	log.Println("\n=== Deactivating VolumeMode ===")
	m.BaseMode.Deactivate()
	m.isActive = false

	// Supprimer uniquement les handlers actifs
	if m.hardwareHandler != nil {
		m.commManager.RemoveHardwareEventHandler(m.hardwareHandler)
		m.hardwareHandler = nil
	}

	log.Println("=== VolumeMode Deactivation Complete ===")
}

// CleanupForExit nettoie toutes les ressources avant la sortie de l'application
func (m *LiveVolumeMode) CleanupForExit() {
	log.Println("\n=== VolumeMode Complete Cleanup Start ===")

	// Désactiver d'abord le mode s'il est actif
	if m.isActive {
		m.Deactivate()
	}

	// Stopper le timer de structure s'il est actif
	m.structureMutex.Lock()
	if m.graceTimer != nil {
		m.graceTimer.Stop()
		m.graceTimer = nil
	}
	m.structureMutex.Unlock()

	// Supprimer les handlers background
	for address := range m.backgroundHandlers {
		m.BaseMode.UnregisterHandler(address)
		delete(m.backgroundHandlers, address)
	}

	// Supprimer les écouteurs d'événements du trackManager
	m.trackManager.RemoveListener("selectedTrackDeviceUpdate", m.onSelectedTrackChange)
	m.trackManager.RemoveListener("returnTracksNameChange", m.onReturnTracksNameChange)

	log.Println("=== VolumeMode Complete Cleanup Complete ===")
}

// IsActive retourne si le mode est actif
func (m *LiveVolumeMode) IsActive() bool {
	return m.isActive
}

// SetSubMode définit le sous-mode actuel
func (m *LiveVolumeMode) SetSubMode(mode string) bool {
	log.Printf("Tentative de changement de mode vers: %s", mode)
	log.Printf("Modes disponibles: %v", m.state.AvailableSubmodes)

	// Vérifier si le mode est disponible
	modeAvailable := false
	for _, availableMode := range m.state.AvailableSubmodes {
		if availableMode == mode {
			modeAvailable = true
			break
		}
	}

	if modeAvailable {
		// Sauvegarder l'ancien mode pour vérifier s'il y a eu un changement
		oldMode := m.state.SubMode

		// Mettre à jour le mode
		m.state.SubMode = mode
		log.Printf("Nouveau mode actif: %s", mode)

		// Ne mettre à jour l'affichage que si le mode a réellement changé
		if oldMode != mode {
			// Déterminer l'index et le nom du send AVANT de mettre à jour l'affichage
			var modeIndex int
			sendName := ""

			if mode == SubmodeVolume {
				modeIndex = 0
			} else if mode == SubmodePan {
				modeIndex = 1
			} else if len(mode) > 1 && mode[0] == SubmodeSendPrefix[0] {
				modeIndex = 2
				// Extraire l'index du send (s0, s1, etc.)
				sendIndex := -1
				_, err := fmt.Sscanf(mode[1:], "%d", &sendIndex)
				if err != nil {
					log.Printf("Erreur de parsing du mode send: %v", err)
					return false
				}

				// Utiliser le nom réel du send depuis liveTrackManager
				returnTracksName := m.trackManager.GetReturnTracksName()
				log.Printf("Noms des pistes de retour: %v", returnTracksName)
				if returnTracksName != nil && len(returnTracksName) > sendIndex {
					sendName = returnTracksName[sendIndex]
					log.Printf("Nom du send sélectionné: %s", sendName)

					// Tronquer le nom si nécessaire pour l'affichage
					if len(sendName) > 27 { // 27 = 30 - 3 (pour "vz," et les virgules)
						sendName = fmt.Sprintf("%s...", sendName[:24])
					}
				}
			}

			// C'est ici qu'on doit mettre à jour toutes les valeurs d'un coup,
			// car on a changé de sous-mode
			log.Printf("Mise à jour complète des valeurs suite au changement de mode %s -> %s", oldMode, mode)
			m.displayManager.UpdateAllValuesAtOnce(mode, m.tracksData, modeIndex, sendName)
		}

		return true
	}

	log.Printf("Mode %s non disponible", mode)
	return false
}

// ModeUp passe au prochain sous-mode disponible
func (m *LiveVolumeMode) ModeUp() {
	currentIndex := -1
	for i, mode := range m.state.AvailableSubmodes {
		if mode == m.state.SubMode {
			currentIndex = i
			break
		}
	}

	if currentIndex < len(m.state.AvailableSubmodes)-1 {
		m.SetSubMode(m.state.AvailableSubmodes[currentIndex+1])
	}
}

// ModeDown passe au sous-mode précédent disponible
func (m *LiveVolumeMode) ModeDown() {
	currentIndex := -1
	for i, mode := range m.state.AvailableSubmodes {
		if mode == m.state.SubMode {
			currentIndex = i
			break
		}
	}

	if currentIndex > 0 {
		m.SetSubMode(m.state.AvailableSubmodes[currentIndex-1])
	}
}

// updateAvailableSubmodes met à jour la liste des sous-modes disponibles en fonction des pistes
func (m *LiveVolumeMode) updateAvailableSubmodes() {
	// Toujours inclure les modes de base
	submodes := []string{SubmodeVolume, SubmodePan}

	// Déterminer le nombre maximum de sends parmi toutes les pistes
	maxSendCount := 0
	for _, trackData := range m.tracksData {
		if trackData.Sends != nil && len(trackData.Sends) > maxSendCount {
			maxSendCount = len(trackData.Sends)
		}
	}

	// Vérifier également le nombre de pistes de retour depuis le trackManager
	returnTrackCount := m.trackManager.GetReturnTrackCount()
	log.Printf("updateAvailableSubmodes: maxSendCount=%d, returnTrackCount=%d", maxSendCount, returnTrackCount)

	// Utiliser le maximum entre le nombre de sends trouvés et le nombre de pistes de retour
	if returnTrackCount > maxSendCount {
		maxSendCount = returnTrackCount
	}

	// Ajouter les modes de sends
	for i := 0; i < maxSendCount; i++ {
		submodes = append(submodes, fmt.Sprintf("%s%d", SubmodeSendPrefix, i))
	}

	// Mettre à jour l'état
	m.state.AvailableSubmodes = submodes

	// Si le mode actuel n'est plus disponible, revenir au mode volume
	modeAvailable := false
	for _, mode := range submodes {
		if mode == m.state.SubMode {
			modeAvailable = true
			break
		}
	}

	if !modeAvailable {
		m.state.SubMode = SubmodeVolume
	}

	log.Printf("Modes disponibles mis à jour: %v", submodes)
}

// registerEventHandlers enregistre les écouteurs d'événements pour le LiveTrackManager
func (m *LiveVolumeMode) registerEventHandlers() {
	m.trackManager.On("selectedTrackDeviceUpdate", m.onSelectedTrackChange)
	m.trackManager.On("returnTracksNameChange", m.onReturnTracksNameChange)
}

// onSelectedTrackChange réagit quand la piste sélectionnée change
func (m *LiveVolumeMode) onSelectedTrackChange(args []interface{}) {
	log.Println(">>> onSelectedTrackChange: Début. Args:", args)
	log.Println("onSelectedTrackChange appelé avec les arguments:", args)

	if len(args) < 1 {
		log.Println("Arguments insuffisants pour onSelectedTrackChange")
		return
	}

	// Récupérer l'argument selectedTrack
	var selectedTrack *int
	if selectTrackPtr, ok := args[0].(*int); ok {
		selectedTrack = selectTrackPtr
	} else {
		log.Printf("Type d'argument inattendu pour selectedTrack: %T", args[0])
		return
	}

	// Si selectedTrack est nil, on sort
	if selectedTrack == nil {
		log.Println("selectedTrack est nil, sortie de la fonction")
		return
	}

	// Comparer avec la dernière piste sélectionnée
	if m.lastSelectedTrack != nil && *m.lastSelectedTrack == *selectedTrack {
		log.Println("Même piste déjà sélectionnée, aucune action requise")
		return
	}

	// Mettre à jour la dernière piste sélectionnée
	m.lastSelectedTrack = selectedTrack

	// Vérifier que nous avons des données valides
	log.Println(">>> onSelectedTrackChange: Vérification des données valides...")
	totalTracks := m.trackManager.GetTrackCount()

	// Protection contre les valeurs incorrectes de totalTracks
	actualTracksCount := len(m.tracksData)
	if totalTracks < actualTracksCount {
		log.Printf("ATTENTION: GetTrackCount() a retourné %d alors que tracksData contient %d pistes, utilisation du maximum",
			totalTracks, actualTracksCount)
		totalTracks = actualTracksCount
	}

	if totalTracks <= 0 {
		log.Println("Aucune piste disponible, sortie de la fonction")
		return
	}

	// Mettre à jour la page si nécessaire
	if !m.state.IsLocked {
		log.Println(">>> onSelectedTrackChange: Vérification changement de page nécessaire...")
		targetPage := int(math.Floor(float64(*selectedTrack)/float64(SlotsPerPage))) + 1
		maxPage := int(math.Ceil(float64(totalTracks) / float64(SlotsPerPage)))

		log.Printf("targetPage: %d, maxPage: %d, currentPage: %d", targetPage, maxPage, m.state.CurrentPage)

		if targetPage > 0 && targetPage <= maxPage && targetPage != m.state.CurrentPage {
			log.Println(">>> onSelectedTrackChange: Appel de UpdatePageForSelectedTrack...")
			m.UpdatePageForSelectedTrack(*selectedTrack)
		}
	}

	// Mettre à jour UNIQUEMENT la sélection visuelle si nécessaire
	log.Println(">>> onSelectedTrackChange: Mise à jour sélection visuelle...")
	startTrack := (m.state.CurrentPage - 1) * SlotsPerPage
	relativeIndex := *selectedTrack - startTrack

	// Ne mettre à jour la sélection que si elle est différente de la dernière sélection connue
	if relativeIndex >= 0 && relativeIndex < SlotsPerPage {
		slotLetter := string(rune('A' + relativeIndex))
		// Même si le mode n'est pas encore marqué comme actif, forcer la mise à jour
		// de la sélection pour s'assurer qu'elle est visible
		m.displayManager.UpdateSelectedTrack(slotLetter)
		log.Printf("Mise à jour de la sélection: slot %s (piste %d)", slotLetter, *selectedTrack)
	} else {
		// Si la piste n'est pas visible sur la page actuelle, effacer la sélection
		m.displayManager.ClearSelectedTrack()
		log.Printf("Piste %d non visible sur la page actuelle, effacement de la sélection", *selectedTrack)
	}
	log.Println(">>> onSelectedTrackChange: Fin")
}

// onReturnTracksNameChange réagit quand les noms des pistes de retour changent
func (m *LiveVolumeMode) onReturnTracksNameChange(args []interface{}) {
	log.Println("=== Return Tracks Name Change ===")
	log.Println("Mode actuel:", m.state.SubMode)
	log.Println("Mode actif:", m.isActive)

	// Mettre à jour les sous-modes disponibles car le nombre de pistes de retour a peut-être changé
	m.updateAvailableSubmodes()

	// Si nous sommes en mode send et que le mode est actif
	if m.isActive && len(m.state.SubMode) > 1 && m.state.SubMode[0] == SubmodeSendPrefix[0] {
		// Extraire l'index du mode actuel (s0, s1, etc.)
		currentMode := m.state.SubMode[1:]
		sendIndex := -1
		_, err := fmt.Sscanf(currentMode, "%d", &sendIndex)
		if err != nil {
			log.Printf("Erreur de parsing du mode send: %v", err)
			return
		}

		returnTracksName := m.trackManager.GetReturnTracksName()
		log.Println("Return Tracks Names:", returnTracksName)
		log.Println("Send Index:", sendIndex)

		if len(returnTracksName) > sendIndex {
			sendName := returnTracksName[sendIndex]
			log.Println("Send Name avant troncature:", sendName)

			// Tronquer le nom si nécessaire
			if len(sendName) > 27 {
				sendName = fmt.Sprintf("%s...", sendName[:24])
				log.Println("Send Name après troncature:", sendName)
			}

			// Envoyer le message avec priorité
			message := fmt.Sprintf("vs,2,%s", sendName)
			log.Println("Message envoyé:", message)
			m.commManager.SendMessage(message, true)
		}
	}
	log.Println("=== Fin Return Tracks Name Change ===")
}

// EnableLock active le verrouillage de la page
func (m *LiveVolumeMode) EnableLock() {
	if m.state.IsLocked {
		return // Déjà verrouillé
	}
	m.state.IsLocked = true
	m.displayManager.UpdateLockState(true) // Utilise le displayManager pour envoyer le message
	log.Println("Verrouillage de page activé")
}

// DisableLock désactive le verrouillage de la page et rafraîchit l'affichage
func (m *LiveVolumeMode) DisableLock() {
	if !m.state.IsLocked {
		return // Déjà déverrouillé
	}
	m.state.IsLocked = false
	m.displayManager.UpdateLockState(false) // Utilise le displayManager pour envoyer le message
	log.Println("Verrouillage de page désactivé")

	// Rafraîchir l'affichage en forçant une mise à jour pour la piste sélectionnée
	if m.lastSelectedTrack != nil {
		// Mettre à jour la page si nécessaire
		log.Println(">>> DisableLock: Vérification changement de page nécessaire...")
		totalTracks := m.trackManager.GetTrackCount()
		if totalTracks > 0 {
			targetPage := int(math.Floor(float64(*m.lastSelectedTrack)/float64(SlotsPerPage))) + 1
			maxPage := int(math.Ceil(float64(totalTracks) / float64(SlotsPerPage)))
			log.Printf("targetPage: %d, maxPage: %d, currentPage: %d", targetPage, maxPage, m.state.CurrentPage)

			if targetPage > 0 && targetPage <= maxPage && targetPage != m.state.CurrentPage {
				log.Println(">>> DisableLock: Appel de UpdatePageForSelectedTrack...")
				m.UpdatePageForSelectedTrack(*m.lastSelectedTrack) // UpdatePageForSelectedTrack met à jour la page et l'affichage
			} else {
				// Si la page n'a pas changé, il faut quand même rafraîchir l'affichage de la sélection et les slots vides
				m.displayManager.ClearEmptySlots()
				m.updateSelectedTrackDisplay(*m.lastSelectedTrack)
			}
		} else {
			m.displayManager.ClearEmptySlots()
			m.displayManager.ClearSelectedTrack()
		}

	} else {
		// Pas de piste sélectionnée, on nettoie juste les slots et la sélection
		m.displayManager.ClearEmptySlots()
		m.displayManager.ClearSelectedTrack()
	}

}

// updateSelectedTrackDisplay met à jour l'affichage de la piste sélectionnée via le displayManager
func (m *LiveVolumeMode) updateSelectedTrackDisplay(selectedTrack int) {
	startTrack := (m.state.CurrentPage - 1) * SlotsPerPage
	relativeIndex := selectedTrack - startTrack

	if relativeIndex >= 0 && relativeIndex < SlotsPerPage {
		slotLetter := string(rune('A' + relativeIndex))
		m.displayManager.UpdateSelectedTrack(slotLetter)
		log.Printf("Piste %d visible, affichage %s", selectedTrack, slotLetter)
	} else {
		m.displayManager.ClearSelectedTrack()
		log.Printf("Piste %d non visible sur la page actuelle", selectedTrack)
	}
}

// La méthode HandleHardwareEvent est maintenant définie dans liveVolume_hardwareHandlers.go

// Les fonctions suivantes (handleTouchEvent, handleButtonEvent, handleEncoderEvent)
// gèrent la logique spécifique du mode Volume pour chaque type d'événement

// ... (Définitions de handleTouchEvent, handleButtonEvent, handleEncoderEvent, etc.)

// Suppression des méthodes dupliquées registerOSCHandlers et UpdatePageForSelectedTrack
// Ces méthodes sont définies respectivement dans:
// - liveVolume_oscHandlers.go
// - liveVolume_pageHandlers.go

// === Suppression de l'implémentation ButtonMode ===
// L'implémentation des boutons est maintenant dans liveVolume_hardwareHandlers.go
