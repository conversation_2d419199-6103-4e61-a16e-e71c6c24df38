package liveVolumeMode

import (
	"fmt"
	"log"
	"math"
	"time"
)

// handleVolumeGet gère les messages de volume
func (m *LiveVolumeMode) handleVolumeGet(trackIndex int, value float32, slotLetter string) {
	// Convertir float32 en float64 pour la compatibilité avec TrackData
	m.tracksData[trackIndex].Volume = float64(value)

	// N'afficher le volume que si nous sommes en mode volume
	if m.state.SubMode == SubmodeVolume {
		// Conserver la précision sans arrondir afin d'éviter les sauts de 0,4 dB
		volumePercent := float64(value) * 100 // Ex. 87,9 pour 0,879
		m.displayManager.UpdateVolume(slotLetter, volumePercent, float64(value))
	}
	log.Printf("Volume update for track %d: %f", trackIndex, value)
}

// handlePanGet gère les messages de pan
func (m *LiveVolumeMode) handlePanGet(trackIndex int, value float32, slotLetter string) {
	// Convertir float32 en float64 pour la compatibilité avec TrackData
	m.tracksData[trackIndex].Pan = float64(value)

	// N'afficher le pan que si nous sommes en mode pan
	if m.state.SubMode == SubmodePan {
		panValue := math.Round((float64(value) + 1) * 50) // Convertir de [-1,1] à [0,100]
		m.displayManager.UpdatePan(slotLetter, panValue)
	}
	log.Printf("Pan update for track %d: %f", trackIndex, value)
}

// handleColorGet gère les messages de couleur
func (m *LiveVolumeMode) handleColorGet(trackIndex int, value string, slotLetter string) {
	m.tracksData[trackIndex].Color = value
	m.displayManager.UpdateColor(slotLetter, value)
	log.Printf("Color update for track %d: %s", trackIndex, value)
}

// handleNameGet gère les messages de nom
func (m *LiveVolumeMode) handleNameGet(trackIndex int, value string, slotLetter string) {
	m.tracksData[trackIndex].Name = value
	m.displayManager.UpdateName(slotLetter, value)
	log.Printf("Name update for track %d: %s", trackIndex, value)
}

// handleMuteStateGet gère les messages de l'état du mute
func (m *LiveVolumeMode) handleMuteStateGet(trackIndex int, value int, slotLetter string) {
	isMainTrack := trackIndex == m.trackManager.GetTrackCount()-1
	muteValue := value
	if isMainTrack {
		muteValue = 2
	}

	m.tracksData[trackIndex].MuteState = muteValue
	m.displayManager.UpdateMuteState(slotLetter, muteValue)
	log.Printf("Mute state update for track %d: %d", trackIndex, muteValue)
}

// handleArmGet gère les messages d'arm
func (m *LiveVolumeMode) handleArmGet(trackIndex int, value bool, slotLetter string) {
	isMainTrack := trackIndex == m.trackManager.GetTrackCount()-1
	isReturnTrack := trackIndex >= m.trackManager.GetTrackCount()-m.trackManager.GetReturnTrackCount()-1 &&
		trackIndex < m.trackManager.GetTrackCount()-1
	isTrackFoldable := m.tracksData[trackIndex].IsFoldable

	armValue := 0
	if isMainTrack || isReturnTrack || isTrackFoldable {
		armValue = 2
	} else if value {
		armValue = 1
	}

	m.tracksData[trackIndex].Arm = armValue
	m.displayManager.UpdateArm(slotLetter, armValue != 0)
	log.Printf("Arm update for track %d: %v", trackIndex, value)
}

// handleIsFoldableGet gère les messages de pliage
func (m *LiveVolumeMode) handleIsFoldableGet(trackIndex int, args []interface{}, slotLetter string) {
	if len(args) < 3 {
		log.Printf("handleIsFoldableGet: not enough arguments")
		return
	}

	isFoldable, ok1 := args[0].(float32)
	foldState, ok2 := args[1].(float32)
	isGrouped, ok3 := args[2].(float32)

	if !ok1 || !ok2 || !ok3 {
		log.Printf("handleIsFoldableGet: invalid argument types")
		return
	}

	trackData := m.tracksData[trackIndex]
	trackData.IsFoldable = isFoldable != 0
	trackData.FoldState = foldState != 0
	trackData.IsGrouped = isGrouped != 0

	// Mise à jour de l'affichage via le display manager
	m.displayManager.UpdateSlotForMode(slotLetter, trackData, m.state.SubMode)

	log.Printf("Is foldable update for track %d: foldable=%v, state=%v, grouped=%v",
		trackIndex, isFoldable != 0, foldState != 0, isGrouped != 0)
}

// handleSendsGet gère les messages de sends
func (m *LiveVolumeMode) handleSendsGet(trackIndex int, sendIndex int, value float32, slotLetter string) {
	log.Printf("Début handleSendsGet - Track: %d, Send: %d, Value: %f, Slot: %s", trackIndex, sendIndex, value, slotLetter)

	// Vérifier si la piste existe
	if _, exists := m.tracksData[trackIndex]; !exists {
		log.Printf("Création d'une nouvelle piste pour l'index %d", trackIndex)
		m.tracksData[trackIndex] = NewTrackData()
	}

	// S'assurer que le tableau Sends existe et a la bonne taille
	currentSends := m.tracksData[trackIndex].Sends
	if len(currentSends) <= sendIndex {
		log.Printf("Redimensionnement du tableau sends de %d à %d", len(currentSends), sendIndex+1)
		newSends := make([]float64, sendIndex+1)
		copy(newSends, currentSends)
		m.tracksData[trackIndex].Sends = newSends
	}

	// Mettre à jour la valeur du send
	m.tracksData[trackIndex].Sends[sendIndex] = float64(value)
	log.Printf("Valeur du send mise à jour: sends[%d] = %f", sendIndex, float64(value))

	// Vérifier si nous sommes dans un mode send
	if len(m.state.SubMode) > 1 && m.state.SubMode[0] == SubmodeSendPrefix[0] {
		// Extraire l'index du mode actuel (s0, s1, etc.)
		currentMode := m.state.SubMode[1:]
		currentSendIndex := -1
		_, err := fmt.Sscanf(currentMode, "%d", &currentSendIndex)

		log.Printf("Mode send actuel: %s (index %d), sendIndex reçu: %d",
			m.state.SubMode, currentSendIndex, sendIndex)

		// Mettre à jour l'affichage si nous sommes sur le bon send
		if err == nil && currentSendIndex == sendIndex {
			log.Printf("Mise à jour de l'affichage pour le slot %s avec la valeur %f",
				slotLetter, float64(value))
			m.displayManager.UpdateSend(slotLetter, sendIndex, float64(value))
		} else {
			if err != nil {
				log.Printf("Erreur de parsing du mode send: %v", err)
			}
			log.Printf("Pas de mise à jour d'affichage - Send index différent (mode: %d, reçu: %d)",
				currentSendIndex, sendIndex)
		}
	} else {
		log.Printf("Pas en mode send (mode actuel: %s)", m.state.SubMode)
	}

	// Mettre à jour les sous-modes disponibles
	m.updateAvailableSubmodes()
	log.Printf("Fin handleSendsGet - Track: %d, Send: %d, Value: %f", trackIndex, sendIndex, value)
}

// updateTrackData met à jour les données d'une piste
func (m *LiveVolumeMode) updateTrackData(trackIndex int, paramName string, args []interface{}) {
	// S'assurer que la piste existe dans tracksData
	if _, exists := m.tracksData[trackIndex]; !exists {
		m.tracksData[trackIndex] = NewTrackData()
	}

	// Mise à jour selon le type de paramètre
	switch paramName {
	case "volume":
		if value, ok := args[2].(float32); ok {
			m.tracksData[trackIndex].Volume = float64(value)
		}
	case "pan":
		if value, ok := args[2].(float32); ok {
			m.tracksData[trackIndex].Pan = float64(value)
		}
	case "color":
		if value, ok := args[2].(string); ok {
			m.tracksData[trackIndex].Color = value
		}
	case "name":
		if value, ok := args[2].(string); ok {
			m.tracksData[trackIndex].Name = value
		}
	case "mute_state":
		if value, ok := args[2].(float32); ok {
			m.tracksData[trackIndex].MuteState = int(value)
		}
	case "arm":
		if value, ok := args[2].(bool); ok {
			if value {
				m.tracksData[trackIndex].Arm = 1
			} else {
				m.tracksData[trackIndex].Arm = 0
			}
		}
	case "is_foldable":
		if len(args) >= 5 {
			if isFoldable, ok1 := args[2].(float32); ok1 {
				if foldState, ok2 := args[3].(float32); ok2 {
					if isGrouped, ok3 := args[4].(float32); ok3 {
						m.tracksData[trackIndex].IsFoldable = isFoldable != 0
						m.tracksData[trackIndex].FoldState = foldState != 0
						m.tracksData[trackIndex].IsGrouped = isGrouped != 0
					}
				}
			}
		}
	case "sends":
		if sendIndex, ok := args[2].(int32); ok {
			if value, ok := args[3].(float32); ok {
				// S'assurer que le tableau Sends existe et a la bonne taille
				if len(m.tracksData[trackIndex].Sends) <= int(sendIndex) {
					newSends := make([]float64, int(sendIndex)+1)
					copy(newSends, m.tracksData[trackIndex].Sends)
					m.tracksData[trackIndex].Sends = newSends
				}
				m.tracksData[trackIndex].Sends[int(sendIndex)] = float64(value)
			}
		}
	}
}

// handleGetMessage traite tous les messages OSC de type get
func (m *LiveVolumeMode) handleGetMessage(args []interface{}) {
	if len(args) < 3 {
		log.Printf("handleGetMessage: not enough arguments")
		return
	}

	// Le premier argument est l'adresse OSC
	address, ok := args[0].(string)
	if !ok {
		log.Printf("handleGetMessage: first argument is not a string")
		return
	}

	// Le deuxième argument est l'index de la piste
	var trackIndex int
	switch v := args[1].(type) {
	case int32:
		trackIndex = int(v)
	case float32:
		trackIndex = int(v)
	default:
		log.Printf("handleGetMessage: trackIndex is not a number (got %T)", args[1])
		return
	}

	// Calcul des indices pour l'affichage
	startTrack := (m.state.CurrentPage - 1) * SlotsPerPage
	endTrack := startTrack + SlotsPerPage

	// Si la piste n'est pas visible sur la page actuelle, on ignore
	if trackIndex < startTrack || trackIndex >= endTrack {
		return
	}

	// S'assurer que la piste existe dans tracksData
	if _, exists := m.tracksData[trackIndex]; !exists {
		m.tracksData[trackIndex] = NewTrackData()
	}

	// Calcul de la lettre du slot
	slotLetter := string(rune('A' + (trackIndex - startTrack)))

	// Traitement selon le type de message
	switch address {
	case volumeModeGetVolumeAddress:
		value, ok := args[2].(float32)
		if !ok {
			log.Printf("handleGetMessage: volume value is not a number")
			return
		}
		m.handleVolumeGet(trackIndex, value, slotLetter)

	case volumeModeGetPanAddress:
		value, ok := args[2].(float32)
		if !ok {
			log.Printf("handleGetMessage: pan value is not a number")
			return
		}
		m.handlePanGet(trackIndex, value, slotLetter)

	case volumeModeGetColorAddress:
		var colorValue int32
		switch v := args[2].(type) {
		case int32:
			colorValue = v
		case float32:
			colorValue = int32(v)
		default:
			log.Printf("handleGetMessage: color value is not a number (got %T)", args[2])
			return
		}
		colorHex := fmt.Sprintf("%06x", colorValue)
		m.handleColorGet(trackIndex, colorHex, slotLetter)

	case volumeModeGetNameAddress:
		value, ok := args[2].(string)
		if !ok {
			log.Printf("handleGetMessage: name value is not a string")
			return
		}
		m.handleNameGet(trackIndex, value, slotLetter)

	case volumeModeGetMuteStateAddress:
		var value float32
		switch v := args[2].(type) {
		case float32:
			value = v
		case int32:
			value = float32(v)
		default:
			log.Printf("handleGetMessage: mute state value is not a number (got %T)", args[2])
			return
		}
		m.handleMuteStateGet(trackIndex, int(value), slotLetter)

	case volumeModeGetArmAddress:
		if len(args) < 3 {
			log.Printf("handleGetMessage: not enough arguments for arm (got %d)", len(args))
			for i, arg := range args {
				log.Printf("Arg %d: %T %v", i, arg, arg)
			}
			return
		}
		var valueBool bool
		switch v := args[2].(type) {
		case bool:
			valueBool = v
		case int32:
			valueBool = v != 0
		case float32:
			valueBool = v != 0
		default:
			log.Printf("handleGetMessage: arm value is not a bool or number (got %T)", args[2])
			return
		}
		m.handleArmGet(trackIndex, valueBool, slotLetter)

	case volumeModeGetIsFoldableAddress:
		if len(args) < 5 {
			log.Printf("handleGetMessage: not enough arguments for is_foldable")
			return
		}
		m.handleIsFoldableGet(trackIndex, args[2:], slotLetter)

	case volumeModeGetSendsAddress:
		sendIndex, ok := args[2].(int32)
		if !ok {
			log.Printf("handleGetMessage: sendIndex is not a number")
			return
		}
		value, ok := args[3].(float32)
		if !ok {
			log.Printf("handleGetMessage: value is not a number")
			return
		}
		m.handleSendsGet(trackIndex, int(sendIndex), value, slotLetter)

	default:
		log.Printf("handleGetMessage: unknown address %s", address)
		return
	}

	// Mise à jour des données stockées
	paramName := ""
	switch address {
	case volumeModeGetVolumeAddress:
		paramName = "volume"
	case volumeModeGetPanAddress:
		paramName = "pan"
	case volumeModeGetColorAddress:
		paramName = "color"
	case volumeModeGetNameAddress:
		paramName = "name"
	case volumeModeGetMuteStateAddress:
		paramName = "mute_state"
	case volumeModeGetArmAddress:
		paramName = "arm"
	case volumeModeGetIsFoldableAddress:
		paramName = "is_foldable"
	case volumeModeGetSendsAddress:
		paramName = "sends"
	}

	if paramName != "" {
		m.updateTrackData(trackIndex, paramName, args)
	}
}

// ========================= STRUCTURE HANDLERS SIMPLIFIÉS =========================

// resetGraceTimer relance le timer de grâce (300 ms)
func (m *LiveVolumeMode) resetGraceTimer() {
	if m.graceTimer != nil {
		m.graceTimer.Stop()
	}
	m.graceTimer = time.AfterFunc(300*time.Millisecond, func() {
		m.finalizeStructure()
	})
}

// handleStructureBegin : initialise une nouvelle structure
func (m *LiveVolumeMode) handleStructureBegin(args []interface{}) {
	if len(args) < 1 {
		log.Printf("Structure begin: arguments insuffisants")
		return
	}

	// Récupérer le nombre de pistes depuis l'argument
	numTracks, ok := args[0].(int32)
	if !ok {
		log.Printf("Structure begin: nombre de pistes invalide")
		return
	}

	m.structureMutex.Lock()
	defer m.structureMutex.Unlock()

	// Si on a déjà des pistes en attente, on les garde
	if m.pendingTracksData == nil {
		m.pendingTracksData = make(map[int]*TrackData)
	}
	m.pendingTrackCount = int(numTracks)
	log.Printf("Nouvelle structure initialisée: %d pistes attendues (pistes déjà reçues: %d)",
		m.pendingTrackCount, len(m.pendingTracksData))
}

// handleStructureTrack : reçoit une piste
func (m *LiveVolumeMode) handleStructureTrack(args []interface{}) {
	if len(args) < 11 {
		log.Printf("Structure track: arguments insuffisants %v", args)
		return
	}

	idx, ok := args[0].(int32)
	if !ok {
		log.Printf("Structure track: index invalide %v", args[0])
		return
	}
	trackIndex := int(idx)

	m.structureMutex.Lock()
	defer m.structureMutex.Unlock()

	// Créer la map si elle n'existe pas
	if m.pendingTracksData == nil {
		m.pendingTracksData = make(map[int]*TrackData)
	}

	// Créer TrackData
	td := NewTrackData()
	td.Name, _ = args[2].(string)
	td.IsFoldable, _ = args[3].(bool)
	td.FoldState, _ = args[4].(bool)
	td.IsGrouped, _ = args[5].(bool)
	if ms, ok := args[6].(int32); ok {
		td.MuteState = int(ms)
	}
	if arm, ok := args[7].(int32); ok {
		td.Arm = int(arm)
	}
	if col, ok := args[8].(int32); ok {
		td.Color = fmt.Sprintf("%06x", col)
	}
	if vol, ok := args[9].(float32); ok {
		td.Volume = float64(vol)
	}
	if pan, ok := args[10].(float32); ok {
		td.Pan = float64(pan)
	}
	if len(args) > 11 {
		// Arg 11 est un tableau d'interface{} contenant les valeurs des sends
		if sendsArray, ok := args[11].([]interface{}); ok {
			log.Printf("DEBUG Structure: Traitement de %d sends pour la piste %d", len(sendsArray), trackIndex)
			for i, sendVal := range sendsArray {
				if s, ok := sendVal.(float32); ok {
					td.Sends = append(td.Sends, float64(s))
					log.Printf("DEBUG Structure: Send %d = %f", i, float64(s))
				} else {
					log.Printf("DEBUG Structure: Type de send invalide: %T", sendVal)
				}
			}
		} else {
			log.Printf("DEBUG Structure: Format de sends invalide: %T", args[11])
		}
	}
	m.pendingTracksData[trackIndex] = td
	log.Printf("DEBUG Structure: Ajout piste %d (%s) - Total actuel: %d/%d",
		trackIndex, td.Name, len(m.pendingTracksData), m.pendingTrackCount)
}

// handleStructureEnd : finalise la structure si elle est complète et valide
func (m *LiveVolumeMode) handleStructureEnd(args []interface{}) {
	log.Println("Structure end reçu")
	m.structureMutex.Lock()
	defer m.structureMutex.Unlock()

	// Si on n'a pas encore reçu le begin, on ne peut pas finaliser
	if m.pendingTrackCount == 0 {
		log.Println("Structure end: nombre de pistes attendues non défini, on attend")
		return
	}

	// Afficher toutes les pistes reçues pour debug
	log.Printf("DEBUG Structure - Pistes reçues:")
	for idx, track := range m.pendingTracksData {
		log.Printf("  - Piste %d: %s", idx, track.Name)
	}

	// On finalise avec ce qu'on a
	m.tracksData = m.pendingTracksData
	m.pendingTracksData = nil
	log.Printf("Structure finalisée avec %d/%d pistes", len(m.tracksData), m.pendingTrackCount)

	// Mettre à jour UI
	m.updateAvailableSubmodes()
	m.displayManager.SendFullUpdate(m.tracksData)
	m.displayManager.ClearEmptySlots()
}

// finalizeStructure applique la nouvelle map si disponible
func (m *LiveVolumeMode) finalizeStructure() {
	m.structureMutex.Lock()
	defer m.structureMutex.Unlock()
	if m.pendingTracksData == nil {
		return
	}
	m.tracksData = m.pendingTracksData
	m.pendingTracksData = nil
	m.pendingTrackCount = 0

	log.Printf("Structure finalisée (%d pistes)", len(m.tracksData))
	// Mettre à jour UI
	m.updateAvailableSubmodes()
	m.displayManager.SendFullUpdate(m.tracksData)
	m.displayManager.ClearEmptySlots()
}

// ========================= ENREGISTREMENT DES HANDLERS =========================
func (m *LiveVolumeMode) registerOSCHandlers() {
	// Get handlers (inchangés)
	m.RegisterHandler(volumeModeGetVolumeAddress, func(a []interface{}) { m.handleGetMessage(append([]interface{}{volumeModeGetVolumeAddress}, a...)) }, "vol")
	m.RegisterHandler(volumeModeGetPanAddress, func(a []interface{}) { m.handleGetMessage(append([]interface{}{volumeModeGetPanAddress}, a...)) }, "pan")
	m.RegisterHandler(volumeModeGetColorAddress, func(a []interface{}) { m.handleGetMessage(append([]interface{}{volumeModeGetColorAddress}, a...)) }, "col")
	m.RegisterHandler(volumeModeGetNameAddress, func(a []interface{}) { m.handleGetMessage(append([]interface{}{volumeModeGetNameAddress}, a...)) }, "name")
	m.RegisterHandler(volumeModeGetSendsAddress, func(a []interface{}) { m.handleGetMessage(append([]interface{}{volumeModeGetSendsAddress}, a...)) }, "sends")
	m.RegisterHandler(volumeModeGetIsFoldableAddress, func(a []interface{}) { m.handleGetMessage(append([]interface{}{volumeModeGetIsFoldableAddress}, a...)) }, "fold")
	m.RegisterHandler(volumeModeGetMuteStateAddress, func(a []interface{}) { m.handleGetMessage(append([]interface{}{volumeModeGetMuteStateAddress}, a...)) }, "mute")
	m.RegisterHandler(volumeModeGetArmAddress, func(a []interface{}) { m.handleGetMessage(append([]interface{}{volumeModeGetArmAddress}, a...)) }, "arm")

	// Structure handlers
	m.RegisterHandler(volumeModeStructureAddress, m.handleStructureBegin, "begin struct")
	m.RegisterHandler(volumeModeStructureTrackAddress, m.handleStructureTrack, "track struct")
	m.RegisterHandler(volumeModeStructureEndAddress, m.handleStructureEnd, "end struct")
}
