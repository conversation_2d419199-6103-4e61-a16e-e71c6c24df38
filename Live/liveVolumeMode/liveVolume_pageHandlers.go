package liveVolumeMode

import (
	"log"
	"math"
	"time"
)

// PageChangeState maintient l'état des changements de page
type PageChangeState struct {
	LastPageChangeTime time.Time
	IsFirstPageChange  bool
	PageChangeTimer    *time.Timer
}

// NewPageChangeState crée un nouvel état de changement de page
func NewPageChangeState() *PageChangeState {
	return &PageChangeState{
		LastPageChangeTime: time.Time{},
		IsFirstPageChange:  true,
		PageChangeTimer:    nil,
	}
}

// SetPage change la page courante
func (m *LiveVolumeMode) SetPage(page int) {
	totalTracks := m.trackManager.GetTrackCount()
	maxPage := int(math.Ceil(float64(totalTracks) / float64(SlotsPerPage)))
	currentTime := time.Now()

	if page >= 1 && page <= maxPage && page != m.state.CurrentPage {
		// Annuler le timer existant s'il y en a un
		if m.pageChangeState.PageChangeTimer != nil {
			m.pageChangeState.PageChangeTimer.Stop()
			m.pageChangeState.PageChangeTimer = nil
		}

		// Nettoyer le cache du displayManager lors d'un changement de page
		m.displayManager.ClearCache()

		// Mettre à jour la page immédiatement
		m.state.CurrentPage = page
		m.displayManager.UpdatePage(page)

		// Si c'est le premier changement de page ou si plus de 200ms se sont écoulées
		if m.pageChangeState.IsFirstPageChange || currentTime.Sub(m.pageChangeState.LastPageChangeTime) > 200*time.Millisecond {
			// Exécuter la mise à jour complète immédiatement
			m.completePageUpdate(page)
			m.pageChangeState.IsFirstPageChange = false
		} else {
			// Programmer la mise à jour complète après 150ms
			m.pageChangeState.PageChangeTimer = time.AfterFunc(150*time.Millisecond, func() {
				m.completePageUpdate(page)
				// Réinitialiser le flag de premier appel après le délai
				m.pageChangeState.IsFirstPageChange = true
			})
		}

		// Mettre à jour le timestamp du dernier changement
		m.pageChangeState.LastPageChangeTime = currentTime
	} else {
		log.Printf("Impossible de changer la page: page=%d, maxPage=%d, currentPage=%d",
			page, maxPage, m.state.CurrentPage)
	}
}

// CompletePageUpdate finalise la mise à jour de la page
func (m *LiveVolumeMode) completePageUpdate(page int) {
	// Récupérer la piste sélectionnée
	var selectedTrack int
	if m.lastSelectedTrack != nil {
		selectedTrack = *m.lastSelectedTrack
	} else {
		selectedTrack = 0
	}

	startTrack := (page - 1) * SlotsPerPage
	relativeIndex := selectedTrack - startTrack

	if relativeIndex >= 0 && relativeIndex < SlotsPerPage {
		slotLetter := string(rune('A' + relativeIndex))
		m.displayManager.UpdateSelectedTrack(slotLetter)
	} else {
		m.displayManager.ClearSelectedTrack()
	}

	m.displayManager.SendFullUpdate(m.tracksData)
	m.displayManager.ClearEmptySlots()
}

// UpdatePageForSelectedTrack met à jour la page en fonction de la piste sélectionnée
func (m *LiveVolumeMode) UpdatePageForSelectedTrack(selectedTrack int) {
	log.Println(">>> UpdatePageForSelectedTrack: Début. SelectedTrack:", selectedTrack)

	// Protection contre les valeurs incorrectes du nombre de pistes
	totalTracksFromManager := m.trackManager.GetTrackCount()
	actualTracksCount := len(m.tracksData)
	totalTracks := totalTracksFromManager

	if totalTracksFromManager < actualTracksCount {
		log.Printf("ATTENTION dans UpdatePageForSelectedTrack: GetTrackCount() a retourné %d alors que tracksData contient %d pistes, utilisation du maximum",
			totalTracksFromManager, actualTracksCount)
		totalTracks = actualTracksCount
	}

	maxPage := int(math.Ceil(float64(totalTracks) / float64(SlotsPerPage)))
	page := int(math.Floor(float64(selectedTrack)/float64(SlotsPerPage))) + 1

	log.Printf("Mise à jour de la page pour la piste %d: totalTracks=%d, maxPage=%d, calculatedPage=%d, currentPage=%d",
		selectedTrack, totalTracks, maxPage, page, m.state.CurrentPage)

	if page > 0 && page <= maxPage && page != m.state.CurrentPage {
		// Mettre à jour l'état et la variable currentPage
		m.state.CurrentPage = page

		// Mettre à jour l'affichage de la page
		m.displayManager.UpdatePage(page)
		log.Printf("Page mise à jour: %d/%d", page, maxPage)

		// Vérifier si nous avons des données pour les pistes de cette page
		startTrack := (page - 1) * SlotsPerPage
		endTrack := startTrack + SlotsPerPage

		hasDataForPage := false
		for i := startTrack; i < endTrack && i < totalTracks; i++ {
			if _, exists := m.tracksData[i]; exists {
				hasDataForPage = true
				break
			}
		}

		if hasDataForPage {
			// Forcer une mise à jour complète uniquement si nous avons des données
			log.Println(">>> UpdatePageForSelectedTrack: Appel de SendFullUpdate...")
			m.displayManager.SendFullUpdate(m.tracksData)
			log.Println(">>> UpdatePageForSelectedTrack: SendFullUpdate terminé.")
		} else {
			log.Println(">>> UpdatePageForSelectedTrack: Pas de données disponibles pour cette page, attente de données...")
			// On pourrait ajouter ici un mécanisme pour demander les données manquantes
			// Mais on laisse l'affichage dans son état actuel pour l'instant
		}

		// Appel à ClearEmptySlots uniquement si nous avons des données fiables sur le nombre de pistes
		if actualTracksCount > 0 || totalTracksFromManager > 0 {
			log.Println(">>> UpdatePageForSelectedTrack: Appel de ClearEmptySlots...")
			m.displayManager.ClearEmptySlots()
			log.Println(">>> UpdatePageForSelectedTrack: ClearEmptySlots terminé.")
		} else {
			log.Println(">>> UpdatePageForSelectedTrack: Évitement de ClearEmptySlots car nombre de pistes non fiable")
		}

		// Mettre à jour l'affichage de la sélection
		startTrack = (page - 1) * SlotsPerPage
		relativeIndex := selectedTrack - startTrack

		if relativeIndex >= 0 && relativeIndex < SlotsPerPage {
			slotLetter := string(rune('A' + relativeIndex))
			log.Printf("Mise à jour de la sélection après changement de page: slot %s", slotLetter)
			m.commManager.SendMessage("st"+slotLetter, m.isActive)
		} else {
			log.Println("La piste sélectionnée n'est pas visible sur la nouvelle page")
			m.commManager.SendMessage("st0", m.isActive)
		}
	} else {
		log.Printf("Pas de changement de page nécessaire: page=%d, maxPage=%d, currentPage=%d",
			page, maxPage, m.state.CurrentPage)
	}
	log.Println(">>> UpdatePageForSelectedTrack: Fin")
}

// PageUp passe à la page suivante
func (m *LiveVolumeMode) PageUp() {
	totalTracks := m.trackManager.GetTrackCount()
	maxPage := int(math.Ceil(float64(totalTracks) / float64(SlotsPerPage)))

	if m.state.CurrentPage < maxPage {
		m.SetPage(m.state.CurrentPage + 1)
	} else {
		log.Println("Déjà sur la dernière page")
	}
}

// PageDown passe à la page précédente
func (m *LiveVolumeMode) PageDown() {
	if m.state.CurrentPage > 1 {
		m.SetPage(m.state.CurrentPage - 1)
	} else {
		log.Println("Déjà sur la première page")
	}
}

// GetPageInfo renvoie les informations sur la page actuelle
func (m *LiveVolumeMode) GetPageInfo() map[string]interface{} {
	totalTracks := m.trackManager.GetTrackCount()
	maxPage := int(math.Ceil(float64(totalTracks) / float64(SlotsPerPage)))
	startTrack := (m.state.CurrentPage - 1) * SlotsPerPage
	endTrack := int(math.Min(float64(startTrack+SlotsPerPage), float64(totalTracks)))

	return map[string]interface{}{
		"currentPage":     m.state.CurrentPage,
		"totalPages":      maxPage,
		"displayedTracks": []int{startTrack + 1, endTrack},
		"totalTracks":     totalTracks,
	}
}
