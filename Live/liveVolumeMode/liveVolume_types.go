package liveVolumeMode

// Configuration constants
const (
	SlotsPerPage = 8
)

// Parameter types for learn mode
const (
	ParamTypeVolume = 1
	ParamTypePan    = 2
	ParamTypeSend   = 3
)

// Constantes pour les adresses OSC
const (
	volumeModeGetVolumeAddress      = "/live/volumeMode/get/volume"
	volumeModeGetPanAddress         = "/live/volumeMode/get/pan"
	volumeModeGetColorAddress       = "/live/volumeMode/get/color"
	volumeModeGetNameAddress        = "/live/volumeMode/get/name"
	volumeModeGetSendsAddress       = "/live/volumeMode/get/send"
	volumeModeGetIsFoldableAddress  = "/live/volumeMode/get/is_foldable"
	volumeModeGetMuteStateAddress   = "/live/volumeMode/get/mute_state"
	volumeModeGetArmAddress         = "/live/volumeMode/get/arm"
	volumeModeStructureAddress      = "/live/volumeMode/structure/begin"
	volumeModeStructureTrackAddress = "/live/volumeMode/structure/track"
	volumeModeStructureEndAddress   = "/live/volumeMode/structure/end"
)

// Submodes constants
const (
	SubmodeVolume     = "vo"
	SubmodePan        = "pa"
	SubmodeSendPrefix = "s"
)

// TrackData représente les données d'une piste
type TrackData struct {
	IsFoldable bool
	FoldState  bool
	IsGrouped  bool
	MuteState  int
	Arm        int
	Color      string
	Name       string
	Volume     float64
	Pan        float64
	Sends      []float64
}

// LiveVolumeModeState représente l'état du mode volume
type LiveVolumeModeState struct {
	IsLocked          bool
	SubMode           string
	AvailableSubmodes []string
	CurrentPage       int

	// Buffers pour les deltas d'encodeurs
	EncoderBuffers    map[int]float64 // Map des buffers par index d'encodeur
}

// TrackInfo représente les informations temporaires d'une piste pendant la réception de la structure
type TrackInfo struct {
	Index      int
	Type       string
	Name       string
	IsFoldable bool
	FoldState  bool
	IsGrouped  bool
	MuteState  int
	Arm        int
	Color      string
	Volume     float64
	Pan        float64
	Sends      []float64
}

// StructureBuffer représente le buffer temporaire pour stocker la structure pendant sa réception
type StructureBuffer struct {
	Tracks []TrackInfo
}

// LearnData représente les données d'apprentissage pour le mode learn
type LearnData struct {
	ParamType   int
	TrackIndex  int
	SendIndex   *int // Pointeur pour permettre la valeur nil
	DeviceIndex *int // Pointeur pour permettre la valeur nil
	ParamIndex  *int // Pointeur pour permettre la valeur nil
}

// NewTrackData crée une nouvelle instance de TrackData avec les valeurs par défaut
func NewTrackData() *TrackData {
	return &TrackData{
		IsFoldable: false,
		FoldState:  false,
		IsGrouped:  false,
		MuteState:  0,
		Arm:        0,
		Color:      "000000",
		Name:       "",
		Volume:     0,
		Pan:        0,
		Sends:      make([]float64, 0),
	}
}

// NewStructureBuffer crée une nouvelle instance de StructureBuffer
func NewStructureBuffer() *StructureBuffer {
	return &StructureBuffer{
		Tracks: make([]TrackInfo, 0),
	}
}
