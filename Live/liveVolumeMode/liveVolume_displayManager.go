package liveVolumeMode

import (
	"fmt"
	"log"
	"math"
	communication "oscbridge/Communication"
	live "oscbridge/Live"
	"oscbridge/Live/utils"
	"strconv"
	"time"
)

// LiveVolumeDisplayManager gère l'affichage du mode volume
type LiveVolumeDisplayManager struct {
	communicationManager *communication.CommunicationManager
	liveTrackManager     *live.LiveTrackManager
	state                *LiveVolumeModeState
	slotsCache           map[string]string
	volumeConverter      *utils.VolumeConverter
	volumeSendConverter  *utils.VolumeSendConverter
	parentMode           *LiveVolumeMode
	clearEmptySlotsTimer *time.Timer
}

// NewLiveVolumeDisplayManager crée une nouvelle instance de LiveVolumeDisplayManager
func NewLiveVolumeDisplayManager(commManager *communication.CommunicationManager, trackManager *live.LiveTrackManager, state *LiveVolumeModeState, parentMode *LiveVolumeMode) *LiveVolumeDisplayManager {
	return &LiveVolumeDisplayManager{
		communicationManager: commManager,
		liveTrackManager:     trackManager,
		state:                state,
		slotsCache:           make(map[string]string),
		volumeConverter:      utils.NewVolumeConverter(),
		volumeSendConverter:  utils.NewVolumeSendConverter(),
		parentMode:           parentMode,
	}
}

// UpdateSlotForMode met à jour un slot selon le sous-mode actif
func (m *LiveVolumeDisplayManager) UpdateSlotForMode(slotLetter string, trackData *TrackData, subMode string) {
	if trackData == nil {
		return
	}

	// Générer le nouveau message
	newMessage := m.formatTrackData(trackData, subMode)

	// Vérifier si le slot a changé
	cacheKey := fmt.Sprintf("%s_%s", slotLetter, subMode)
	cachedMessage, exists := m.slotsCache[cacheKey]

	// Ne mettre à jour que si le message est différent
	if !exists || cachedMessage != newMessage {
		slMessage := fmt.Sprintf("sl%s%s", slotLetter, newMessage)
		// Forcer l'envoi avec priorité pour s'assurer que les mises à jour sont visibles
		m.communicationManager.SendMessage(slMessage, true)
		// Mettre à jour le cache
		m.slotsCache[cacheKey] = newMessage
	}
}

// formatTrackData formate les données de la piste pour le message sl
func (m *LiveVolumeDisplayManager) formatTrackData(trackData *TrackData, subMode string) string {
	var value int
	var valueString string

	switch subMode {
	case SubmodeVolume:
		value = int(math.Round(trackData.Volume * 100))
		valueString = m.volumeConverter.ToDb(trackData.Volume) + " dB"
	case SubmodePan:
		value = int(math.Round((trackData.Pan + 1) * 50))
		valueString = m.convertPanToDisplayString(trackData.Pan)
	default:
		if len(subMode) > 1 && subMode[0] == 's' {
			if sendIndex, err := strconv.Atoi(subMode[1:]); err == nil {
				if sendIndex >= 0 && sendIndex < len(trackData.Sends) {
					value = int(math.Round(trackData.Sends[sendIndex] * 100))
					valueString = m.volumeSendConverter.ToDb(trackData.Sends[sendIndex]) + " dB"
				}
			}
		}
	}
	// Correction couleur : JS envoie la couleur décimale, Go doit convertir l'hex en décimal
	colorDec := 0
	if len(trackData.Color) > 0 {
		fmt.Sscanf(trackData.Color, "%x", &colorDec)
	}
	return fmt.Sprintf(",%s,%d,%d,%d,%d,%d,%d,%d,%s",
		trackData.Name,
		boolToInt(trackData.IsFoldable),
		boolToInt(trackData.FoldState),
		boolToInt(trackData.IsGrouped),
		trackData.MuteState,
		trackData.Arm,
		colorDec,
		value,
		valueString)
}

// ClearSlot efface un slot
func (m *LiveVolumeDisplayManager) ClearSlot(slotLetter string) {
	// Nettoyer le cache pour ce slot
	for key := range m.slotsCache {
		if len(key) >= len(slotLetter) && key[:len(slotLetter)] == slotLetter {
			delete(m.slotsCache, key)
		}
	}
	// Forcer l'envoi avec priorité pour s'assurer que les mises à jour sont visibles
	m.communicationManager.SendMessage(fmt.Sprintf("es%s0", slotLetter), true)
}

// UpdatePage met à jour la page courante
func (m *LiveVolumeDisplayManager) UpdatePage(page int) {
	totalTracks := m.liveTrackManager.GetTrackCount()
	totalPages := int(math.Ceil(float64(totalTracks) / float64(SlotsPerPage)))
	// Forcer l'envoi avec priorité pour s'assurer que les mises à jour sont visibles
	m.communicationManager.SendMessage(fmt.Sprintf("vp%d/%d", page, totalPages), true)
}

// UpdateSelectedTrack met à jour la piste sélectionnée
func (m *LiveVolumeDisplayManager) UpdateSelectedTrack(slotLetter string) {
	// Forcer l'envoi avec priorité pour s'assurer que les mises à jour sont visibles
	m.communicationManager.SendMessage(fmt.Sprintf("st%s", slotLetter), true)
}

// ClearSelectedTrack efface la sélection de piste
func (m *LiveVolumeDisplayManager) ClearSelectedTrack() {
	// Forcer l'envoi avec priorité pour s'assurer que les mises à jour sont visibles
	m.communicationManager.SendMessage("st0", true)
}

// UpdateLockState met à jour l'état de verrouillage
func (m *LiveVolumeDisplayManager) UpdateLockState(isLocked bool) {
	// Forcer l'envoi avec priorité pour s'assurer que les mises à jour sont visibles
	m.communicationManager.SendMessage(fmt.Sprintf("lv0%d", boolToInt(isLocked)), true)
}

// UpdateColor met à jour la couleur d'un slot
func (m *LiveVolumeDisplayManager) UpdateColor(slotLetter, color string) {
	// Convertir la couleur hex en décimal
	var colorDec int
	fmt.Sscanf(color, "%x", &colorDec)
	// Forcer l'envoi avec priorité pour s'assurer que les mises à jour sont visibles
	m.communicationManager.SendMessage(fmt.Sprintf("co%s%d", slotLetter, colorDec), true)
}

// UpdateName met à jour le nom d'un slot
func (m *LiveVolumeDisplayManager) UpdateName(slotLetter, name string) {
	// Forcer l'envoi avec priorité pour s'assurer que les mises à jour sont visibles
	m.communicationManager.SendMessage(fmt.Sprintf("na%s%s", slotLetter, m.shortString(name, 20)), true)
}

// UpdateMuteState met à jour l'état muet
func (m *LiveVolumeDisplayManager) UpdateMuteState(slotLetter string, muteState int) {
	// Forcer l'envoi avec priorité pour s'assurer que les mises à jour sont visibles
	m.communicationManager.SendMessage(fmt.Sprintf("mu%s%d", slotLetter, muteState), true)
}

// UpdateArm met à jour l'état d'armement
func (m *LiveVolumeDisplayManager) UpdateArm(slotLetter string, isArm bool) {
	// Forcer l'envoi avec priorité pour s'assurer que les mises à jour sont visibles
	m.communicationManager.SendMessage(fmt.Sprintf("am%s%d", slotLetter, boolToInt(isArm)), true)
}

// UpdateVolume met à jour le volume
func (m *LiveVolumeDisplayManager) UpdateVolume(slotLetter string, value float64, dbValue float64) {
	// Convertir la valeur (en pourcentage) en valeur entre 0 et 1 pour le volumeConverter
	volumeValue := value / 100
	dbString := m.volumeConverter.ToDb(volumeValue) + " dB"
	// Forcer l'envoi avec priorité pour s'assurer que les mises à jour sont visibles
	m.communicationManager.SendMessage(fmt.Sprintf("vo,%s%v,%s", slotLetter, value, dbString), true)
}

// UpdatePan met à jour le panoramique
func (m *LiveVolumeDisplayManager) UpdatePan(slotLetter string, value float64) {
	panRawValue := (value / 50) - 1
	panDisplayString := m.convertPanToDisplayString(panRawValue)
	// Forcer l'envoi avec priorité pour s'assurer que les mises à jour sont visibles
	m.communicationManager.SendMessage(fmt.Sprintf("vo,%s%v,%s", slotLetter, value, panDisplayString), true)
}

// UpdateSend met à jour un send
func (m *LiveVolumeDisplayManager) UpdateSend(slotLetter string, sendIndex int, value float64) {
	sendValue := int(math.Round(value * 100))
	// Forcer l'envoi avec priorité pour s'assurer que les mises à jour sont visibles
	m.communicationManager.SendMessage(fmt.Sprintf("vo,%s%d,%s dB", slotLetter, sendValue, m.volumeSendConverter.ToDb(value)), true)
}

// ClearCache vide le cache
func (m *LiveVolumeDisplayManager) ClearCache() {
	m.slotsCache = make(map[string]string)
}

// UpdateAllValuesAtOnce met à jour toutes les valeurs d'un coup, incluant l'index du mode et le nom du send si applicable.
func (m *LiveVolumeDisplayManager) UpdateAllValuesAtOnce(subMode string, tracksData map[int]*TrackData, modeIndex int, sendName string) {
	log.Printf("UpdateAllValuesAtOnce - Mode: %s, Index: %d, SendName: '%s'", subMode, modeIndex, sendName)

	startTrack := (m.state.CurrentPage - 1) * SlotsPerPage
	values := make([]string, 0, SlotsPerPage)

	// Vérifier si nous avons des données pour au moins une piste sur la page actuelle
	dataAvailable := false
	for i := 0; i < SlotsPerPage; i++ {
		trackIndex := startTrack + i
		if trackData, exists := tracksData[trackIndex]; exists && trackData != nil {
			dataAvailable = true
			break
		}
	}

	// Si nous n'avons aucune donnée pour cette page et que le cache contient des données, ne pas mettre à jour l'affichage
	if !dataAvailable && len(tracksData) == 0 {
		log.Printf("Aucune donnée disponible pour la page %d, mise à jour de l'affichage annulée", m.state.CurrentPage)
		return
	}

	for i := 0; i < SlotsPerPage; i++ {
		trackData, exists := tracksData[startTrack+i]

		if exists && trackData != nil {
			log.Printf("Traitement piste %d (slot %d) - Nom: %s", startTrack+i, i, trackData.Name)

			switch {
			case subMode == SubmodeVolume:
				value := int(math.Round(trackData.Volume * 100))
				text := m.volumeConverter.ToDb(trackData.Volume) + " dB"
				values = append(values, fmt.Sprintf("%d,%s", value, text))
				log.Printf("  Volume: %d, %s", value, text)

			case subMode == SubmodePan:
				value := int(math.Round((trackData.Pan + 1) * 50))
				text := m.convertPanToDisplayString(trackData.Pan)
				values = append(values, fmt.Sprintf("%d,%s", value, text))
				log.Printf("  Pan: %d, %s", value, text)

			case len(subMode) > 1 && subMode[0] == SubmodeSendPrefix[0]:
				// Extraire l'index du send (s0, s1, etc.)
				sendIndex := -1
				_, err := fmt.Sscanf(subMode[1:], "%d", &sendIndex)
				if err != nil {
					log.Printf("Erreur de parsing du mode send: %v", err)
					values = append(values, "0,no send")
					continue
				}

				log.Printf("  Send index: %d, Sends disponibles: %v", sendIndex, trackData.Sends)

				// Vérifier si le send existe pour cette piste
				if sendIndex >= 0 && sendIndex < len(trackData.Sends) {
					value := int(math.Round(trackData.Sends[sendIndex] * 100))
					text := m.volumeSendConverter.ToDb(trackData.Sends[sendIndex]) + " dB"
					values = append(values, fmt.Sprintf("%d,%s", value, text))
					log.Printf("  Send value: %d, %s", value, text)
				} else {
					values = append(values, "0,no send")
					log.Printf("  Pas de send disponible")
				}

			default:
				values = append(values, "0,-inf dB")
				log.Printf("  Mode non reconnu")
			}
		} else {
			// Utiliser une valeur par défaut mais pas -inf dB
			values = append(values, "-,-")
			log.Printf("Pas de données pour la piste %d", startTrack+i)
		}
	}

	// Ne pas envoyer de mise à jour si toutes les valeurs sont vides ou par défaut
	allDefault := true
	for _, value := range values {
		if value != "-,-" {
			allDefault = false
			break
		}
	}

	if !allDefault || dataAvailable {
		// Envoyer la mise à jour combinée
		// Format: vz,<modeIndex>,<sendName>,<value1,text1>,<value2,text2>,...
		message := fmt.Sprintf("vz,%d,%s,%s", modeIndex, sendName, joinStrings(values, ","))
		log.Printf("Envoi du message combiné: %s", message)
		// Forcer l'envoi avec priorité pour s'assurer que les mises à jour sont visibles
		m.communicationManager.SendMessage(message, true)
	} else {
		log.Printf("Mise à jour annulée - toutes les valeurs sont par défaut")
	}
}

// ClearEmptySlots efface les slots qui ne correspondent pas à des pistes existantes
func (m *LiveVolumeDisplayManager) ClearEmptySlots() {
	// Annuler le timer existant s'il y en a un
	if m.clearEmptySlotsTimer != nil {
		m.clearEmptySlotsTimer.Stop()
	}

	// Créer un nouveau timer qui exécutera le nettoyage après 100ms
	m.clearEmptySlotsTimer = time.AfterFunc(100*time.Millisecond, func() {
		m.doClearEmptySlots()
	})
}

// doClearEmptySlots est la fonction qui effectue réellement le nettoyage
func (m *LiveVolumeDisplayManager) doClearEmptySlots() {
	totalTracks := m.liveTrackManager.GetTrackCount()
	startTrack := (m.state.CurrentPage - 1) * SlotsPerPage

	log.Printf("doClearEmptySlots: totalTracks=%d, startTrack=%d", totalTracks, startTrack)

	// Ne jamais avoir moins de 2 pistes (minimum de Live)
	if totalTracks < 2 {
		totalTracks = 2
	}

	// Parcourir les slots de la page actuelle
	for i := 0; i < SlotsPerPage; i++ {
		trackIndex := startTrack + i
		slotLetter := string(rune('A' + i))

		// Vérifier si ce slot doit être effacé
		shouldClear := false

		// Condition 1: Indice de piste hors limites des pistes existantes
		if trackIndex >= totalTracks {
			shouldClear = true
			log.Printf("doClearEmptySlots: Le slot %s sera effacé car trackIndex (%d) >= totalTracks (%d)", slotLetter, trackIndex, totalTracks)
		}

		// Condition 2: Pas de données pour cette piste dans tracksData
		if _, hasData := m.parentMode.tracksData[trackIndex]; !hasData {
			shouldClear = true
			log.Printf("doClearEmptySlots: Le slot %s sera effacé car pas de données dans tracksData pour l'index %d", slotLetter, trackIndex)
		}

		// Exception: Ne jamais effacer les slots A et B sur la page 1 (minimum 2 pistes)
		if startTrack == 0 && (slotLetter == "A" || slotLetter == "B") {
			shouldClear = false
		}

		// Effacer si nécessaire
		if shouldClear {
			log.Printf("doClearEmptySlots: Effacement du slot %s (piste %d)", slotLetter, trackIndex)
			m.ClearSlot(slotLetter)
		} else {
			log.Printf("doClearEmptySlots: Conservation du slot %s (piste %d)", slotLetter, trackIndex)
		}
	}
}

// SendFullUpdate envoie une mise à jour complète de tous les slots
func (m *LiveVolumeDisplayManager) SendFullUpdate(tracksData map[int]*TrackData) {
	log.Println(">>> SendFullUpdate: Mise à jour complète des slots")

	startTrack := (m.state.CurrentPage - 1) * SlotsPerPage
	endTrack := startTrack + SlotsPerPage

	log.Printf(">>> SendFullUpdate: Page %d (pistes %d à %d), Mode %s",
		m.state.CurrentPage, startTrack, endTrack-1, m.state.SubMode)

	// Vérifier si nous avons des données pour cette page
	hasData := false
	for i := startTrack; i < endTrack; i++ {
		if _, exists := tracksData[i]; exists {
			hasData = true
			break
		}
	}

	if !hasData {
		log.Printf(">>> SendFullUpdate: Aucune donnée pour la page %d", m.state.CurrentPage)
		// On continue quand même pour effacer les slots
	}

	// Mettre à jour chaque slot de la page
	for i := 0; i < SlotsPerPage; i++ {
		trackIndex := startTrack + i
		slotLetter := string(rune('A' + i))
		trackData, exists := tracksData[trackIndex]

		if exists && trackData != nil {
			// Mettre à jour le slot avec les données de piste
			log.Printf(">>> SendFullUpdate: Mise à jour slot %s avec piste %d (%s)",
				slotLetter, trackIndex, trackData.Name)
			// Forcer l'envoi avec priorité pour s'assurer que les mises à jour sont visibles
			// Générer le nouveau message
			newMessage := m.formatTrackData(trackData, m.state.SubMode)
			slMessage := fmt.Sprintf("sl%s%s", slotLetter, newMessage)
			m.communicationManager.SendMessage(slMessage, true)
			// Mettre à jour le cache
			cacheKey := fmt.Sprintf("%s_%s", slotLetter, m.state.SubMode)
			m.slotsCache[cacheKey] = newMessage
		} else {
			// Gérer le slot sans données
			// Exception: Ne jamais effacer les slots A et B sur la page 1
			if startTrack == 0 && (slotLetter == "A" || slotLetter == "B") {
				log.Printf(">>> SendFullUpdate: Protection du slot %s (piste minimale)", slotLetter)
			} else {
				log.Printf(">>> SendFullUpdate: Effacement du slot %s (pas de données)", slotLetter)
				m.ClearSlot(slotLetter)
			}
		}
	}
}

// Fonctions utilitaires

func (m *LiveVolumeDisplayManager) convertPanToDisplayString(panValue float64) string {
	if panValue == 0 {
		return "C"
	} else if panValue < 0 {
		return fmt.Sprintf("%dL", int(math.Abs(math.Round(panValue*50))))
	}
	return fmt.Sprintf("%dR", int(math.Round(panValue*50)))
}

func (m *LiveVolumeDisplayManager) shortString(s string, maxLength int) string {
	if len(s) > maxLength {
		return s[:maxLength] + "..."
	}
	return s
}

func boolToInt(b bool) int {
	if b {
		return 1
	}
	return 0
}

func joinStrings(strs []string, sep string) string {
	if len(strs) == 0 {
		return ""
	}
	result := strs[0]
	for _, s := range strs[1:] {
		result += sep + s
	}
	return result
}
