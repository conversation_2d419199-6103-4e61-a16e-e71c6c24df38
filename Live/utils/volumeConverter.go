package utils

import (
	"fmt"
	"math"
)

// VolumeConverter gère la conversion des volumes en dB
type VolumeConverter struct {
	lowThreshold   float64
	highThreshold  float64
	middleRangeMap map[float64]float64
}

// NewVolumeConverter crée une nouvelle instance de VolumeConverter
func NewVolumeConverter() *VolumeConverter {
	converter := &VolumeConverter{
		lowThreshold:  0.12,
		highThreshold: 0.4,
		middleRangeMap: map[float64]float64{
			0.387: -18.6, 0.378: -19.0, 0.365: -19.7, 0.36: -20.0,
			0.344: -21.0, 0.337: -21.4, 0.329: -22.0, 0.321: -22.6,
			0.315: -23.0, 0.302: -24.0, 0.299: -24.3, 0.29: -25.0,
			0.286: -25.4, 0.279: -26.0, 0.273: -26.6, 0.268: -27.0,
			0.261: -27.7, 0.258: -28.0, 0.248: -29.0, 0.244: -29.4,
			0.238: -30.0, 0.233: -30.6, 0.229: -31.0, 0.22: -32.0,
			0.217: -32.4, 0.212: -33.0, 0.203: -34.0, 0.2: -34.4,
			0.195: -35.0, 0.187: -36.0, 0.183: -36.5, 0.179: -37.0,
			0.172: -38.0, 0.164: -39.0, 0.161: -39.4, 0.157: -40.0,
			0.15: -41.0, 0.143: -42.0, 0.136: -43.0, 0.133: -43.4,
			0.129: -44.0,
		},
	}
	return converter
}

// ToDb convertit une valeur de volume en dB
func (v *VolumeConverter) ToDb(volume float64) string {
	// Arrondir à 3 décimales pour éviter les problèmes de précision
	volume = math.Round(volume*1000) / 1000

	if volume <= 0 {
		return "- inf"
	}
	if volume >= 1 {
		return "6.0"
	}

	var result float64
	if volume <= v.lowThreshold {
		// Fonction linéaire basse : 180*x-66.6
		result = 180*volume - 66.6
	} else if volume >= v.highThreshold {
		// Fonction linéaire haute : 40*x-34
		result = 40*volume - 34
	} else {
		// Pour la zone médiane, recherche de la valeur la plus proche
		result = v.findNearestDbValue(volume)
	}

	// Éviter l'affichage de -0.0
	if result > -0.05 && result < 0 {
		result = 0
	}

	return fmt.Sprintf("%.1f", result)
}

// findNearestDbValue trouve la valeur dB la plus proche pour un volume donné
func (v *VolumeConverter) findNearestDbValue(volume float64) float64 {
	// Si la valeur existe exactement dans la map
	if db, exists := v.middleRangeMap[volume]; exists {
		return db
	}

	// Sinon, trouver les deux valeurs les plus proches et interpoler
	var nearestLower, nearestHigher struct {
		vol float64
		db  float64
	}
	nearestLower.vol = -1
	nearestHigher.vol = 2 // valeur supérieure à 1 pour initialisation

	for vol, db := range v.middleRangeMap {
		if vol < volume && (nearestLower.vol == -1 || vol > nearestLower.vol) {
			nearestLower.vol = vol
			nearestLower.db = db
		}
		if vol > volume && (nearestHigher.vol == 2 || vol < nearestHigher.vol) {
			nearestHigher.vol = vol
			nearestHigher.db = db
		}
	}

	// Interpolation linéaire entre les deux valeurs les plus proches
	if nearestLower.vol != -1 && nearestHigher.vol != 2 {
		ratio := (volume - nearestLower.vol) / (nearestHigher.vol - nearestLower.vol)
		return nearestLower.db + ratio*(nearestHigher.db-nearestLower.db)
	}

	return 0 // Fallback
}
