package utils

import (
	"fmt"
	"math"
)

// VolumeSendConverter gère la conversion des volumes pour l'envoi
type VolumeSendConverter struct {
	highThreshold float64
	volumeMap     map[float64]float64
}

// NewVolumeSendConverter crée une nouvelle instance de VolumeSendConverter
func NewVolumeSendConverter() *VolumeSendConverter {
	converter := &VolumeSendConverter{
		highThreshold: 0.4,
		volumeMap: map[float64]float64{
			0.388: -24.5, 0.378: -25.0, 0.372: -25.3, 0.365: -25.7,
			0.36: -26.0, 0.35: -26.6, 0.344: -27.0, 0.333: -27.7,
			0.329: -28.0, 0.315: -29.0, 0.302: -30.0, 0.29: -31.0,
			0.286: -31.4, 0.279: -32.0, 0.268: -33.0, 0.258: -34.0,
			0.248: -35.0, 0.238: -36.0, 0.229: -37.0, 0.22: -38.0,
			0.212: -39.0, 0.203: -40.0, 0.195: -41.0, 0.187: -42.0,
			0.179: -43.0, 0.172: -44.0, 0.164: -45.0, 0.157: -46.0,
			0.15: -47.0, 0.143: -48.0, 0.136: -49.0, 0.129: -50.0,
			0.116: -52.0, 0.11: -53.0, 0.103: -54.0, 0.097: -55.0,
			0.085: -57.0, 0.079: -58.0, 0.074: -59.0, 0.062: -61.0,
			0.058: -62.0, 0.045: -64.0, 0.037: -65.0, 0.029: -67.0,
			0.022: -68.0, 0.016: -69.0, 0.008: -69.5,
		},
	}
	return converter
}

// ToDb convertit une valeur de volume en dB pour l'envoi
func (v *VolumeSendConverter) ToDb(volume float64) string {
	// Arrondir à 3 décimales pour la précision
	volume = math.Round(volume*1000) / 1000

	if volume <= 0 {
		return "- inf"
	}
	if volume >= 1 {
		return "0.0"
	}

	var result float64
	if volume > v.highThreshold {
		// Fonction linéaire haute : 40*x-40
		result = 40*volume - 40
	} else {
		// Pour les valeurs <= 0.4, recherche de la valeur la plus proche
		result = v.findNearestDbValue(volume)
	}

	return fmt.Sprintf("%.1f", result)
}

// findNearestDbValue trouve la valeur dB la plus proche pour un volume donné
func (v *VolumeSendConverter) findNearestDbValue(volume float64) float64 {
	// Si la valeur existe exactement dans la map
	if db, exists := v.volumeMap[volume]; exists {
		return db
	}

	// Sinon, trouver les deux valeurs les plus proches et interpoler
	var nearestLower, nearestHigher struct {
		vol float64
		db  float64
	}
	nearestLower.vol = -1
	nearestHigher.vol = 2 // valeur supérieure à 1 pour initialisation

	for vol, db := range v.volumeMap {
		if vol < volume && (nearestLower.vol == -1 || vol > nearestLower.vol) {
			nearestLower.vol = vol
			nearestLower.db = db
		}
		if vol > volume && (nearestHigher.vol == 2 || vol < nearestHigher.vol) {
			nearestHigher.vol = vol
			nearestHigher.db = db
		}
	}

	// Interpolation linéaire entre les deux valeurs les plus proches
	if nearestLower.vol != -1 && nearestHigher.vol != 2 {
		ratio := (volume - nearestLower.vol) / (nearestHigher.vol - nearestLower.vol)
		return nearestLower.db + ratio*(nearestHigher.db-nearestLower.db)
	}

	return -70 // Fallback
}
