package livetrackmode

import (
	"log"
	communication "oscbridge/Communication"
	live "oscbridge/Live"
	"oscbridge/Live/types" // Assurez-vous que le package types est correctement importé
)

// handleButtonEvent traite les événements de bouton pour le mode Track
func (m *LiveTrackMode) handleButtonEvent(event *communication.ButtonEvent) {
	if event == nil {
		return
	}

	log.Printf("Mode Track: Bouton %d état %d", event.Index, event.State)

	// Cas spécial pour B05 : traiter seulement le relâché (état 0)
	// L'appui (état 1) est maintenant géré par HardwareManager comme bouton transversal
	if event.Index == 5 {
		if event.State == 0 {
			// B05 avec état 0 : exit quickview
			log.Printf("Mode Track: B05 relâché - Exit quickview")
			// Vérifier si on est réellement en QuickView
			m.mutex.RLock()
			isQuickView := m.isQuickView
			m.mutex.RUnlock()

			if isQuickView {
				log.Println("Mode Track: B05 relâché en QuickView, émission de requestExitQuickView")
				m.BaseMode.Emit("requestExitQuickView")
			} else {
				log.Println("Mode Track: B05 relâché hors QuickView, ignoré")
			}
		}
		// B05 état 1 (appuyé) est géré par HardwareManager, ne pas traiter ici
		return
	}

	// Pour tous les autres boutons, ne traiter que les appuis (state = 1)
	if event.State != 1 {
		log.Printf("Mode Track: Bouton %d relâché, ignoré", event.Index)
		return
	}

	log.Printf("Mode Track: Bouton %d appuyé, traitement", event.Index)

	switch event.Index {
	case 0: // B00 - Switch to Learn Mode
		m.BaseMode.Emit("modeChange", "learn")
		log.Printf("Mode Track: B00 - Switch to Learn Mode")
	case 1: // B01 - (non défini)
		log.Printf("Mode Track: B01 - Return to page 1")
		m.SetPage(1)
	case 2: // B02 - Switch to Volume Mode
		m.BaseMode.Emit("modeChange", "volume")
		log.Printf("Mode Track: B02 - Switch to Volume Mode")
	case 3: // B03 - Return to page 1
		m.BaseMode.Emit("modeChange", "device")
		log.Printf("Mode Track: B03 - Switch to Device Mode")
	case 4: // B04 - Switch to Browse Mode
		m.BaseMode.Emit("modeChange", "browser")
		log.Printf("Mode Track: B04 - Switch to Browse Mode")

	case 6: // B06 - Select previous track or jump to previous cue
		m.mutex.RLock()
		isQuickView := m.isQuickView
		m.mutex.RUnlock()

		if isQuickView {
			log.Printf("Mode Track: B06 appuyé en QuickView - Envoi de /live/song/jump_to_prev_cue")
			m.BaseMode.Send("/live/song/jump_to_prev_cue")
		} else {
			m.selectPreviousTrack()
			log.Printf("Mode Track: B06 - Select previous track")
		}
	case 7: // B07 - Select next track or jump to next cue
		m.mutex.RLock()
		isQuickView := m.isQuickView
		m.mutex.RUnlock()

		if isQuickView {
			log.Printf("Mode Track: B07 appuyé en QuickView - Envoi de /live/song/jump_to_next_cue")
			m.BaseMode.Send("/live/song/jump_to_next_cue")
		} else {
			m.selectNextTrack()
			log.Printf("Mode Track: B07 - Select next track")
		}
	case 8: // B08 - Page down
		m.pageDown()
		log.Printf("Mode Track: B08 - Page down")
	case 9: // B09 - Page up
		m.pageUp()
		log.Printf("Mode Track: B09 - Page up")
	case 13: // B13 - Toggle lock or Tap Tempo
		// Vérifier si on est en QuickView
		m.mutex.RLock()
		isQuickView := m.isQuickView
		m.mutex.RUnlock()

		if isQuickView {
			log.Printf("Mode Track: B13 appuyé en QuickView - Envoi de /live/song/tap_tempo")
			m.BaseMode.Send("/live/song/tap_tempo")
		} else {
			log.Printf("Mode Track: B13 appuyé hors QuickView - Toggle lock")
			m.ToggleLock()
		}
	default:
		log.Printf("Mode Track: Bouton non géré: %d", event.Index)
	}
}

// selectPreviousTrack sélectionne la piste précédente
func (m *LiveTrackMode) selectPreviousTrack() {
	currentTrack := m.trackManager.GetSelectedTrack()
	if currentTrack == nil {
		log.Println("selectPreviousTrack: Aucune piste sélectionnée")
		return
	}

	if *currentTrack > 0 {
		newTrackIndex := *currentTrack - 1
		err := m.trackManager.SetSelectedTrack(newTrackIndex)
		if err != nil {
			log.Printf("selectPreviousTrack: Erreur lors de la sélection de la piste %d: %v", newTrackIndex, err)
		} else {
			log.Printf("selectPreviousTrack: Piste %d sélectionnée", newTrackIndex)
		}
	} else {
		log.Printf("selectPreviousTrack: Déjà sur la première piste (%d)", *currentTrack)
	}
}

// selectNextTrack sélectionne la piste suivante
func (m *LiveTrackMode) selectNextTrack() {
	currentTrack := m.trackManager.GetSelectedTrack()
	if currentTrack == nil {
		log.Println("selectNextTrack: Aucune piste sélectionnée")
		return
	}

	// Obtenir le nombre total de pistes
	trackCount := m.trackManager.GetTrackCount()
	if *currentTrack < trackCount-1 {
		newTrackIndex := *currentTrack + 1
		err := m.trackManager.SetSelectedTrack(newTrackIndex)
		if err != nil {
			log.Printf("selectNextTrack: Erreur lors de la sélection de la piste %d: %v", newTrackIndex, err)
		} else {
			log.Printf("selectNextTrack: Piste %d sélectionnée", newTrackIndex)
		}
	} else {
		log.Printf("selectNextTrack: Déjà sur la dernière piste (%d/%d)", *currentTrack, trackCount-1)
	}
}

// HandleHardwareEvent gère les événements hardware reçus
func (m *LiveTrackMode) HandleHardwareEvent(event communication.HardwareEvent) {
	//log.Printf("LiveTrackMode.HandleHardwareEvent received: Type=%s, Raw=%s", event.Type, event.RawMessage)

	switch event.Type {
	case "button":
		m.handleButtonEvent(event.ButtonEvent)
	case "encoder":
		if event.EncoderEvent != nil {
			//log.Printf("Encoder changed: Index %d, Value %d", event.EncoderEvent.Index, event.EncoderEvent.Value)
			m.handleEncoderChange(event.EncoderEvent.Index, event.EncoderEvent.Value)
		}
	case "touch":
		if event.TouchEvent != nil {
			log.Printf("Touch pressed: Type %s, Index %d", event.TouchEvent.TouchType, event.TouchEvent.Index)
			m.handleTouchEvent(event.TouchEvent)
		}
	default:
		log.Printf("Unhandled hardware event type: %s", event.Type)
	}
}

// handleButtonPress gère l'appui sur un bouton
func (m *LiveTrackMode) handleButtonPress(buttonIndex int) {
	// --- Logique pour QuickView ---
	// Exemple: Si le bouton 5 est pressé, demander la sortie de QuickView
	if buttonIndex == 5 {
		// Vérifier si on est réellement en QuickView
		m.mutex.RLock()
		isQuickView := m.isQuickView
		m.mutex.RUnlock()

		if isQuickView {
			log.Println("[LiveTrackMode] Bouton 5 détecté en QuickView, émission de requestExitQuickView")
			m.BaseMode.Emit("requestExitQuickView")
			return // Événement traité pour sortir de QuickView
		} else {
			// Si on n'est pas en QuickView, le bouton 5 pourrait avoir une autre fonction ou être ignoré
			log.Println("[LiveTrackMode] Bouton 5 détecté hors QuickView, ignoré pour la sortie.")
			// Ne pas faire 'return' ici si le bouton 5 a une autre fonction hors QuickView
		}
	}
	// --- Fin Logique QuickView ---

	// Mapping basé sur onButtonPressed dans liveTrackMode.js pour les autres boutons
	switch buttonIndex {
	case 0: // Correspond à 'pageDown' dans le JS
		log.Println("Handling Page Down button")
		m.pageDown()
	case 1: // Correspond à 'pageUp' dans le JS
		log.Println("Handling Page Up button")
		m.pageUp()
	case 2: // Correspond à 'toggleLock' dans le JS
		log.Println("Handling Toggle Lock button")
		m.ToggleLock() // Assurez-vous que ToggleLock est implémenté
	default:
		log.Printf("Button index %d not handled in TrackMode", buttonIndex)
	}
}

// handleEncoderEvent traite les événements d'encodeur
func (m *LiveTrackMode) handleEncoderEvent(event *communication.EncoderEvent) {
	if event == nil {
		return
	}

	log.Printf("Mode Track: Événement encodeur reçu: Index=%d, Value=%d", 
		event.Index, event.Value)
	
	// Appeler la fonction de traitement des encodeurs
	m.handleEncoderChange(event.Index, event.Value)
}

// handleEncoderChange gère le changement d'un encodeur
func (m *LiveTrackMode) handleEncoderChange(encoderIndex int, changeValue int) {
	log.Printf("Mode Track: Encodeur %d tourné avec la valeur %d", encoderIndex, changeValue)

	// Déterminer la piste cible en fonction du mode (QuickView ou Lock)
	m.mutex.RLock()
	isQuickView := m.isQuickView
	isLocked := m.isLocked
	var targetTrack *TrackData
	var targetTrackIndex *int

	if isQuickView {
		// En QuickView, on utilise toujours la piste sélectionnée
		targetTrack = m.selectedTrackData
		if m.selectedTrackData != nil {
			idx := m.selectedTrackData.TrackIdx
			targetTrackIndex = &idx
		}
	} else if isLocked && m.lockedTrackData != nil {
		// Mode normal + verrouillé = utiliser la piste verrouillée
		targetTrack = m.lockedTrackData
		targetTrackIndex = m.lockedTrackIndex
	} else {
		// Mode normal + non verrouillé = utiliser la piste sélectionnée
		targetTrack = m.selectedTrackData
		if m.selectedTrackData != nil {
			idx := m.selectedTrackData.TrackIdx
			targetTrackIndex = &idx
		}
	}
	currentPage := m.currentPage
	m.mutex.RUnlock()

	if targetTrack == nil || targetTrackIndex == nil {
		log.Println("handleEncoderChange: Aucune piste cible disponible")
		return
	}

	// Calculer le delta normalisé (les valeurs d'encodeur sont sur une base 1000)
	normalizedDelta := float64(changeValue) / 1000.0

	// Constante pour le nombre de sends par page
	const SendsPerPage = 4

	switch encoderIndex {
	case 0, 1, 2, 3: // Sends (0-3)
		// Calculer l'index absolu du send en fonction de la page actuelle
		sendIndexOffset := encoderIndex
		absoluteSendIndex := (currentPage-1)*SendsPerPage + sendIndexOffset
		
		// Vérifier que le send existe
		if absoluteSendIndex >= 0 && absoluteSendIndex < len(targetTrack.Sends) {
			// Envoyer le message OSC pour ajuster le send
			m.BaseMode.Send("/live/track/adjust/send", *targetTrackIndex, absoluteSendIndex, normalizedDelta)
			log.Printf("Envoi OSC: /live/track/adjust/send %d %d %f", *targetTrackIndex, absoluteSendIndex, normalizedDelta)
		} else {
			log.Printf("handleEncoderChange: Index de send invalide %d", absoluteSendIndex)
		}
		
	case 4: // Volume (encodeur 4)
		// Envoyer le message OSC pour ajuster le volume
		m.BaseMode.Send("/live/track/adjust/volume", *targetTrackIndex, normalizedDelta)
		log.Printf("Envoi OSC: /live/track/adjust/volume %d %f", *targetTrackIndex, normalizedDelta)
		
	case 5: // Panning (encodeur 5)
		// Envoyer le message OSC pour ajuster le panning
		m.BaseMode.Send("/live/track/adjust/panning", *targetTrackIndex, normalizedDelta)
		log.Printf("Envoi OSC: /live/track/adjust/panning %d %f", *targetTrackIndex, normalizedDelta)
		
	case 6, 7: // Encodeurs 6 et 7 (ignorés pour l'instant)
		log.Printf("Encodeur %d non utilisé actuellement", encoderIndex)
		
	default:
		log.Printf("Index d'encodeur non géré: %d", encoderIndex)
	}
}

// handleTouchEvent gère les événements tactiles
func (m *LiveTrackMode) handleTouchEvent(touchEvent *communication.TouchEvent) {
	if touchEvent == nil {
		return
	}

	touchType := touchEvent.TouchType
	index := touchEvent.Index

	log.Printf("handleTouchEvent: Type=%s, Index=%d", touchType, index)

	switch touchType {
	case "mut":
		m.toggleMute()
	case "sol":
		m.toggleSolo()
	case "arm":
		m.toggleArm()
	case "sm":
		m.handleSwitchModeTouch(index)
	case "le":
		m.handleLearnTouch(index)
	case "lkt":
		m.ToggleLock()
	case "paninit":
		m.resetPanning()
	case "volinit":
		m.resetVolume()
	case "td": // Track Daughters
		m.handleTrackDaughters(index)
	case "tw": // Track Sisters
		m.handleTrackSisters(index)
	case "pa": // Track Parent
		m.handleTrackParent(index)
	case "me": // Toggle Metronome
		m.BaseMode.Send("/live/song/toggle_metro")
		log.Printf("Toggle metronome")
	case "lo": // Toggle Loop
		m.BaseMode.Send("/live/song/toggle_loop")
		log.Printf("Toggle loop")
	case "ll": // Set Loop Length
		// Envoyer le message avec la valeur flottante originale
		m.BaseMode.Send("/live/song/set/loop_length", touchEvent.FloatValue)
		log.Printf("Set loop length with beats: %.6f", touchEvent.FloatValue)
	case "li": // Toggle Punch In
		m.BaseMode.Send("/live/song/toggle_punch_in")
		log.Printf("Toggle punch in")
	case "lr": // Toggle Punch Out
		m.BaseMode.Send("/live/song/toggle_punch_out")
		log.Printf("Toggle punch out")
	case "ac": // Set or Delete Cue
		m.BaseMode.Send("/live/song/set_or_delete_cue")
		log.Printf("Set or delete cue")
	case "lc": // Jump to Cue Point
		m.BaseMode.Send("/live/song/cue_point/jump", touchEvent.Index)
		log.Printf("Jump to cue point with index: %d", touchEvent.Index)
	case "ls": // Set Loop Start
		m.BaseMode.Send("/live/song/set/loop_start")
		log.Printf("Set loop start")
	default:
		log.Printf("Unhandled touch type: %s", touchType)
	}
}

// --- Helpers pour handleTouchPress ---

func (m *LiveTrackMode) toggleMute() {
	trackIndex := m.getTargetTrackIndex()
	if trackIndex != nil {
		m.BaseMode.Send(OscAddressTrackSetMuteToggle, *trackIndex)
		log.Printf("Toggle mute for track %d", *trackIndex)
	}
}

func (m *LiveTrackMode) toggleSolo() {
	trackIndex := m.getTargetTrackIndex()
	if trackIndex != nil {
		m.BaseMode.Send(OscAddressTrackSetSoloToggle, *trackIndex)
		log.Printf("Toggle solo for track %d", *trackIndex)
	}
}

func (m *LiveTrackMode) toggleArm() {
	trackIndex := m.getTargetTrackIndex()
	if trackIndex != nil {
		m.BaseMode.Send(OscAddressTrackSetArmToggle, *trackIndex)
		log.Printf("Toggle arm for track %d", *trackIndex)
	}
}

func (m *LiveTrackMode) handleSwitchModeTouch(index int) {
	var targetMode string
	switch index {
	case 1:
		targetMode = "volume"
	case 2:
		targetMode = "device"
	case 3:
		targetMode = "learn"
	case 4:
		targetMode = "browser"
	}
	if targetMode != "" {
		m.BaseMode.Emit("modeChange", targetMode)
		log.Printf("Switching to mode: %s", targetMode)
	}
}

func (m *LiveTrackMode) handleLearnTouch(index int) {
	trackIndexPtr := m.getTargetTrackIndex()
	if trackIndexPtr == nil {
		log.Println("handleLearnTouch: No target track available")
		return
	}
	trackIndex := *trackIndexPtr

	var paramType int
	var sendIndex *int

	switch index {
	case -1: // Volume
		paramType = types.ParamTypeVolume
	case -2: // Panning
		paramType = types.ParamTypePan
	default: // Sends (index >= 0)
		paramType = types.ParamTypeSend
		absSendIndex := (m.currentPage-1)*SendsPerPage + index
		// Vérifier si l'index du send est valide
		m.mutex.RLock()
		var targetSends []float64
		if m.isLocked && m.lockedTrackData != nil {
			targetSends = m.lockedTrackData.Sends
		} else if !m.isLocked && m.selectedTrackData != nil {
			targetSends = m.selectedTrackData.Sends
		}
		m.mutex.RUnlock()
		if targetSends != nil && absSendIndex >= 0 && absSendIndex < len(targetSends) {
			sendIndex = &absSendIndex
		} else {
			log.Printf("handleLearnTouch: Invalid send index %d for touch index %d", absSendIndex, index)
			return
		}
	}

	learnData := &types.LearnData{
		ParamType:   paramType,
		TrackIndex:  trackIndex,
		SendIndex:   sendIndex,
		DeviceIndex: nil,
		ParamIndex:  nil,
	}

	log.Printf("Setting learn data: %+v", learnData)
	if service := m.BaseMode.GetService(); service != nil {
		if modeManager, ok := service.(*live.LiveModeManager); ok {
			modeManager.SetLearnData(learnData)
			m.BaseMode.Emit("modeChange", "learn")
		} else {
			log.Println("handleLearnTouch: Failed to cast service to LiveModeManager")
		}
	} else {
		log.Println("handleLearnTouch: Service is nil")
	}
}

func (m *LiveTrackMode) resetPanning() {
	trackIndex := m.getTargetTrackIndex()
	if trackIndex != nil {
		m.sendParameterUpdate(*trackIndex, "panning", 0.0, -1)
		log.Printf("Reset panning for track %d", *trackIndex)
	}
}

func (m *LiveTrackMode) resetVolume() {
	trackIndex := m.getTargetTrackIndex()
	if trackIndex != nil {
		// Utiliser la valeur par défaut du JS: 0.85
		m.sendParameterUpdate(*trackIndex, "volume", 0.85, -1)
		log.Printf("Reset volume for track %d to 0.85", *trackIndex)
	}
}

func (m *LiveTrackMode) handleTrackDaughters(index int) {
	trackType := 0
	if !m.isQuickView && m.isLocked {
		trackType = 1
	}
	oscAddress := OscAddressSelectedTrackDaughter
	oscArgs := []interface{}{trackType, index}
	m.BaseMode.Send(oscAddress, oscArgs)
	log.Printf("Handle track daughters for index %d (trackType: %d)", index, trackType)
}

func (m *LiveTrackMode) handleTrackSisters(index int) {
	trackType := 0
	if !m.isQuickView && m.isLocked {
		trackType = 1
	}
	oscAddress := OscAddressSelectedTrackSister
	oscArgs := []interface{}{trackType, index}
	m.BaseMode.Send(oscAddress, oscArgs)
	log.Printf("Handle track sisters for index %d (trackType: %d)", index, trackType)
}

func (m *LiveTrackMode) handleTrackParent(index int) {
	trackType := 0
	if !m.isQuickView && m.isLocked {
		trackType = 1
	}
	oscAddress := OscAddressSelectedTrackParent
	oscArgs := []interface{}{trackType, index}
	m.BaseMode.Send(oscAddress, oscArgs)
	log.Printf("Handle track parent for index %d (trackType: %d)", index, trackType)
}

// getTargetTrackIndex retourne l'index de la piste cible (sélectionnée ou verrouillée)
func (m *LiveTrackMode) getTargetTrackIndex() *int {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	if m.isLocked && m.lockedTrackIndex != nil {
		return m.lockedTrackIndex
	} else if !m.isLocked && m.selectedTrackData != nil {
		return &m.selectedTrackData.TrackIdx
	}
	log.Println("getTargetTrackIndex: No target track available")
	return nil
}

// --- Fin Helpers pour handleTouchPress ---

// sendParameterUpdate envoie une mise à jour de paramètre OSC à Live
func (m *LiveTrackMode) sendParameterUpdate(trackIndex int, paramName string, value float64, sendIndex int) {
	var oscAddress string
	var oscArgs []interface{}

	switch paramName {
	case "volume":
		oscAddress = OscAddressTrackSetVolume
		oscArgs = []interface{}{trackIndex, float32(value)}
	case "panning":
		oscAddress = OscAddressTrackSetPanning
		oscArgs = []interface{}{trackIndex, float32(value)}
	case "send":
		if sendIndex < 0 {
			log.Println("sendParameterUpdate: Invalid send index")
			return
		}
		oscAddress = OscAddressTrackSetSends
		oscArgs = []interface{}{trackIndex, int32(sendIndex), float32(value)}
	default:
		log.Printf("sendParameterUpdate: Unknown param name: %s", paramName)
		return
	}
	m.BaseMode.Send(oscAddress, oscArgs)
	// Mise à jour locale optionnelle retirée pour privilégier le retour OSC
}

// --- Fonctions de pagination et de lock ---

// pageUp passe à la page suivante de sends/returns
func (m *LiveTrackMode) pageUp() {
	// Calculer maxPage en fonction du nombre de pistes de retour
	var returnTrackCount int
	if m.trackManager != nil {
		returnTrackCount = m.trackManager.GetReturnTrackCount()
	} else {
		log.Println("pageUp: trackManager is nil, cannot determine max page.")
		return // Ou définir une valeur par défaut ?
	}

	var maxPage int
	if returnTrackCount > 0 {
		maxPage = (returnTrackCount + SendsPerPage - 1) / SendsPerPage // Arrondi supérieur entier
	} else {
		maxPage = 1 // Au moins une page, même s'il n'y a pas de retours
	}

	m.mutex.RLock()
	currentPage := m.currentPage
	m.mutex.RUnlock()

	if currentPage < maxPage {
		log.Printf("Moving to page %d", currentPage+1)
		m.SetPage(currentPage + 1)
	} else {
		log.Printf("Already on last page (%d)", currentPage)
	}
}

// pageDown passe à la page précédente de sends/returns
func (m *LiveTrackMode) pageDown() {
	m.mutex.RLock()
	currentPage := m.currentPage
	m.mutex.RUnlock()
	if currentPage > 1 {
		log.Printf("Moving to page %d", currentPage-1)
		m.SetPage(currentPage - 1)
	} else {
		log.Println("Already on first page")
	}
}

// ToggleLock bascule l'état de verrouillage de la piste
func (m *LiveTrackMode) ToggleLock() {
	// Vérifier d'abord le mode QuickView avec un verrou de lecture
	m.mutex.RLock()
	isQuickView := m.isQuickView
	m.mutex.RUnlock()

	if isQuickView {
		log.Println("ToggleLock: Lock toggle not available in quick view mode")
		return
	}

	// Récupérer l'index de la piste sélectionnée avant de prendre le verrou
	var currentSelectedTrack *int
	selectedTrackPtr := m.trackManager.GetSelectedTrack()
	if selectedTrackPtr != nil {
		idx := *selectedTrackPtr
		currentSelectedTrack = &idx
	}

	// Prendre le verrou uniquement pour la modification de l'état
	m.mutex.Lock()
	previousLockState := m.isLocked
	m.isLocked = !previousLockState

	// Si on verrouille, mettre à jour les données de verrouillage
	if m.isLocked && currentSelectedTrack != nil {
		m.lockedTrackIndex = currentSelectedTrack
		if m.selectedTrackData != nil && m.selectedTrackData.TrackIdx == *m.lockedTrackIndex {
			copiedData := *m.selectedTrackData
			m.lockedTrackData = &copiedData
		} else {
			m.lockedTrackData = nil
		}
	} else if !m.isLocked {
		m.lockedTrackIndex = nil
		m.lockedTrackData = nil
	}

	// Stocker les données nécessaires pour les opérations après le déverrouillage
	selectedTrackData := m.selectedTrackData
	isActive := m.isActive
	m.mutex.Unlock()

	// Effectuer toutes les opérations externes après avoir relâché le verrou
	if m.isLocked && currentSelectedTrack != nil {
		log.Printf("Track locked on index %d", *currentSelectedTrack)
		m.BaseMode.Send(OscAddressTrackLock, "1")
		// Mise à jour de l'affichage avec le nouvel état
		m.displayManager.UpdateTrackViewState(false, true)
	} else if !m.isLocked {
		log.Println("Track unlocked")
		m.BaseMode.Send(OscAddressTrackLock, "0")
		// Mise à jour de l'affichage avec le nouvel état
		m.displayManager.UpdateTrackViewState(false, false)

		if selectedTrackData != nil {
			var returnNames []string
			if m.trackManager != nil {
				returnNames = m.trackManager.GetReturnTracksName()
			}
			m.mutex.RLock()
			currentPage := m.currentPage
			m.mutex.RUnlock()

			m.displayManager.RefreshFullTrackUIDisplay(selectedTrackData, currentPage, isActive, returnNames)
		}
		m.updateReturnNamesForCurrentPage()
	} else {
		log.Println("ToggleLock: Cannot lock, no track selected.")
		// Revenir à l'état non verrouillé si on ne peut pas verrouiller
		m.mutex.Lock()
		m.isLocked = false
		m.mutex.Unlock()
		// Mise à jour de l'affichage avec l'état par défaut
		m.displayManager.UpdateTrackViewState(false, false)
	}
}

// updateEncoderValue calcule la nouvelle valeur après un changement d'encodeur
func UpdateEncoderValue(config EncoderConfig, currentValue float64, change int) float64 {
	step := config.MinStep
	if change > 0 {
		currentValue += step
	} else if change < 0 {
		currentValue -= step
	}
	if currentValue < config.Min {
		currentValue = config.Min
	} else if currentValue > config.Max {
		currentValue = config.Max
	}
	return currentValue
}

// updateReturnNamesForCurrentPage récupère les noms des pistes de retour et met à jour l'affichage
// --- Fonctions déplacées de liveTrackMode.go vers ici ---

// trackUp sélectionne la piste suivante
