package livetrackmode

import (
	"fmt"
	"log"
	"strings"
)

// registerOSCHandlers enregistre les handlers OSC pour le mode Track.
// Appelée depuis Initialize.
func (m *LiveTrackMode) registerOSCHandlers() {
	log.Println("Enregistrement des handlers OSC pour LiveTrackMode...")

	// Définir les adresses et les handlers associés
	handlers := map[string]func([]interface{}){
		// Handlers spécifiques pour les données initiales et le lock
		OscAddressStartListenTrackMode:     func(args []interface{}) { m.handleOscInitialTrackData(args, false) },
		OscAddressStartListenTrackLockMode: func(args []interface{}) { m.handleOscInitialTrackData(args, true) },
		OscAddressTrackLock:                m.handleOscTrackLock,
		OscAddressTrackUnlock:              m.handleOscTrackUnlock,

		// Handlers pour les mises à jour de paramètres de la piste sélectionnée
		OscAddressSelectedTrackVolume:       func(args []interface{}) { m.HandleParameterUpdate("volume", args) },
		OscAddressSelectedTrackColor:        func(args []interface{}) { m.HandleParameterUpdate("color", args) },
		OscAddressSelectedTrackName:         func(args []interface{}) { m.HandleParameterUpdate("name", args) },
		OscAddressSelectedTrackPanning:      func(args []interface{}) { m.HandleParameterUpdate("panning", args) },
		OscAddressSelectedTrackMute:         func(args []interface{}) { m.HandleParameterUpdate("mute", args) },
		OscAddressSelectedTrackSolo:         func(args []interface{}) { m.HandleParameterUpdate("solo", args) },
		OscAddressSelectedTrackMutedViaSolo: func(args []interface{}) { m.HandleParameterUpdate("muted_via_solo", args) },
		OscAddressSelectedTrackArm:          func(args []interface{}) { m.HandleParameterUpdate("arm", args) },
		OscAddressSelectedTrackSends:        func(args []interface{}) { m.HandleParameterUpdate("sends", args) },

		// Handlers pour les mises à jour de paramètres de la piste verrouillée
		OscAddressLockedTrackVolume:       func(args []interface{}) { m.HandleParameterUpdate("volume", args) },
		OscAddressLockedTrackColor:        func(args []interface{}) { m.HandleParameterUpdate("color", args) },
		OscAddressLockedTrackName:         func(args []interface{}) { m.HandleParameterUpdate("name", args) },
		OscAddressLockedTrackPanning:      func(args []interface{}) { m.HandleParameterUpdate("panning", args) },
		OscAddressLockedTrackMute:         func(args []interface{}) { m.HandleParameterUpdate("mute", args) },
		OscAddressLockedTrackSolo:         func(args []interface{}) { m.HandleParameterUpdate("solo", args) },
		OscAddressLockedTrackMutedViaSolo: func(args []interface{}) { m.HandleParameterUpdate("muted_via_solo", args) },
		OscAddressLockedTrackArm:          func(args []interface{}) { m.HandleParameterUpdate("arm", args) },
		OscAddressLockedTrackSends:        func(args []interface{}) { m.HandleParameterUpdate("sends", args) },

		// Handlers pour les relations entre pistes
		OscAddressTrackModeDaughters:     m.handleTrackModeDaughters,
		OscAddressTrackModeSisters:       m.handleTrackModeSisters,
		OscAddressTrackModeParent:        m.handleTrackModeParent,
		OscAddressTrackLockModeDaughters: m.handleTrackLockModeDaughters,
		OscAddressTrackLockModeSisters:   m.handleTrackLockModeSisters,
		OscAddressTrackLockModeParent:    m.handleTrackLockModeParent,

		// Handlers pour la synchronisation
		OscAddressSync:               m.handleSync,
		OscAddressBeats:              m.handleBeats,
		OscAddressSongState:          m.handleSongState,
		OscAddressSongCurrentCueName: m.handleOscCurrentCueName,
		OscAddressSongCuePointNames:  m.handleOscCuePointNames,
	}

	// Enregistrer chaque handler
	for address, handler := range handlers {
		parts := strings.Split(address, "/")
		paramName := "unknown"
		if len(parts) > 0 {
			paramName = parts[len(parts)-1]
		}
		description := fmt.Sprintf("Handler for '%s' parameter update", paramName)
		// Ajuster la description pour les handlers de relation
		if strings.Contains(address, "daughters") || strings.Contains(address, "sisters") || strings.Contains(address, "parent") {
			description = "Handler for track relation updates"
		}

		log.Printf("Registering handler for OSC address: %s", address)
		m.BaseMode.RegisterHandler(address, handler, description)
		m.backgroundHandlers[address] = handler
	}

	log.Println("Handlers OSC enregistrés.")
}

// handleOscTrackUnlock gère le message OSC pour déverrouiller la piste.
func (m *LiveTrackMode) handleOscTrackUnlock(args []interface{}) {
	log.Println("Confirmation de déverrouillage de piste reçue")
	m.mutex.Lock()
	// Quoi qu'il arrive, la confirmation de Live indique que la piste est déverrouillée.
	// Mettons à jour notre état pour correspondre à Live.
	wasLocked := m.isLocked
	m.isLocked = false
	m.lockedTrackData = nil  // Toujours nettoyer les données verrouillées lors du déverrouillage confirmé
	m.lockedTrackIndex = nil // Toujours nettoyer l'index verrouillé
	isQuickView := m.isQuickView

	if !wasLocked {
		log.Println("Confirmation de déverrouillage reçue, mais le mode était déjà déverrouillé localement.")
	}
	m.mutex.Unlock()

	// Mettre à jour l'affichage avec le nouvel état
	m.displayManager.UpdateTrackViewState(isQuickView, false)

	// Mettre à jour l'affichage pour refléter la piste actuellement sélectionnée
	m.mutex.RLock()
	selectedData := m.selectedTrackData
	m.mutex.RUnlock()

	if selectedData != nil {
		var returnNames []string
		if m.trackManager != nil {
			returnNames = m.trackManager.GetReturnTracksName()
		}
		m.mutex.RLock()
		currentPage := m.currentPage
		m.mutex.RUnlock()
		m.displayManager.RefreshFullTrackUIDisplay(selectedData, currentPage, m.IsActive(), returnNames)
	} else {
		m.displayManager.ClearTrackDisplay()
	}
	m.updateReturnNamesForCurrentPage()
}

// handleOscTrackLock gère le message OSC pour verrouiller la piste.
func (m *LiveTrackMode) handleOscTrackLock(args []interface{}) {
	log.Println("Confirmation de verrouillage de piste reçue")
	m.mutex.Lock()
	// La confirmation de Live indique que la piste est verrouillée.
	// Mettons à jour notre état pour correspondre à Live.
	wasLocked := m.isLocked
	m.isLocked = true
	isQuickView := m.isQuickView
	// Ne pas toucher à lockedTrackData/Index ici, ils seront mis à jour par handleOscInitialTrackData

	if wasLocked {
		log.Println("Confirmation de verrouillage reçue, mais le mode était déjà verrouillé localement.")
	}
	m.mutex.Unlock()

	// Mettre à jour l'affichage avec le nouvel état
	m.displayManager.UpdateTrackViewState(isQuickView, true)

	// L'affichage principal de la piste ne change pas ici, il attend les données de /live/start_listen_trackLockMode
}

// handleOscInitialTrackData gère les messages OSC contenant les données initiales d'une piste.
func (m *LiveTrackMode) handleOscInitialTrackData(args []interface{}, isLockMode bool) {
	log.Printf("Données initiales de piste reçues (isLockMode: %t)", isLockMode)

	if len(args) < 10 {
		log.Printf("handleOscInitialTrackData: Pas assez d'arguments reçus (%d), au moins 10 attendus", len(args))
		return
	}

	// Traiter les arguments dans l'ordre avec des récupérations de défauts si nécessaire
	trackIdx, _ := ParseOscInt(args[0])
	trackIsFoldable, _ := ParseOscBool(args[1])
	trackFoldState, _ := ParseOscBool(args[2])
	trackIsGrouped, _ := ParseOscBool(args[3])
	trackMute, _ := ParseOscBool(args[4])
	trackMutedViaSolo, _ := ParseOscBool(args[5])
	trackSolo, _ := ParseOscBool(args[6])
	trackArm, _ := ParseOscInt(args[7])
	trackColorInt, _ := ParseOscInt(args[8])
	trackColor := fmt.Sprintf("%d", trackColorInt)
	trackName, _ := ParseOscString(args[9])
	trackVolume := 0.0
	if len(args) > 10 {
		trackVolume, _ = ParseOscFloat(args[10])
	}

	var displayIndex string
	var formattedTrackNumber string
	var totalTracksIncludingMain int
	var returnTrackCount int
	if m.trackManager != nil {
		totalTracksIncludingMain = m.trackManager.GetTrackCount()
		returnTrackCount = m.trackManager.GetReturnTrackCount()
	} else {
		log.Println("handleOscInitialTrackData: trackManager indisponible, calcul DisplayIndex/FormattedTrackNumber approximatif.")
		totalTracksIncludingMain = trackIdx + 1
		returnTrackCount = 0
	}
	masterTrackIndex := totalTracksIncludingMain - 1
	normalTrackCount := totalTracksIncludingMain - returnTrackCount - 1
	if returnTrackCount > 0 && trackIdx >= normalTrackCount && trackIdx < masterTrackIndex {
		returnIndex := trackIdx - normalTrackCount
		if returnIndex >= 0 && returnIndex < 26 {
			displayIndex = string(rune('A' + returnIndex))
		} else {
			displayIndex = "?"
		}
	} else {
		displayIndex = fmt.Sprintf("%d", trackIdx+1)
	}
	formattedTrackNumber = fmt.Sprintf("track %d/%d", trackIdx+1, totalTracksIncludingMain)

	m.mutex.Lock()
	var targetTrackToUpdate *TrackData
	if isLockMode {
		if m.lockedTrackData == nil {
			m.lockedTrackData = DefaultTrackData()
		}
		targetTrackToUpdate = m.lockedTrackData
		m.lockedTrackIndex = &trackIdx
	} else {
		if m.selectedTrackData == nil {
			m.selectedTrackData = DefaultTrackData()
		}
		targetTrackToUpdate = m.selectedTrackData
	}

	targetTrackToUpdate.TrackIdx = trackIdx
	targetTrackToUpdate.TrackIsFoldable = trackIsFoldable
	targetTrackToUpdate.TrackFoldState = trackFoldState
	targetTrackToUpdate.TrackIsGrouped = trackIsGrouped
	targetTrackToUpdate.Mute = trackMute
	targetTrackToUpdate.MutedViaSolo = trackMutedViaSolo
	targetTrackToUpdate.Solo = trackSolo
	targetTrackToUpdate.Arm = trackArm
	targetTrackToUpdate.Color = trackColor
	targetTrackToUpdate.Name = trackName
	targetTrackToUpdate.Volume = trackVolume
	if !trackIsFoldable {
		targetTrackToUpdate.DaughterNames = []string{}
	}
	if !trackIsGrouped {
		targetTrackToUpdate.SisterNames = []string{}
		targetTrackToUpdate.ParentName = ""
	}
	targetTrackToUpdate.DisplayIndex = displayIndex
	targetTrackToUpdate.FormattedTrackNumber = formattedTrackNumber

	if isLockMode {
		log.Printf("Données de piste verrouillée mises à jour pour la piste %d (%s)", targetTrackToUpdate.TrackIdx, targetTrackToUpdate.Name)
	} else {
		log.Printf("Données de piste sélectionnée mises à jour pour la piste %d (%s)", targetTrackToUpdate.TrackIdx, targetTrackToUpdate.Name)
	}
	m.mutex.Unlock()

	// Logique de rafraîchissement de l'affichage
	shouldRefreshDisplay := false
	currentModeIsActive := m.IsActive() // L'état actif du mode détermine la priorité d'envoi
	currentPage := m.currentPage        // Lecture de currentPage ici, car elle est utilisée si shouldRefreshDisplay est true

	if isLockMode {
		// Si le message concerne la lockedTrack, on rafraîchit l'affichage SEULEMENT si le mode Track est actif
		// (car c'est la lockedTrack qui est affichée).
		if targetTrackToUpdate.TrackIdx == trackIdx && currentModeIsActive { // Vérifier aussi trackIdx pour être sûr
			shouldRefreshDisplay = true
			log.Printf("handleOscInitialTrackData: Rafraîchissement planifié pour lockedTrackData (piste %d), car mode actif.", trackIdx)
		} else {
			log.Printf("handleOscInitialTrackData: Pas de rafraîchissement pour lockedTrackData (piste %d). Mode actif: %t, Piste concernée par message: %t", trackIdx, currentModeIsActive, targetTrackToUpdate.TrackIdx == trackIdx)
		}
	} else {
		// Le message concerne selectedTrackData.
		// Appliquer la condition d'exclusion: (Actif && !QuickView && Locked)
		currentIsQuickView := m.GetQuickViewState()
		currentIsLocked := m.GetLockState()
		excludeDisplayUpdate := currentModeIsActive && !currentIsQuickView && currentIsLocked

		if targetTrackToUpdate.TrackIdx == trackIdx && !excludeDisplayUpdate {
			shouldRefreshDisplay = true
			log.Printf("handleOscInitialTrackData: Rafraîchissement planifié pour selectedTrackData (piste %d). Exclu: %t (Actif: %t, QuickView: %t, Locked: %t)", trackIdx, excludeDisplayUpdate, currentModeIsActive, currentIsQuickView, currentIsLocked)
		} else {
			log.Printf("handleOscInitialTrackData: Pas de rafraîchissement pour selectedTrackData (piste %d). Piste concernée: %t, Exclu: %t", trackIdx, targetTrackToUpdate.TrackIdx == trackIdx, excludeDisplayUpdate)
		}
	}

	if shouldRefreshDisplay {
		var returnNames []string
		if m.trackManager != nil {
			returnNames = m.trackManager.GetReturnTracksName()
		}
		// Le troisième argument de RefreshFullTrackUIDisplay est 'isActive', qui détermine la priorité d'envoi.
		// Il doit être l'état actuel du mode Track.
		m.displayManager.RefreshFullTrackUIDisplay(targetTrackToUpdate, currentPage, currentModeIsActive, returnNames)
	} else {
		log.Printf("handleOscInitialTrackData: Rafraîchissement de l'affichage global ignoré pour la piste %d (isLockMode: %t).", trackIdx, isLockMode)
	}
}

// handleOscParameterUpdate gère les messages OSC de mise à jour des paramètres.
func (m *LiveTrackMode) handleOscParameterUpdate(args []interface{}, address string) {
	// Extraire le nom du paramètre de la fin de l'adresse
	parts := strings.Split(address, "/")
	if len(parts) == 0 {
		log.Printf("Erreur: Impossible d'extraire le nom du paramètre depuis l'adresse OSC: %s", address)
		return
	}
	paramName := parts[len(parts)-1]

	// On évite de logger les paramètres à haute fréquence comme volume/panning
	if paramName != "volume" && paramName != "panning" && paramName != "sends" {
		log.Printf("Mise à jour de paramètre OSC reçue: '%s'", paramName)
	}

	m.HandleParameterUpdate(paramName, args) // Appel à la méthode dans liveTrackMode.go
}

// --- Fonctions utilitaires pour le parsing OSC (peuvent être déplacées dans un package utils plus tard) ---

func ParseOscInt(val interface{}) (int, bool) {
	// OSC peut envoyer des int32 ou int64
	switch v := val.(type) {
	case int:
		return v, true
	case int32:
		return int(v), true
	case int64:
		// Attention: perte potentielle de précision si > max int
		return int(v), true
	case float32:
		return int(v), true
	case float64:
		return int(v), true
	default:
		log.Printf("Erreur de parsing OSC: type inattendu pour int: %T", val)
		return 0, false
	}
}

func ParseOscFloat(val interface{}) (float64, bool) {
	// OSC peut envoyer des float32, float64 ou même des entiers qu'il faut convertir
	switch v := val.(type) {
	case float32:
		return float64(v), true
	case float64:
		return v, true
	case int:
		return float64(v), true
	case int32:
		return float64(v), true
	case int64:
		return float64(v), true
	default:
		log.Printf("Erreur de parsing OSC: type inattendu pour float: %T", val)
		return 0.0, false
	}
}

func ParseOscBool(val interface{}) (bool, bool) {
	// OSC peut envoyer des booléens ou des entiers (0/1)
	switch v := val.(type) {
	case bool:
		return v, true
	case int, int32, int64:
		intValue, ok := ParseOscInt(v)
		if ok {
			return intValue != 0, true
		}
		return false, false
	case float32, float64:
		floatValue, ok := ParseOscFloat(v)
		if ok {
			return floatValue != 0.0, true
		}
		return false, false
	default:
		log.Printf("Erreur de parsing OSC: type inattendu pour bool: %T", val)
		return false, false
	}
}

func ParseOscString(val interface{}) (string, bool) {
	switch v := val.(type) {
	case string:
		return v, true
	case int, int32, int64:
		// Convertir les entiers en chaînes pour les couleurs au format numérique
		intValue, ok := ParseOscInt(v)
		if ok {
			return fmt.Sprintf("%d", intValue), true
		}
		return "", false
	case float32, float64:
		// Convertir aussi les nombres à virgule en chaînes
		floatValue, ok := ParseOscFloat(v)
		if ok {
			return fmt.Sprintf("%f", floatValue), true
		}
		return "", false
	default:
		log.Printf("Erreur de parsing OSC: type inattendu pour string: %T", val)
		return "", false
	}
}

// --- Méthodes pour les relations entre pistes ---

// handleTrackModeDaughters gère les messages OSC pour les pistes filles en mode normal
func (m *LiveTrackMode) handleTrackModeDaughters(args []interface{}) {
	log.Println("Réception des pistes filles en mode normal (concerne la piste sélectionnée)")
	daughterNames := make([]string, 0, len(args))
	for _, arg := range args {
		if name, ok := arg.(string); ok {
			daughterNames = append(daughterNames, name)
		}
	}

	m.mutex.RLock() // Utiliser RLock pour lire selectedTrackData initialement
	selectedTrack := m.selectedTrackData
	m.mutex.RUnlock()

	if selectedTrack != nil {
		m.mutex.Lock() // Lock pour modifier selectedTrack.DaughterNames
		previousDaughterCount := len(selectedTrack.DaughterNames)
		selectedTrack.DaughterNames = daughterNames
		log.Printf("handleTrackModeDaughters: Données filles de selectedTrack '%s' (index %d) mises à jour. De %d à %d filles.", selectedTrack.Name, selectedTrack.TrackIdx, previousDaughterCount, len(daughterNames))
		m.mutex.Unlock()

		// Condition pour l'affichage basée sur la nouvelle logique
		isActive := m.IsActive() // Utilise le getter thread-safe
		isQuickView := m.GetQuickViewState()
		isLocked := m.GetLockState()

		excludeDisplayUpdate := isActive && !isQuickView && isLocked

		if !excludeDisplayUpdate {
			if m.displayManager != nil {
				log.Printf("handleTrackModeDaughters: Mise à jour de l'affichage des filles pour selectedTrack. Exclu: %t (Actif: %t, QuickView: %t, Locked: %t)", excludeDisplayUpdate, isActive, isQuickView, isLocked)
				m.displayManager.HandleTrackDaughters(daughterNames)
			}
		} else {
			log.Printf("handleTrackModeDaughters: Affichage des filles de selectedTrack IGNORÉ. Exclu: %t (Actif: %t, QuickView: %t, Locked: %t)", excludeDisplayUpdate, isActive, isQuickView, isLocked)
		}
	} else {
		log.Println("handleTrackModeDaughters: selectedTrackData est nil, impossible de stocker ou d'afficher DaughterNames.")
	}
}

// handleTrackModeSisters gère les messages OSC pour les pistes soeurs en mode normal
func (m *LiveTrackMode) handleTrackModeSisters(args []interface{}) {
	log.Println("Réception des pistes soeurs en mode normal (concerne la piste sélectionnée)")
	sisterNames := make([]string, 0, len(args))
	for _, arg := range args {
		if name, ok := arg.(string); ok {
			sisterNames = append(sisterNames, name)
		}
	}

	m.mutex.RLock()
	selectedTrack := m.selectedTrackData
	m.mutex.RUnlock()

	if selectedTrack != nil {
		m.mutex.Lock()
		previousSisterCount := len(selectedTrack.SisterNames)
		selectedTrack.SisterNames = sisterNames
		log.Printf("handleTrackModeSisters: Données soeurs de selectedTrack '%s' (index %d) mises à jour. De %d à %d soeurs.", selectedTrack.Name, selectedTrack.TrackIdx, previousSisterCount, len(sisterNames))
		m.mutex.Unlock()

		isActive := m.IsActive()
		isQuickView := m.GetQuickViewState()
		isLocked := m.GetLockState()
		excludeDisplayUpdate := isActive && !isQuickView && isLocked

		if !excludeDisplayUpdate {
			if m.displayManager != nil {
				log.Printf("handleTrackModeSisters: Mise à jour de l'affichage des soeurs pour selectedTrack. Exclu: %t (Actif: %t, QuickView: %t, Locked: %t)", excludeDisplayUpdate, isActive, isQuickView, isLocked)
				m.displayManager.HandleTrackSisters(sisterNames)
			}
		} else {
			log.Printf("handleTrackModeSisters: Affichage des soeurs de selectedTrack IGNORÉ. Exclu: %t (Actif: %t, QuickView: %t, Locked: %t)", excludeDisplayUpdate, isActive, isQuickView, isLocked)
		}
	} else {
		log.Println("handleTrackModeSisters: selectedTrackData est nil, impossible de stocker ou d'afficher SisterNames.")
	}
}

// handleTrackModeParent gère les messages OSC pour la piste parente en mode normal
func (m *LiveTrackMode) handleTrackModeParent(args []interface{}) {
	log.Println("Réception de la piste parente en mode normal (concerne la piste sélectionnée)")
	parentName := ""
	if len(args) > 0 {
		if name, ok := args[0].(string); ok {
			parentName = name
		}
	} else {
		log.Println("Aucun argument reçu pour la piste parente en mode normal")
	}

	m.mutex.RLock()
	selectedTrack := m.selectedTrackData
	m.mutex.RUnlock()

	if selectedTrack != nil {
		m.mutex.Lock()
		previousParentName := selectedTrack.ParentName
		selectedTrack.ParentName = parentName
		log.Printf("handleTrackModeParent: Donnée parent de selectedTrack '%s' (index %d) mise à jour. De '%s' à '%s'.", selectedTrack.Name, selectedTrack.TrackIdx, previousParentName, parentName)
		m.mutex.Unlock()

		isActive := m.IsActive()
		isQuickView := m.GetQuickViewState()
		isLocked := m.GetLockState()
		excludeDisplayUpdate := isActive && !isQuickView && isLocked

		if !excludeDisplayUpdate {
			if m.displayManager != nil {
				log.Printf("handleTrackModeParent: Mise à jour de l'affichage du parent pour selectedTrack. Exclu: %t (Actif: %t, QuickView: %t, Locked: %t)", excludeDisplayUpdate, isActive, isQuickView, isLocked)
				m.displayManager.HandleTrackParent(parentName)
			}
		} else {
			log.Printf("handleTrackModeParent: Affichage du parent de selectedTrack IGNORÉ. Exclu: %t (Actif: %t, QuickView: %t, Locked: %t)", excludeDisplayUpdate, isActive, isQuickView, isLocked)
		}
	} else {
		log.Println("handleTrackModeParent: selectedTrackData est nil, impossible de stocker ou d'afficher ParentName.")
	}
}

// handleTrackLockModeDaughters gère les messages OSC pour les pistes filles en mode verrouillé
func (m *LiveTrackMode) handleTrackLockModeDaughters(args []interface{}) {
	log.Println("Réception des pistes filles en mode verrouillé (concerne la piste verrouillée)")
	daughterNames := make([]string, 0, len(args))
	for _, arg := range args {
		if name, ok := arg.(string); ok {
			daughterNames = append(daughterNames, name)
		}
	}

	m.mutex.Lock()
	isLocked := m.isLocked
	lockedTrack := m.lockedTrackData
	m.mutex.Unlock()

	if isLocked && lockedTrack != nil {
		m.mutex.Lock()
		lockedTrack.DaughterNames = daughterNames
		m.mutex.Unlock()
		if m.displayManager != nil {
			log.Println("Mode verrouillé : Mise à jour de l'affichage des filles pour la piste verrouillée.")
			m.displayManager.HandleTrackDaughters(daughterNames)
		}
	} else if !isLocked {
		// Si PAS verrouillé, ces informations ne sont pas pertinentes pour l'affichage actuel.
		log.Println("Mode non-verrouillé : Infos filles pour piste verrouillée reçues, mais non pertinentes pour l'affichage actuel.")
	} else {
		log.Println("Mode verrouillé, mais lockedTrackData est nil, impossible de stocker DaughterNames")
	}
}

// handleTrackLockModeSisters gère les messages OSC pour les pistes soeurs en mode verrouillé
func (m *LiveTrackMode) handleTrackLockModeSisters(args []interface{}) {
	log.Println("Réception des pistes soeurs en mode verrouillé (concerne la piste verrouillée)")
	sisterNames := make([]string, 0, len(args))
	for _, arg := range args {
		if name, ok := arg.(string); ok {
			sisterNames = append(sisterNames, name)
		}
	}

	m.mutex.Lock()
	isLocked := m.isLocked
	lockedTrack := m.lockedTrackData
	m.mutex.Unlock()

	if isLocked && lockedTrack != nil {
		m.mutex.Lock()
		lockedTrack.SisterNames = sisterNames
		m.mutex.Unlock()
		if m.displayManager != nil {
			log.Println("Mode verrouillé : Mise à jour de l'affichage des soeurs pour la piste verrouillée.")
			m.displayManager.HandleTrackSisters(sisterNames)
		}
	} else if !isLocked {
		log.Println("Mode non-verrouillé : Infos soeurs pour piste verrouillée reçues, mais non pertinentes pour l'affichage actuel.")
	} else {
		log.Println("Mode verrouillé, mais lockedTrackData est nil, impossible de stocker SisterNames")
	}
}

// handleTrackLockModeParent gère les messages OSC pour la piste parente en mode verrouillé
func (m *LiveTrackMode) handleTrackLockModeParent(args []interface{}) {
	log.Println("Réception de la piste parente en mode verrouillé (concerne la piste verrouillée)")
	parentName := ""
	if len(args) > 0 {
		if name, ok := args[0].(string); ok {
			parentName = name
		}
	} else {
		log.Println("Aucun argument reçu pour la piste parente en mode verrouillé")
		// Pas besoin de continuer si pas de nom de parent
	}

	m.mutex.Lock()
	isLocked := m.isLocked
	lockedTrack := m.lockedTrackData
	m.mutex.Unlock()

	if isLocked && lockedTrack != nil {
		m.mutex.Lock()
		lockedTrack.ParentName = parentName
		m.mutex.Unlock()
		if m.displayManager != nil {
			log.Println("Mode verrouillé : Mise à jour de l'affichage du parent pour la piste verrouillée.")
			m.displayManager.HandleTrackParent(parentName) // parentName peut être vide
		}
	} else if !isLocked {
		log.Println("Mode non-verrouillé : Info parent pour piste verrouillée reçue, mais non pertinente pour l'affichage actuel.")
	} else {
		log.Println("Mode verrouillé, mais lockedTrackData est nil, impossible de stocker ParentName")
	}
}

// handleSync gère les messages OSC de synchronisation
func (m *LiveTrackMode) handleSync(args []interface{}) {
	if len(args) != 8 {
		log.Printf("handleSync: Nombre incorrect d'arguments: %d (attendu: 8)", len(args))
		return
	}

	// Extraire les arguments
	bar, ok1 := ParseOscInt(args[0])
	beat, ok2 := ParseOscInt(args[1])
	subdivision, ok3 := ParseOscInt(args[2])
	time, ok4 := ParseOscFloat(args[3])
	tempo, ok5 := ParseOscFloat(args[4])
	signatureNum, ok6 := ParseOscInt(args[5])
	signatureDenom, ok7 := ParseOscInt(args[6])
	isPlaying, ok8 := ParseOscInt(args[7])

	// Vérifier que tous les arguments ont été correctement parsés
	if !ok1 || !ok2 || !ok3 || !ok4 || !ok5 || !ok6 || !ok7 || !ok8 {
		log.Println("handleSync: Erreur lors du parsing des arguments")
		return
	}

	// Envoyer les données au display manager
	m.displayManager.HandleSyncMessage(bar, beat, subdivision, float32(time), float32(tempo), signatureNum, signatureDenom, isPlaying)
}

// handleBeats gère les messages OSC de beats
func (m *LiveTrackMode) handleBeats(args []interface{}) {
	if len(args) != 4 {
		log.Printf("handleBeats: Nombre incorrect d'arguments: %d (attendu: 4)", len(args))
		return
	}

	// Extraire les arguments
	bar, ok1 := ParseOscInt(args[0])
	beat, ok2 := ParseOscInt(args[1])
	subdivision, ok3 := ParseOscInt(args[2])
	time, ok4 := ParseOscFloat(args[3])

	// Vérifier que tous les arguments ont été correctement parsés
	if !ok1 || !ok2 || !ok3 || !ok4 {
		log.Println("handleBeats: Erreur lors du parsing des arguments")
		return
	}

	// Envoyer les données au display manager
	m.displayManager.HandleBeatsMessage(bar, beat, subdivision, float32(time))
}

// handleSongState gère les messages OSC pour l'état du morceau
func (m *LiveTrackMode) handleSongState(args []interface{}) {
	log.Println("Réception de l'état du morceau")

	if len(args) != 6 {
		log.Printf("handleSongState: Nombre incorrect d'arguments: %d (attendu: 6)", len(args))
		return
	}

	// Extraire les arguments
	isPlaying, ok1 := ParseOscInt(args[0])
	recordMode, ok2 := ParseOscInt(args[1])
	metronome, ok3 := ParseOscInt(args[2])
	loop, ok4 := ParseOscInt(args[3])
	punchIn, ok5 := ParseOscInt(args[4])
	punchOut, ok6 := ParseOscInt(args[5])

	// Vérifier que tous les arguments ont été correctement parsés
	if !ok1 || !ok2 || !ok3 || !ok4 || !ok5 || !ok6 {
		log.Println("handleSongState: Erreur lors du parsing des arguments")
		return
	}

	// Envoyer les données au display manager (cette méthode sera créée ensuite)
	if m.displayManager != nil {
		m.displayManager.HandleSongStateMessage(isPlaying, recordMode, metronome, loop, punchIn, punchOut)
	} else {
		log.Println("handleSongState: displayManager est nil")
	}
}

// HandleParameterUpdate gère la mise à jour d'un paramètre de piste
func (m *LiveTrackMode) HandleParameterUpdate(param string, args []interface{}) {
	// Réduire la verbosité des logs pour les paramètres fréquemment mis à jour
	verbose := !(param == "volume" || param == "panning" || param == "sends")

	if verbose {
		log.Printf("HandleParameterUpdate: param '%s' avec args %v", param, args)
	}

	if len(args) == 0 {
		log.Printf("HandleParameterUpdate: Aucun argument reçu pour param '%s'", param)
		return
	}

	// Le premier argument est généralement l'index de la piste
	trackIndex, ok := ParseOscInt(args[0])
	if !ok {
		log.Printf("HandleParameterUpdate: Échec d'analyse de l'index de piste: %v", args[0])
		return
	}

	// Déterminer si l'adresse OSC d'origine du paramètre était pour la selectedTrack (non lockedTrack).
	// Ceci est crucial pour la logique de m.shouldUpdateUI.
	// Nous ne pouvons pas directement accéder à l'adresse OSC ici car elle a été résolue par le routeur.
	// Nous devons donc déduire cela en fonction du contexte actuel (isLocked, lockedTrackIndex).
	// Si le mode est verrouillé et que trackIndex correspond à lockedTrackIndex, alors ce paramètre
	// est logiquement pour la lockedTrack, même si l'URL originale était /live/trackMode/get/param.
	// Le nom du paramètre lui-même (venant de la map dans registerOSCHandlers) nous dit si c'était
	// une adresse LockMode ou non.
	// Pour simplifier : si `param` est un nom qui existe dans les adresses `OscAddressLockedTrack...`,
	// alors c'est `isSelectedTrackParamURL = false`.
	// Ceci est une approximation car `param` est juste la fin de l'adresse. Il faudrait passer l'adresse complète.

	// Solution alternative: La fonction appelante handleOscParameterUpdate dans ce fichier
	// *connaît* l'adresse OSC complète. Nous allons la modifier pour qu'elle passe cette information.
	// Pour l'instant, nous allons devoir faire une supposition moins précise ou attendre de refactoriser cela.

	// Pour l'instant, on se base sur le fait que `HandleParameterUpdate` est appelé avec `param` qui vient
	// de la résolution d'adresse. Si cette adresse était une adresse `Locked`, alors la logique dans
	// `registerOSCHandlers` aura déjà acheminé vers le bon `targetTrack` (lockedData).
	// On va déterminer `isSelectedTrackParamURL` en regardant si le `targetTrack` actuel est le `selectedTrackData`.

	m.mutex.RLock()
	isLockedGlobal := m.isLocked
	var targetTrack *TrackData
	isSelectedTrackCurrentlyTargeted := false

	if isLockedGlobal && m.lockedTrackIndex != nil && *m.lockedTrackIndex == trackIndex {
		targetTrack = m.lockedTrackData
		// isSelectedTrackParamURL reste false (paramètre pour locked track)
	} else if !isLockedGlobal && m.selectedTrackData != nil && m.selectedTrackData.TrackIdx == trackIndex {
		targetTrack = m.selectedTrackData
		isSelectedTrackCurrentlyTargeted = true // paramètre pour selected track
	} else if m.selectedTrackData != nil && m.selectedTrackData.TrackIdx == trackIndex {
		// Cas où le mode est verrouillé, mais le message OSC concerne la selectedTrack (qui n'est pas la lockedTrack)
		// Ex: /live/trackMode/get/volume pour une piste X, alors que le mode est lock sur Y.
		targetTrack = m.selectedTrackData
		isSelectedTrackCurrentlyTargeted = true
	} else if m.lockedTrackData != nil && m.lockedTrackIndex != nil && *m.lockedTrackIndex == trackIndex {
		// Mode non verrouillé mais le message concerne l'ancienne locked track (peu probable via OSC standard)
		targetTrack = m.lockedTrackData
		// isSelectedTrackParamURL reste false
	} else {
		// Ni la selectedTrack ni la lockedTrack (si applicable) ne correspondent à trackIndex.
		// Ou les structures de données sont nil.
		m.mutex.RUnlock()
		if verbose {
			var selectedIdxLog string
			if m.selectedTrackData != nil {
				selectedIdxLog = fmt.Sprintf("%d", m.selectedTrackData.TrackIdx)
			} else {
				selectedIdxLog = "nil"
			}
			var lockedIdxLog string
			if m.lockedTrackIndex != nil {
				lockedIdxLog = fmt.Sprintf("%d", *m.lockedTrackIndex)
			} else {
				lockedIdxLog = "nil"
			}
			log.Printf("HandleParameterUpdate: Aucune piste cible (selected ou locked) ne correspond à l'index %d pour le paramètre '%s'. Données: selectedIdx %s, lockedIdx %s",
				trackIndex, param, selectedIdxLog, lockedIdxLog)
		}
		return
	}
	m.mutex.RUnlock()

	if targetTrack == nil { // Double vérification après la logique complexe ci-dessus.
		log.Printf("HandleParameterUpdate: targetTrack est nil après la logique de sélection pour l'index %d, param '%s'. Ce ne devrait pas arriver.", trackIndex, param)
		return
	}

	// Mise à jour de la donnée (toujours effectuée si targetTrack est trouvé)
	updated := false
	if len(args) > 1 {
		switch param {
		case "volume":
			if val, ok := ParseOscFloat(args[1]); ok {
				targetTrack.Volume = val
				updated = true
				if m.shouldUpdateUI(trackIndex, isSelectedTrackCurrentlyTargeted) {
					m.displayManager.UpdateVolumeDisplay(val, m.displayManager.volumeConverter.ToDb(val))
				}
			} else {
				log.Printf("HandleParameterUpdate: Échec d'analyse de la valeur volume: %v", args[1])
			}

		case "panning":
			if val, ok := ParseOscFloat(args[1]); ok {
				targetTrack.Panning = val
				updated = true
				if m.shouldUpdateUI(trackIndex, isSelectedTrackCurrentlyTargeted) {
					m.displayManager.UpdatePanningDisplay(val)
				}
			} else {
				log.Printf("HandleParameterUpdate: Échec d'analyse de la valeur panning: %v", args[1])
			}

		case "sends":
			if len(args) >= 3 {
				sendIndex, sendOk := ParseOscInt(args[1])
				sendValue, valueOk := ParseOscFloat(args[2])

				if !sendOk || !valueOk {
					log.Printf("HandleParameterUpdate: Échec d'analyse de l'index send ou valeur: %v, %v", args[1], args[2])
					// Ne pas retourner, car on a déjà targetTrack. On logge juste.
				} else {
					m.mutex.Lock() // Protéger l'accès concurrentiel à targetTrack.Sends
					if targetTrack.Sends == nil {
						targetTrack.Sends = []float64{}
					}
					for len(targetTrack.Sends) <= sendIndex {
						targetTrack.Sends = append(targetTrack.Sends, 0.0)
					}
					targetTrack.Sends[sendIndex] = sendValue
					m.mutex.Unlock()
					updated = true

					if m.shouldUpdateUI(trackIndex, isSelectedTrackCurrentlyTargeted) {
						m.mutex.RLock()
						currentPage := m.currentPage
						m.mutex.RUnlock()
						startSendIndex := (currentPage - 1) * SendsPerPage
						endSendIndex := startSendIndex + SendsPerPage - 1
						if sendIndex >= startSendIndex && sendIndex <= endSendIndex {
							slotIndex := sendIndex - startSendIndex
							slotLetter := string(rune('A' + slotIndex))
							m.displayManager.UpdateSingleSend(slotLetter, sendValue)
						}
					}
				}
			} else {
				log.Printf("HandleParameterUpdate: Arguments insuffisants pour sends: %v", args)
			}

		case "name":
			if val, ok := ParseOscString(args[1]); ok {
				targetTrack.Name = val
				updated = true
				if m.shouldUpdateUI(trackIndex, isSelectedTrackCurrentlyTargeted) {
					m.displayManager.UpdateNameDisplay(val)
				}
			} else {
				log.Printf("HandleParameterUpdate: Échec d'analyse de name: %v", args[1])
			}

		case "color":
			if val, ok := ParseOscString(args[1]); ok {
				targetTrack.Color = val
				updated = true
				if m.shouldUpdateUI(trackIndex, isSelectedTrackCurrentlyTargeted) {
					m.displayManager.UpdateColorDisplay(val)
				}
			} else {
				log.Printf("HandleParameterUpdate: Échec d'analyse de color: %v", args[1])
			}

		case "mute":
			if val, ok := ParseOscBool(args[1]); ok {
				targetTrack.Mute = val
				updated = true
				if m.shouldUpdateUI(trackIndex, isSelectedTrackCurrentlyTargeted) {
					var returnNames []string
					if m.trackManager != nil {
						returnNames = m.trackManager.GetReturnTracksName()
					}
					m.mutex.RLock()
					currentPage := m.currentPage
					m.mutex.RUnlock()
					m.displayManager.RefreshFullTrackUIDisplay(targetTrack, currentPage, m.IsActive(), returnNames)
				}
			} else {
				log.Printf("HandleParameterUpdate: Échec d'analyse de mute: %v", args[1])
			}

		case "solo":
			if val, ok := ParseOscBool(args[1]); ok {
				targetTrack.Solo = val
				updated = true
				if m.shouldUpdateUI(trackIndex, isSelectedTrackCurrentlyTargeted) {
					var returnNames []string
					if m.trackManager != nil {
						returnNames = m.trackManager.GetReturnTracksName()
					}
					m.mutex.RLock()
					currentPage := m.currentPage
					m.mutex.RUnlock()
					m.displayManager.RefreshFullTrackUIDisplay(targetTrack, currentPage, m.IsActive(), returnNames)
				}
			} else {
				log.Printf("HandleParameterUpdate: Échec d'analyse de solo: %v", args[1])
			}

		case "muted_via_solo":
			if val, ok := ParseOscBool(args[1]); ok {
				targetTrack.MutedViaSolo = val
				updated = true
				if m.shouldUpdateUI(trackIndex, isSelectedTrackCurrentlyTargeted) {
					var returnNames []string
					if m.trackManager != nil {
						returnNames = m.trackManager.GetReturnTracksName()
					}
					m.mutex.RLock()
					currentPage := m.currentPage
					m.mutex.RUnlock()
					m.displayManager.RefreshFullTrackUIDisplay(targetTrack, currentPage, m.IsActive(), returnNames)
				}
			} else {
				log.Printf("HandleParameterUpdate: Échec d'analyse de muted_via_solo: %v", args[1])
			}

		case "arm":
			if boolVal, ok := ParseOscBool(args[1]); ok {
				if boolVal {
					targetTrack.Arm = 1
				} else {
					targetTrack.Arm = 0
				}
				updated = true
				if m.shouldUpdateUI(trackIndex, isSelectedTrackCurrentlyTargeted) {
					m.displayManager.UpdateArmDisplay(targetTrack.Arm)
				}
			} else {
				if intVal, ok := ParseOscInt(args[1]); ok {
					targetTrack.Arm = intVal
					updated = true
					if m.shouldUpdateUI(trackIndex, isSelectedTrackCurrentlyTargeted) {
						m.displayManager.UpdateArmDisplay(intVal)
					}
				} else {
					log.Printf("HandleParameterUpdate: Échec d'analyse de arm comme booléen ou entier: %v (%T)", args[1], args[1])
				}
			}

		default:
			log.Printf("HandleParameterUpdate: Paramètre non géré '%s'", param)
		}
	} else {
		log.Printf("HandleParameterUpdate: Arguments insuffisants pour param '%s'", param)
	}

	if updated && verbose {
		var dataType string
		if isSelectedTrackCurrentlyTargeted {
			dataType = "selectedTrackData"
		} else {
			dataType = "lockedTrackData"
		}
		log.Printf("HandleParameterUpdate: Piste %d param '%s' (%s) mise à jour. Affichage UI: %t",
			trackIndex, param, dataType,
			m.shouldUpdateUI(trackIndex, isSelectedTrackCurrentlyTargeted))
	}
}

// handleOscCurrentCueName gère le message OSC pour le nom de la cue courante.
func (m *LiveTrackMode) handleOscCurrentCueName(args []interface{}) {
	log.Println("Réception du nom de la cue courante")

	if len(args) != 1 {
		log.Printf("handleOscCurrentCueName: Nombre incorrect d'arguments: %d (attendu: 1)", len(args))
		return
	}

	cueName, ok := ParseOscString(args[0])
	if !ok {
		log.Printf("handleOscCurrentCueName: Échec d'analyse de l'argument du nom de la cue: %v", args[0])
		return
	}

	// Envoyer le nom de la cue au display manager
	if m.displayManager != nil {
		m.displayManager.UpdateCueNameDisplay(cueName)
	} else {
		log.Println("handleOscCurrentCueName: displayManager est nil")
	}
}

// handleOscCuePointNames gère le message OSC pour les noms de cue points.
func (m *LiveTrackMode) handleOscCuePointNames(args []interface{}) {
	log.Println("Réception des noms de cue points")

	if len(args) != 1 {
		log.Printf("handleOscCuePointNames: Nombre incorrect d'arguments: %d (attendu: 1)", len(args))
		return
	}

	// L'argument devrait être un slice d'interface{} contenant les noms
	cuePointsInterface, ok := args[0].([]interface{})
	if !ok {
		log.Printf("handleOscCuePointNames: L'argument n'est pas un slice d'interface{}: %T", args[0])
		return
	}

	// Convertir en slice de strings et limiter à 64 éléments maximum
	var cuePointNames []string
	maxCuePoints := 64
	for i, cuePointInterface := range cuePointsInterface {
		if i >= maxCuePoints {
			log.Printf("handleOscCuePointNames: Limitation à %d cue points (reçu %d)", maxCuePoints, len(cuePointsInterface))
			break
		}

		if cueName, ok := ParseOscString(cuePointInterface); ok {
			cuePointNames = append(cuePointNames, cueName)
		} else {
			log.Printf("handleOscCuePointNames: Échec d'analyse du nom de cue point à l'index %d: %v", i, cuePointInterface)
			// Continuer avec les autres éléments même si un échoue
		}
	}

	// Envoyer les noms de cue points au display manager
	if m.displayManager != nil {
		m.displayManager.HandleCuePointNames(cuePointNames)
	} else {
		log.Println("handleOscCuePointNames: displayManager est nil")
	}
}
