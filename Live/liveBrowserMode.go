package live

import (
	"fmt"
	"log"
	communication "oscbridge/Communication"
	"strings"
)

// BrowserItem représente un élément dans le navigateur
type BrowserItem struct {
	Index    int
	Name     string
	Type     string
	Loadable bool
	IsFolder bool
}

// BrowserState représente l'état du navigateur
type BrowserState struct {
	Path             []interface{}
	Items            []BrowserItem
	IsPreviewEnabled bool
	IsHotswapEnabled bool
	CurrentPage      int
	TotalPages       int
	TotalItems       int
	DisplayPath      []string
}

// LastPreviewedItem garde une trace du dernier élément prévisualisé
type LastPreviewedItem struct {
	Index int
	Page  int
}

// LiveBrowserMode gère la navigation dans le navigateur de Live
type LiveBrowserMode struct {
	*communication.BaseMode
	trackManager  *LiveTrackManager
	commManager   *communication.CommunicationManager
	state         *BrowserState
	lastPreviewed *LastPreviewedItem
	isActive      bool
}

// Constantes pour les adresses OSC
const (
	browserGetItemsAddress       = "/live/browser/get/items"
	browserHotswapEnableAddress  = "/live/browser/hotswap/enable"
	browserHotswapDisableAddress = "/live/browser/hotswap/disable"
	browserLoadAddress           = "/live/browser/load"
	browserPreviewAddress        = "/live/browser/preview"
)

// NewLiveBrowserMode crée une nouvelle instance du mode navigateur
func NewLiveBrowserMode(trackManager *LiveTrackManager, commManager *communication.CommunicationManager) *LiveBrowserMode {
	mode := &LiveBrowserMode{
		BaseMode:     communication.NewBaseMode(),
		trackManager: trackManager,
		commManager:  commManager,
		state: &BrowserState{
			Path:             make([]interface{}, 0),
			Items:            make([]BrowserItem, 0),
			IsPreviewEnabled: false,
			IsHotswapEnabled: false,
			CurrentPage:      1,
			TotalPages:       1,
			TotalItems:       0,
			DisplayPath:      make([]string, 0),
		},
		lastPreviewed: &LastPreviewedItem{
			Index: -1,
			Page:  -1,
		},
		isActive: false,
	}

	// Enregistrer les handlers OSC
	mode.RegisterHandler(browserGetItemsAddress, mode.handleGetItems, "Gestion des items du browser")
	mode.RegisterHandler(browserHotswapEnableAddress, mode.handleHotswapEnable, "Activation du hotswap")
	mode.RegisterHandler(browserHotswapDisableAddress, mode.handleHotswapDisable, "Désactivation du hotswap")
	mode.RegisterHandler(browserLoadAddress, mode.handleLoad, "Chargement d'un item")
	mode.RegisterHandler(browserPreviewAddress, mode.handlePreview, "Preview d'un item")

	return mode
}

// Initialize initialise le mode browser
func (m *LiveBrowserMode) Initialize(service communication.OscService) {
	log.Println("Initialisation du mode browser...")
	m.BaseMode.Initialize(service)

	// Envoyer le message initial
	m.commManager.SendMessage("bw,0", m.IsActive())

	// Aller à la page d'accueil
	m.goHome()

	log.Println("Mode browser initialisé avec succès.")
}

// Activate active le mode browser
func (m *LiveBrowserMode) Activate() {
	log.Println("\n=== Activating BrowserMode ===")
	m.BaseMode.Activate()
	m.isActive = true

	// Activer l'affichage du mode browser
	m.commManager.SendMessage("mo,4", m.isActive)
	m.commManager.SetActiveMode(m)

	log.Println("=== BrowserMode Activation Complete ===")
}

// Deactivate désactive le mode browser
func (m *LiveBrowserMode) Deactivate() {
	log.Println("\n=== Deactivating BrowserMode ===")
	m.BaseMode.Deactivate()
	m.isActive = false

	// Désactiver l'affichage du mode browser
	//m.commManager.SendMessage("mo,5", false)

	log.Println("=== BrowserMode Deactivation Complete ===")
}

// CleanupForExit nettoie toutes les ressources avant la sortie de l'application
func (m *LiveBrowserMode) CleanupForExit() {
	log.Println("\n=== BrowserMode Complete Cleanup Start ===")

	// Désactiver le mode s'il est actif
	if m.isActive {
		m.Deactivate()
	}

	// Nettoyer les ressources de base
	m.BaseMode.CleanupForExit()

	// Réinitialiser l'état
	m.goHome()
	m.state = &BrowserState{
		Path:             make([]interface{}, 0),
		Items:            make([]BrowserItem, 0),
		IsPreviewEnabled: false,
		IsHotswapEnabled: false,
		CurrentPage:      1,
		TotalPages:       1,
		TotalItems:       0,
		DisplayPath:      make([]string, 0),
	}
	m.lastPreviewed = &LastPreviewedItem{
		Index: -1,
		Page:  -1,
	}

	log.Println("=== BrowserMode Complete Cleanup Complete ===")
}

// HandleHardwareEvent gère les événements matériels
func (m *LiveBrowserMode) HandleHardwareEvent(event communication.HardwareEvent) {
	if !m.IsActive() {
		return
	}

	log.Printf("Mode navigateur: Événement matériel reçu: Type=%s", event.Type)

	switch event.Type {
	case "touch":
		m.handleTouchEvent(event.TouchEvent)
	case "button":
		m.handleButtonEvent(event.ButtonEvent)
	case "encoder":
		m.handleEncoderEvent(event.EncoderEvent)
	}
}

// handleGetItems traite les messages OSC de type get/items
func (m *LiveBrowserMode) handleGetItems(args []interface{}) {
	log.Printf("handleGetItems appelé avec %d arguments: %v", len(args), args)

	if len(args) < 9 {
		log.Printf("Erreur: Nombre d'arguments insuffisant pour handleGetItems: %v", args)
		return
	}

	// Mise à jour de l'état
	m.state.CurrentPage = int(args[0].(int32))
	m.state.TotalPages = int(args[1].(int32))

	log.Printf("Page courante: %d, Total pages: %d", m.state.CurrentPage, m.state.TotalPages)

	// Extraction des items
	indices := args[2].([]interface{})
	names := args[3].([]interface{})
	types := args[4].([]interface{})
	loadable := args[5].([]interface{})
	isFolder := args[6].([]interface{})

	// Mise à jour des items
	m.state.Items = make([]BrowserItem, len(indices))
	for i := range indices {
		m.state.Items[i] = BrowserItem{
			Index:    int(indices[i].(int32)),
			Name:     names[i].(string),
			Type:     types[i].(string),
			Loadable: loadable[i].(int32) == 1,
			IsFolder: isFolder[i].(int32) == 1,
		}
	}

	m.state.TotalItems = int(args[7].(int32))
	displayPath := args[8].([]interface{})
	m.state.DisplayPath = make([]string, len(displayPath))
	for i, p := range displayPath {
		m.state.DisplayPath[i] = p.(string)
	}

	log.Printf("Construction des messages série pour %d items", len(m.state.Items))

	// Envoi des messages à l'interface
	pathStr := strings.Join(m.state.DisplayPath, " > ")
	pathMsg := fmt.Sprintf("bp,%s,%d/%d,%d items",
		pathStr, m.state.CurrentPage, m.state.TotalPages, m.state.TotalItems)

	log.Printf("Envoi du message chemin: %s", pathMsg)
	if err := m.commManager.SendMessage(pathMsg, m.IsActive()); err != nil {
		log.Printf("Erreur lors de l'envoi du message chemin: %v", err)
	}

	// Diviser les items en deux messages
	firstBatch := m.state.Items[:min(8, len(m.state.Items))]

	// Construire et envoyer les messages batch
	firstMsg := "b1"
	for _, item := range firstBatch {
		firstMsg += fmt.Sprintf(",%s,%d", item.Name, boolToInt(item.IsFolder))
	}
	log.Printf("Envoi du premier batch: %s", firstMsg)
	if err := m.commManager.SendMessage(firstMsg, m.IsActive()); err != nil {
		log.Printf("Erreur lors de l'envoi du premier batch: %v", err)
	}

	// N'envoyer le second batch que s'il y a plus de 8 items
	if len(m.state.Items) > 8 {
		secondBatch := m.state.Items[8:min(16, len(m.state.Items))]
		secondMsg := "b2"
		for _, item := range secondBatch {
			secondMsg += fmt.Sprintf(",%s,%d", item.Name, boolToInt(item.IsFolder))
		}
		log.Printf("Envoi du second batch: %s", secondMsg)
		if err := m.commManager.SendMessage(secondMsg, m.IsActive()); err != nil {
			log.Printf("Erreur lors de l'envoi du second batch: %v", err)
		}
	} else {
		// Envoyer un message b2 vide pour effacer les anciens items
		secondMsg := "b2"
		log.Printf("Envoi d'un second batch vide (moins de 8 items)")
		if err := m.commManager.SendMessage(secondMsg, m.IsActive()); err != nil {
			log.Printf("Erreur lors de l'envoi du second batch vide: %v", err)
		}
	}
}

func (m *LiveBrowserMode) handleHotswapEnable(args []interface{}) {
	m.state.IsHotswapEnabled = true

	// Vérifier que le chemin a une longueur impaire comme attendu par le code Python
	if len(args) > 0 {
		log.Printf("Hotswap enabled avec chemin: %v (longueur: %d)", args, len(args))

		// Vérifier si le chemin a une longueur impaire
		if len(args)%2 == 0 {
			log.Printf("Attention: Le chemin pour le hotswap a une longueur paire (%d), ce qui peut causer des problèmes", len(args))
		}

		// Transmettre les arguments tels quels au Python
		m.Send("/live/browser/hotswap/enable", args...)
	} else {
		log.Println("Hotswap enabled sans chemin spécifié")
	}
}

func (m *LiveBrowserMode) handleHotswapDisable(args []interface{}) {
	m.state.IsHotswapEnabled = false
	log.Println("Hotswap disabled")
}

func (m *LiveBrowserMode) handleLoad(args []interface{}) {
	log.Printf("Load message received: %v", args)

	// Si le hotswap est activé, on doit le désactiver
	if m.state.IsHotswapEnabled {
		log.Println("Hotswap était activé, désactivation")
		m.Send(browserHotswapDisableAddress)
		m.state.IsHotswapEnabled = false
	}

	// Dans tous les cas, on revient au mode device après le chargement
	// pour être cohérent avec le comportement JavaScript
	log.Println("Retour au mode device après chargement")
	m.BaseMode.Emit("modeChange", "device")
}

func (m *LiveBrowserMode) handlePreview(args []interface{}) {
	log.Printf("Preview message received: %v", args)
}

func (m *LiveBrowserMode) handleTouchEvent(event *communication.TouchEvent) {
	if event == nil {
		return
	}

	switch event.TouchType {
	case "bb":
		m.goback()
	case "bs":
		if event.Index >= 0 && event.Index < len(m.state.Items) {
			item := m.state.Items[event.Index]
			if item.Loadable {
				if m.state.IsPreviewEnabled {
					if event.Index == m.lastPreviewed.Index && m.state.CurrentPage == m.lastPreviewed.Page {
						m.load(event.Index)
						m.lastPreviewed.Index = -1
						m.lastPreviewed.Page = -1
					} else {
						m.preview(event.Index)
						m.lastPreviewed.Index = event.Index
						m.lastPreviewed.Page = m.state.CurrentPage
					}
				} else {
					m.load(event.Index)
				}
			} else if item.IsFolder {
				m.sendGetItemsMessage(1, append(m.state.Path, event.Index)...)
			}
		}
	case "bl":
		if event.Index >= 0 && event.Index < len(m.state.Items) {
			item := m.state.Items[event.Index]
			if item.IsFolder {
				m.sendGetItemsMessage(1, append(m.state.Path, event.Index)...)
			}
		}
	case "sm":
		switch event.Index {
		case 0:
			m.Send(browserHotswapDisableAddress)
			// Changer vers le mode track
			m.BaseMode.Emit("modeChange", "track")
			log.Printf("Switching to mode: track")
		case 1:
			m.Send(browserHotswapDisableAddress)
			// Changer vers le mode volume
			m.BaseMode.Emit("modeChange", "volume")
			log.Printf("Switching to mode: volume")
		case 2:
			m.Send(browserHotswapDisableAddress)
			// Changer vers le mode device
			m.BaseMode.Emit("modeChange", "device")
			log.Printf("Switching to mode: device")
		case 3:
			m.Send(browserHotswapDisableAddress)
			// Changer vers le mode learn
			m.BaseMode.Emit("modeChange", "learn")
			log.Printf("Switching to mode: learn")
		}
	case "bp":
		m.state.IsPreviewEnabled = event.Index == 1
		m.lastPreviewed.Index = -1
		m.lastPreviewed.Page = -1
		log.Printf("isPreviewEnabled set to %v", m.state.IsPreviewEnabled)
	}
}

func (m *LiveBrowserMode) handleButtonEvent(event *communication.ButtonEvent) {
	if event == nil {
		return
	}

	log.Printf("Mode navigateur: Bouton %d état %d", event.Index, event.State)

	// Ne traiter que les boutons appuyés (state = 1), ignorer les relâchements (state = 0)
	if event.State != 1 {
		log.Printf("Mode navigateur: Bouton %d relâché, ignoré", event.Index)
		return
	}

	log.Printf("Mode navigateur: Bouton %d appuyé, traitement", event.Index)

	switch event.Index {
	case 0: // B00 - Switch to Learn Mode
		m.Send(browserHotswapDisableAddress)
		m.BaseMode.Emit("modeChange", "learn")
		log.Printf("Mode Browser: B00 - Switch to Learn Mode")
	case 1: // B01 - Switch to Track Mode (quickview off)
		m.Send(browserHotswapDisableAddress)
		m.BaseMode.Emit("modeChange", "track")
		log.Printf("Mode Browser: B01 - Switch to Track Mode (quickview off)")
	case 2: // B02 - Switch to Volume Mode
		m.Send(browserHotswapDisableAddress)
		m.BaseMode.Emit("modeChange", "volume")
		log.Printf("Mode Browser: B02 - Switch to Volume Mode")
	case 3: // B03 - Switch to Device Mode
		m.Send(browserHotswapDisableAddress)
		m.BaseMode.Emit("modeChange", "device")
		log.Printf("Mode Browser: B03 - Switch to Device Mode")
	case 4: // B04 - Return to home
		m.goHome()
		log.Printf("Mode Browser: B04 - Return to home")
	// B05 est maintenant géré comme bouton transversal dans HardwareManager
	case 6: // B06 - Back
		m.goback()
		log.Printf("Mode Browser: B06 - Back")
	case 7: // B07 - (non défini)
		log.Printf("Mode Browser: B07 - Non défini")
	case 8: // B08 - Page down
		m.pageDown()
		log.Printf("Mode Browser: B08 - Page down")
	case 9: // B09 - Page up
		m.pageUp()
		log.Printf("Mode Browser: B09 - Page up")
	case 13: // B13 - Toggle lock (TODO: implémenter le verrouillage pour le browser)
		// TODO: Implémenter le système de verrouillage pour le mode browser
		log.Printf("Mode Browser: B13 - Toggle lock (TODO: implémenter)")
	default:
		log.Printf("Mode Browser: Bouton non géré: %d", event.Index)
	}
}

func (m *LiveBrowserMode) handleEncoderEvent(event *communication.EncoderEvent) {
	if event == nil {
		return
	}
	// Implémenter si nécessaire
}

func (m *LiveBrowserMode) pageUp() {
	if m.state.CurrentPage < m.state.TotalPages {
		nextPage := m.state.CurrentPage + 1
		m.sendGetItemsMessage(nextPage, m.state.Path...)
	}
}

func (m *LiveBrowserMode) pageDown() {
	if m.state.CurrentPage > 1 {
		prevPage := m.state.CurrentPage - 1
		m.sendGetItemsMessage(prevPage, m.state.Path...)
	}
}

func (m *LiveBrowserMode) goHome() {
	defaultFolders := []string{
		"instruments",
		"audio_effects",
		"plugins",
		"midi_effects",
		"sounds",
		"samples",
		"drums",
		"clips",
		"user_library",
	}

	// Simuler un message OSC pour l'écran d'accueil
	indices := make([]interface{}, len(defaultFolders))
	names := make([]interface{}, len(defaultFolders))
	types := make([]interface{}, len(defaultFolders))
	loadable := make([]interface{}, len(defaultFolders))
	isFolder := make([]interface{}, len(defaultFolders))

	for i := range defaultFolders {
		indices[i] = int32(i)
		names[i] = defaultFolders[i]
		types[i] = "Folder"
		loadable[i] = int32(0)
		isFolder[i] = int32(1)
	}

	m.handleGetItems([]interface{}{
		int32(1),                   // currentPage
		int32(1),                   // totalPages
		indices,                    // indices
		names,                      // noms
		types,                      // types
		loadable,                   // loadable
		isFolder,                   // isFolder
		int32(len(defaultFolders)), // totalItems
		[]interface{}{"home"},      // displayPath
	})

	m.state.Path = make([]interface{}, 0)
}

func (m *LiveBrowserMode) goback() {
	if len(m.state.Path) == 0 {
		return
	} else if len(m.state.Path) == 1 {
		m.goHome()
	} else {
		m.sendGetItemsMessage(1, m.state.Path[:len(m.state.Path)-1]...)
	}
}

func (m *LiveBrowserMode) sendGetItemsMessage(page int, path ...interface{}) {
	args := make([]interface{}, 0, len(path)+1)
	args = append(args, int32(page))
	args = append(args, path...)

	m.state.Path = path
	m.Send(browserGetItemsAddress, args...)
}

func (m *LiveBrowserMode) load(index int) {
	if index >= 0 && index < len(m.state.Items) && m.state.Items[index].Loadable {
		args := make([]interface{}, 0, len(m.state.Path)+2)
		args = append(args, int32(m.state.CurrentPage))
		args = append(args, m.state.Path...)
		args = append(args, int32(index))
		m.Send(browserLoadAddress, args...)
	}
}

func (m *LiveBrowserMode) preview(index int) {
	if index >= 0 && index < len(m.state.Items) && m.state.Items[index].Loadable {
		args := make([]interface{}, 0, len(m.state.Path)+2)
		args = append(args, int32(m.state.CurrentPage))
		args = append(args, m.state.Path...)
		args = append(args, int32(index))
		m.Send(browserPreviewAddress, args...)
	}
}

// Fonctions utilitaires
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func boolToInt(b bool) int {
	if b {
		return 1
	}
	return 0
}

// IsActive retourne l'état d'activation du mode
func (m *LiveBrowserMode) IsActive() bool {
	return m.isActive
}
