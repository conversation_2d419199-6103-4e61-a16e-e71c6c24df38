package livelearnmode

import (
	"encoding/json"
	"fmt"
	"log"
	"math"
	live "oscbridge/Live"
	"time"
)

// ParseOscFloat convertit une valeur OSC en float64
func ParseOscFloat(val interface{}) (float64, bool) {
	// OSC peut envoyer des float32, float64 ou même des entiers qu'il faut convertir
	switch v := val.(type) {
	case float32:
		return float64(v), true
	case float64:
		return v, true
	case int:
		return float64(v), true
	case int32:
		return float64(v), true
	case int64:
		return float64(v), true
	default:
		log.Printf("Erreur de parsing OSC: type inattendu pour float: %T", val)
		return 0.0, false
	}
}

// --- Fonctions utilitaires pour réduire le code dupliqué ---

// getActiveSlotIndex récupère l'index du slot actif
func (m *LiveLearnMode) getActiveSlotIndex() int {
	m.state.Mutex.RLock()
	defer m.state.Mutex.RUnlock()

	slotIndex := 0
	if m.state.ActiveSlot != nil {
		slotIndex = *m.state.ActiveSlot
	}
	return slotIndex
}

// disableLearningMode désactive le mode apprentissage et met à jour l'affichage
func (m *LiveLearnMode) disableLearningMode() {
	m.state.Mutex.Lock()
	m.state.IsLearning = false
	m.state.Mutex.Unlock()

	// Mettre à jour l'affichage du statut d'apprentissage
	m.displayManager.UpdateLearningStatus(false, 0)

	// Vider les données d'apprentissage
	if service := m.BaseMode.GetService(); service != nil {
		if mm, ok := service.(*live.LiveModeManager); ok {
			mm.SetLearnData(nil)
		}
	}
}

// getDisplaySlotInfo calcule les informations d'affichage pour un slot
func (m *LiveLearnMode) getDisplaySlotInfo(slotIndex int) (displaySlot int, isOnCurrentPage bool) {
	slotPage := (slotIndex / SlotsPerPage) + 1
	displaySlot = slotIndex % SlotsPerPage
	isOnCurrentPage = slotPage == m.state.CurrentPage
	return displaySlot, isOnCurrentPage
}

// convertChainPath convertit un chemin de chaîne OSC en []int
func convertChainPath(chainPath []interface{}) ([]int, bool) {
	chainPathInt := make([]int, len(chainPath))
	for i, v := range chainPath {
		if intVal, ok := v.(int); ok {
			chainPathInt[i] = intVal
		} else if int32Val, ok := v.(int32); ok {
			chainPathInt[i] = int(int32Val)
		} else if floatVal, ok := v.(float64); ok {
			chainPathInt[i] = int(floatVal)
		} else if float32Val, ok := v.(float32); ok {
			chainPathInt[i] = int(float32Val)
		} else {
			log.Printf("convertChainPath: élément de chainPath n'est pas un entier: %v (type: %T)", v, v)
			return nil, false
		}
	}
	return chainPathInt, true
}

// chainPathToJSON convertit un chemin de chaîne en JSON
func chainPathToJSON(chainPath []interface{}) (string, error) {
	jsonChainPath, err := json.Marshal(chainPath)
	if err != nil {
		log.Printf("Erreur lors de la conversion du chemin en JSON: %v", err)
		return "", err
	}
	return string(jsonChainPath), nil
}

// getRealTrackIndex calcule l'index réel de la piste à partir de l'argument OSC
func getRealTrackIndex(trackArg int) int {
	realTrackIndex := int(math.Abs(float64(trackArg))) - 1
	log.Printf("Track arg: %d, realTrackIndex: %d", trackArg, realTrackIndex)
	return realTrackIndex
}

// --- Handlers pour l'apprentissage des paramètres ---

func (m *LiveLearnMode) handleTrackLearningVolume(args []interface{}) {
	if len(args) < 2 {
		log.Println("handleTrackLearningVolume: arguments insuffisants")
		return
	}

	// Extraire les arguments
	value, ok := ParseOscFloat(args[1])
	if !ok {
		log.Println("handleTrackLearningVolume: impossible de convertir value en float64")
		return
	}

	// Récupérer l'index de la piste réelle à partir de l'argument OSC
	trackArg, ok := ParseOscInt(args[0])
	if !ok {
		log.Println("handleTrackLearningVolume: impossible de convertir trackArg en entier")
		return
	}
	realTrackIndex := getRealTrackIndex(trackArg)

	// Récupérer les informations de la piste (simplifiées pour cet exemple)
	trackName := fmt.Sprintf("Track %d", realTrackIndex)
	trackColor := "0"

	// Récupérer l'index du slot actif
	slotIndex := m.getActiveSlotIndex()

	// Créer un type pour le paramètre
	paramType := ParamTypeVolume

	// Mettre à jour les informations du slot (état interne uniquement)
	slot := m.UpdateSlotInfoOnly(slotIndex, map[string]interface{}{
		"type":          paramType,
		"trackName":     trackName,
		"trackColor":    trackColor,
		"value":         value,
		"min":           float64(0), // Plage de volume Live
		"max":           float64(1),
		"parameterName": "volume",
	})

	// Envoyer un message OSC de confirmation à Live
	m.BaseMode.Send(OscAddressLearnSlot, []interface{}{slotIndex, int(paramType), realTrackIndex})

	// Calculer la page et l'index relatif pour l'affichage
	displaySlot, isOnCurrentPage := m.getDisplaySlotInfo(slotIndex)

	// Mettre à jour l'affichage si le slot est sur la page courante
	if isOnCurrentPage && slot != nil {
		// Envoyer le message ls
		volumePercent := int(math.Round(value * 100))
		volumeDb := m.volumeConverter.ToDb(value)
		log.Printf("[DEBUG] handleTrackLearningVolume - Envoi du message ls pour le slot %d: ls,%d,volume,%d,%s dB",
			slotIndex, displaySlot, volumePercent, volumeDb)
		m.commManager.SendMessage(fmt.Sprintf("ls,%d,volume,%d,%s dB", displaySlot, volumePercent, volumeDb), m.isActive)

		// Envoyer le message lo
		deviceName := "" // Pas de device pour le volume de piste
		log.Printf("[DEBUG] handleTrackLearningVolume - Envoi du message lo pour le slot %d: lo,%d,%s,%s,%s",
			slotIndex, displaySlot, deviceName, trackName, trackColor)
		m.commManager.SendMessage(fmt.Sprintf("lo,%d,%s,%s,%s", displaySlot, deviceName, trackName, trackColor), m.isActive)
	}

	// Désactiver le mode apprentissage
	m.disableLearningMode()
}

func (m *LiveLearnMode) handleTrackLearningPanning(args []interface{}) {
	if len(args) < 2 {
		log.Println("handleTrackLearningPanning: arguments insuffisants")
		return
	}

	// Extraire les arguments
	value, ok := ParseOscFloat(args[1])
	if !ok {
		log.Println("handleTrackLearningPanning: impossible de convertir value en float64")
		return
	}

	// Récupérer l'index de la piste réelle à partir de l'argument OSC
	trackArg, ok := ParseOscInt(args[0])
	if !ok {
		log.Println("handleTrackLearningPanning: impossible de convertir trackArg en entier")
		return
	}
	realTrackIndex := getRealTrackIndex(trackArg)

	// Récupérer les informations de la piste (simplifiées pour cet exemple)
	trackName := fmt.Sprintf("Track %d", realTrackIndex)
	trackColor := "0"

	// Récupérer l'index du slot actif
	slotIndex := m.getActiveSlotIndex()

	// Créer un type pour le paramètre
	paramType := ParamTypePan

	// Mettre à jour les informations du slot (état interne uniquement)
	slot := m.UpdateSlotInfoOnly(slotIndex, map[string]interface{}{
		"type":          paramType,
		"trackName":     trackName,
		"trackColor":    trackColor,
		"value":         value,
		"min":           float64(-1), // Plage de panoramique Live
		"max":           float64(1),
		"parameterName": "pan",
	})

	// Envoyer un message OSC de confirmation à Live
	m.BaseMode.Send(OscAddressLearnSlot, []interface{}{slotIndex, int(paramType), realTrackIndex})

	// Calculer la page et l'index relatif pour l'affichage
	displaySlot, isOnCurrentPage := m.getDisplaySlotInfo(slotIndex)

	// Mettre à jour l'affichage si le slot est sur la page courante
	if isOnCurrentPage && slot != nil {
		// Envoyer le message ls
		panPercent := int(math.Round((value + 1) * 50))
		// Formater l'affichage du panoramique
		panDisplay := "C"
		if value < -0.01 {
			panDisplay = fmt.Sprintf("%.0f%%L", math.Abs(value)*100)
		} else if value > 0.01 {
			panDisplay = fmt.Sprintf("%.0f%%R", value*100)
		}
		m.commManager.SendMessage(fmt.Sprintf("ls,%d,pan,%d,%s", displaySlot, panPercent, panDisplay), m.isActive)

		// Envoyer le message lo
		deviceName := "" // Pas de device pour le panoramique de piste
		m.commManager.SendMessage(fmt.Sprintf("lo,%d,%s,%s,%s", displaySlot, deviceName, trackName, trackColor), m.isActive)
	}

	// Désactiver le mode apprentissage
	m.disableLearningMode()
}

func (m *LiveLearnMode) handleTrackLearningSends(args []interface{}) {
	if len(args) < 3 {
		log.Println("handleTrackLearningSends: arguments insuffisants")
		return
	}

	// Extraire les arguments
	sendIndex, ok := ParseOscInt(args[1])
	if !ok {
		log.Println("handleTrackLearningSends: impossible de convertir sendIndex en entier")
		return
	}

	value, ok := ParseOscFloat(args[2])
	if !ok {
		log.Println("handleTrackLearningSends: impossible de convertir value en float64")
		return
	}

	// Récupérer l'index de la piste réelle à partir de l'argument OSC
	trackArg, ok := ParseOscInt(args[0])
	if !ok {
		log.Println("handleTrackLearningSends: impossible de convertir trackArg en entier")
		return
	}
	realTrackIndex := getRealTrackIndex(trackArg)

	// Récupérer les informations de la piste (simplifiées pour cet exemple)
	trackName := fmt.Sprintf("Track %d", realTrackIndex)
	trackColor := "0"

	// Récupérer le nom de la piste de retour depuis le trackManager
	var returnTrackName string
	if m.trackManager != nil {
		returnNames := m.trackManager.GetReturnTracksName()
		if len(returnNames) > sendIndex {
			returnTrackName = returnNames[sendIndex]
		} else {
			// Fallback si le nom n'est pas disponible
			returnTrackName = fmt.Sprintf("%c", 'A'+sendIndex) // A, B, C...
		}
	} else {
		// Fallback si trackManager est nil
		returnTrackName = fmt.Sprintf("%c", 'A'+sendIndex) // A, B, C...
	}

	// Récupérer l'index du slot actif
	slotIndex := m.getActiveSlotIndex()

	// Créer un type pour le paramètre
	paramType := ParamTypeSend

	// Mettre à jour les informations du slot
	m.UpdateSlotInfo(slotIndex, map[string]interface{}{
		"type":          paramType,
		"trackName":     trackName,
		"trackColor":    trackColor,
		"sendIndex":     &sendIndex,
		"value":         value,
		"min":           float64(0), // Plage de send Live
		"max":           float64(1),
		"parameterName": fmt.Sprintf("send %s", returnTrackName),
	})

	// Envoyer un message OSC de confirmation à Live
	m.BaseMode.Send(OscAddressLearnSlot, []interface{}{slotIndex, int(paramType), realTrackIndex, sendIndex})

	// Calculer la page et l'index relatif pour l'affichage
	displaySlot, isOnCurrentPage := m.getDisplaySlotInfo(slotIndex)

	// Mettre à jour l'affichage si le slot est sur la page courante
	if isOnCurrentPage {
		sendPercent := int(math.Round(value * 100))
		sendDb := m.volumeSendConverter.ToDb(value)
		// Formater la valeur avec padStart(3, '0') comme dans le JS
		formattedValue := fmt.Sprintf("%03d", sendPercent)
		m.commManager.SendMessage(fmt.Sprintf("ls,%d,send %s,%s,%s dB", displaySlot, returnTrackName, formattedValue, sendDb), m.isActive)
		// Envoyer le message de mise à jour des propriétés de l'objet
		deviceName := "" // Pas de device pour les sends de piste
		m.commManager.SendMessage(fmt.Sprintf("lo,%d,%s,%s,%s", displaySlot, deviceName, trackName, trackColor), m.isActive)
	}

	// Désactiver le mode apprentissage
	m.disableLearningMode()
}

func (m *LiveLearnMode) handleTrackLearningMute(args []interface{}) {
	if len(args) < 2 {
		log.Println("handleTrackLearningMute: arguments insuffisants")
		return
	}

	// Extraire les arguments
	valueInt, ok := ParseOscInt(args[1])
	if !ok {
		log.Println("handleTrackLearningMute: impossible de convertir value en entier")
		return
	}
	value := valueInt == 1 // Convertir en booléen

	// Récupérer l'index de la piste réelle à partir de l'argument OSC
	trackArg, ok := ParseOscInt(args[0])
	if !ok {
		log.Println("handleTrackLearningMute: impossible de convertir trackArg en entier")
		return
	}
	realTrackIndex := getRealTrackIndex(trackArg)

	// Récupérer les informations de la piste (simplifiées pour cet exemple)
	trackName := fmt.Sprintf("Track %d", realTrackIndex)
	trackColor := "0"

	// Récupérer l'index du slot actif
	slotIndex := m.getActiveSlotIndex()

	// Créer un type pour le paramètre
	paramType := ParamTypeMute
	isQuantized := true
	valueFloat := float64(0)
	if value {
		valueFloat = 1.0
	}

	// Mettre à jour les informations du slot
	m.UpdateSlotInfo(slotIndex, map[string]interface{}{
		"type":          paramType,
		"trackName":     trackName,
		"trackColor":    trackColor,
		"value":         valueFloat,
		"min":           float64(0),
		"max":           float64(1),
		"isQuantized":   &isQuantized,
		"parameterName": "Mute",
	})

	// Envoyer un message OSC de confirmation à Live
	m.BaseMode.Send(OscAddressLearnSlot, []interface{}{slotIndex, int(paramType), realTrackIndex})

	// Nous n'envoyons pas directement les messages ls et lo ici
	// car UpdateSlotInfo va déclencher l'envoi des messages appropriés
	// via le displayManager

	// Désactiver le mode apprentissage
	m.disableLearningMode()
}

func (m *LiveLearnMode) handleTrackLearningSolo(args []interface{}) {
	if len(args) < 2 {
		log.Println("handleTrackLearningSolo: arguments insuffisants")
		return
	}

	// Extraire les arguments
	valueInt, ok := ParseOscInt(args[1])
	if !ok {
		log.Println("handleTrackLearningSolo: impossible de convertir value en entier")
		return
	}
	value := valueInt == 1 // Convertir en booléen

	// Récupérer l'index de la piste réelle à partir de l'argument OSC
	trackArg, ok := ParseOscInt(args[0])
	if !ok {
		log.Println("handleTrackLearningSolo: impossible de convertir trackArg en entier")
		return
	}
	realTrackIndex := getRealTrackIndex(trackArg)

	// Récupérer les informations de la piste (simplifiées pour cet exemple)
	trackName := fmt.Sprintf("Track %d", realTrackIndex)
	trackColor := "0"

	// Récupérer l'index du slot actif
	slotIndex := m.getActiveSlotIndex()

	// Créer un type pour le paramètre
	paramType := ParamTypeSolo
	isQuantized := true
	valueFloat := float64(0)
	if value {
		valueFloat = 1.0
	}

	// Mettre à jour les informations du slot
	m.UpdateSlotInfo(slotIndex, map[string]interface{}{
		"type":          paramType,
		"trackName":     trackName,
		"trackColor":    trackColor,
		"value":         valueFloat,
		"min":           float64(0),
		"max":           float64(1),
		"isQuantized":   &isQuantized,
		"parameterName": "Solo",
	})

	// Envoyer un message OSC de confirmation à Live
	m.BaseMode.Send(OscAddressLearnSlot, []interface{}{slotIndex, int(paramType), realTrackIndex})

	// Nous n'envoyons pas directement les messages ls et lo ici
	// car UpdateSlotInfo va déclencher l'envoi des messages appropriés
	// via le displayManager

	// Désactiver le mode apprentissage
	m.disableLearningMode()
}

func (m *LiveLearnMode) handleDeviceLearningParamValue(args []interface{}) {
	if len(args) < 5 {
		log.Println("handleDeviceLearningParamValue: arguments insuffisants")
		return
	}

	// Extraire les arguments de base
	deviceIndex, ok := ParseOscInt(args[1])
	if !ok {
		log.Println("handleDeviceLearningParamValue: impossible de convertir deviceIndex en entier")
		return
	}

	paramIndex, ok := ParseOscInt(args[2])
	if !ok {
		log.Println("handleDeviceLearningParamValue: impossible de convertir paramIndex en entier")
		return
	}

	value, ok := ParseOscFloat(args[3])
	if !ok {
		log.Println("handleDeviceLearningParamValue: impossible de convertir value en float64")
		return
	}

	valueString, ok := args[4].(string)
	if !ok {
		log.Println("handleDeviceLearningParamValue: valueString n'est pas une chaîne")
		valueString = fmt.Sprintf("%.2f", value) // Fallback
	}

	// Récupérer l'index du slot actif
	slotIndex := m.getActiveSlotIndex()

	// Activer temporairement le mode apprentissage si nécessaire
	m.state.Mutex.RLock()
	isLearning := m.state.IsLearning
	m.state.Mutex.RUnlock()

	if !isLearning {
		// Activer temporairement le mode apprentissage pour traiter ce message
		m.state.Mutex.Lock()
		m.state.IsLearning = true
		m.state.Mutex.Unlock()
		m.displayManager.UpdateLearningStatus(true, slotIndex)
	}

	// Calculer si le slot est sur la page courante
	_, isOnCurrentPage := m.getDisplaySlotInfo(slotIndex)

	// Valeurs par défaut pour la piste
	trackName := "Current Track"
	trackColor := "0"

	// Traiter le paramètre du device (verrouillé ou normal)
	m.handleDeviceParameter(deviceIndex, paramIndex, value, valueString, slotIndex, isOnCurrentPage, trackName, trackColor)

	// Désactiver le mode apprentissage
	m.disableLearningMode()
}

// handleDeviceParameter gère l'apprentissage d'un paramètre de device (verrouillé ou normal)
func (m *LiveLearnMode) handleDeviceParameter(deviceIndex, paramIndex int, value float64, valueString string,
	slotIndex int, isOnCurrentPage bool, trackName, trackColor string) {

	// Préparer les mises à jour de base communes à tous les types de devices
	updates := map[string]interface{}{
		"type":           ParamTypeDevice,
		"value":          value,
		"valueString":    valueString,
		"parameterIndex": paramIndex,
		"deviceIndex":    deviceIndex,
		"trackName":      trackName,
		"trackColor":     trackColor,
	}

	// Récupérer les propriétés existantes du slot pour ne pas les écraser
	m.state.Mutex.RLock()
	existingSlot, exists := m.state.Slots[slotIndex]
	m.state.Mutex.RUnlock()

	// Déterminer la source des informations du paramètre
	var deviceParams *DeviceParameters
	var deviceExists bool

	if deviceIndex == LockedDeviceIndex {
		// Pour les devices verrouillés, chercher dans l'index 0
		deviceParams, deviceExists = m.deviceParameters[0]
	} else {
		// Pour les devices normaux, chercher à l'index spécifique
		deviceParams, deviceExists = m.deviceParameters[deviceIndex]
	}

	// Ajouter les informations du paramètre aux mises à jour
	if deviceExists && paramIndex >= 0 && paramIndex < len(deviceParams.Names) {
		// Utiliser les informations du device manager
		if deviceParams.Names[paramIndex] != "" {
			updates["parameterName"] = deviceParams.Names[paramIndex]
		}
		if deviceParams.DeviceName != "" {
			updates["deviceName"] = deviceParams.DeviceName
		}
		updates["isQuantized"] = deviceParams.IsQuantized[paramIndex]
		updates["min"] = deviceParams.Min[paramIndex]
		updates["max"] = deviceParams.Max[paramIndex]
	} else if exists {
		// Préserver les propriétés existantes si le slot existe déjà
		if existingSlot.ParameterName != "" {
			// Ne pas inclure parameterName dans updates pour préserver la valeur existante
		} else {
			updates["parameterName"] = fmt.Sprintf("Param %d", paramIndex)
		}

		if existingSlot.DeviceName != "" {
			// Ne pas inclure deviceName dans updates pour préserver la valeur existante
		} else {
			if deviceIndex == LockedDeviceIndex {
				updates["deviceName"] = "Locked Device"
			} else {
				updates["deviceName"] = fmt.Sprintf("Device %d", deviceIndex)
			}
		}

		// Préserver les propriétés min, max et isQuantized si elles existent
		if existingSlot.Min != nil {
			// Ne pas inclure min dans updates pour préserver la valeur existante
		} else {
			updates["min"] = float64(0)
		}

		if existingSlot.Max != nil {
			// Ne pas inclure max dans updates pour préserver la valeur existante
		} else {
			updates["max"] = float64(1)
		}

		if existingSlot.IsQuantized != nil {
			updates["isQuantized"] = *existingSlot.IsQuantized
		} else {
			updates["isQuantized"] = false
		}
	} else {
		// Valeurs par défaut si le slot n'existe pas et qu'on n'a pas d'infos du device manager
		updates["parameterName"] = fmt.Sprintf("Param %d", paramIndex)
		if deviceIndex == LockedDeviceIndex {
			updates["deviceName"] = "Locked Device"
		} else {
			updates["deviceName"] = fmt.Sprintf("Device %d", deviceIndex)
		}
		updates["min"] = float64(0)
		updates["max"] = float64(1)
		updates["isQuantized"] = false
	}

	// Mettre à jour le slot
	m.UpdateSlotInfoOnly(slotIndex, updates)

	// Envoyer un message OSC de confirmation à Live
	m.BaseMode.Send(OscAddressLearnSlot, []interface{}{
		slotIndex, int(ParamTypeDevice), ChainTrackIndex, deviceIndex, paramIndex,
	})

	// Mettre à jour l'affichage si le slot est sur la page courante
	if isOnCurrentPage {
		m.state.Mutex.RLock()
		slot, exists := m.state.Slots[slotIndex]
		m.state.Mutex.RUnlock()

		if exists {
			m.displayManager.UpdateSlot(slotIndex, slot)
		}
	}
}

func (m *LiveLearnMode) handleDeviceLearningParamValueStr(args []interface{}) {
	// Cette méthode est similaire à handleDeviceLearningParamValue mais avec une chaîne de valeur
	// Dans la plupart des cas, handleDeviceLearningParamValue est suffisant car il gère déjà valueString
	log.Println("handleDeviceLearningParamValueStr: redirection vers handleDeviceLearningParamValue")
	m.handleDeviceLearningParamValue(args)
}

func (m *LiveLearnMode) handleChainLearningVolume(args []interface{}) {
	if len(args) < 3 {
		log.Println("handleChainLearningVolume: arguments insuffisants")
		return
	}

	// Extraire les arguments
	chainPath, ok := args[1].([]interface{})
	if !ok {
		log.Println("handleChainLearningVolume: chainPath n'est pas un tableau")
		return
	}

	// Convertir chainPath en []int
	chainPathInt, ok := convertChainPath(chainPath)
	if !ok {
		return
	}

	value, ok := ParseOscFloat(args[2])
	if !ok {
		log.Println("handleChainLearningVolume: impossible de convertir value en float64")
		return
	}

	// Récupérer les informations de la piste (simplifiées pour cet exemple)
	trackName := "Current Track"
	trackColor := "0"

	// Récupérer l'index du slot actif
	slotIndex := m.getActiveSlotIndex()

	// Créer un type pour le paramètre
	paramType := ParamTypeChainVolume

	// Mettre à jour les informations du slot
	m.UpdateSlotInfo(slotIndex, map[string]interface{}{
		"type":          paramType,
		"trackName":     trackName,
		"trackColor":    trackColor,
		"chainPath":     chainPathInt,
		"value":         value,
		"min":           float64(0), // Plage de volume Live
		"max":           float64(1),
		"parameterName": "chain vol",
	})

	// Envoyer un message OSC de confirmation à Live
	// Convertir le chemin en JSON pour éviter les problèmes de parsing côté Python
	jsonChainPath, err := chainPathToJSON(chainPath)
	if err != nil {
		return
	}
	m.BaseMode.Send(OscAddressLearnSlot, []interface{}{slotIndex, int(paramType), ChainTrackIndex, jsonChainPath})

	// Nous n'envoyons pas directement les messages ls et lo ici
	// car UpdateSlotInfo va déclencher l'envoi des messages appropriés
	// via le displayManager

	// Désactiver le mode apprentissage
	m.disableLearningMode()
}

func (m *LiveLearnMode) handleChainLearningPanning(args []interface{}) {
	if len(args) < 3 {
		log.Println("handleChainLearningPanning: arguments insuffisants")
		return
	}

	// Extraire les arguments
	chainPath, ok := args[1].([]interface{})
	if !ok {
		log.Println("handleChainLearningPanning: chainPath n'est pas un tableau")
		return
	}

	// Convertir chainPath en []int
	chainPathInt, ok := convertChainPath(chainPath)
	if !ok {
		return
	}

	value, ok := ParseOscFloat(args[2])
	if !ok {
		log.Println("handleChainLearningPanning: impossible de convertir value en float64")
		return
	}

	// Récupérer les informations de la piste (simplifiées pour cet exemple)
	trackName := "Current Track"
	trackColor := "0"

	// Récupérer l'index du slot actif
	slotIndex := m.getActiveSlotIndex()

	// Créer un type pour le paramètre
	paramType := ParamTypeChainPan

	// Mettre à jour les informations du slot
	m.UpdateSlotInfo(slotIndex, map[string]interface{}{
		"type":          paramType,
		"trackName":     trackName,
		"trackColor":    trackColor,
		"chainPath":     chainPathInt,
		"value":         value,
		"min":           float64(-1), // Plage de panoramique Live
		"max":           float64(1),
		"parameterName": "chain pan",
	})

	// Envoyer un message OSC de confirmation à Live
	// Convertir le chemin en JSON pour éviter les problèmes de parsing côté Python
	jsonChainPath, err := chainPathToJSON(chainPath)
	if err != nil {
		return
	}
	m.BaseMode.Send(OscAddressLearnSlot, []interface{}{slotIndex, int(paramType), ChainTrackIndex, jsonChainPath})

	// Nous n'envoyons pas directement les messages ls et lo ici
	// car UpdateSlotInfo va déclencher l'envoi des messages appropriés
	// via le displayManager

	// Désactiver le mode apprentissage
	m.disableLearningMode()
}

func (m *LiveLearnMode) handleChainLearningMute(args []interface{}) {
	if len(args) < 3 {
		log.Println("handleChainLearningMute: arguments insuffisants")
		return
	}

	// Extraire les arguments
	chainPath, ok := args[1].([]interface{})
	if !ok {
		log.Println("handleChainLearningMute: chainPath n'est pas un tableau")
		return
	}

	// Convertir chainPath en []int
	chainPathInt, ok := convertChainPath(chainPath)
	if !ok {
		return
	}

	valueInt, ok := ParseOscInt(args[2])
	if !ok {
		log.Println("handleChainLearningMute: impossible de convertir value en entier")
		return
	}
	value := valueInt == 1 // Convertir en booléen

	// Récupérer les informations de la piste (simplifiées pour cet exemple)
	trackName := "Current Track"
	trackColor := "0"

	// Récupérer l'index du slot actif et stocker l'heure pour le debounce
	slotIndex := m.getActiveSlotIndex()

	// Stocker l'heure actuelle et le slot pour le debounce
	m.state.Mutex.Lock()
	m.lastMuteTime = time.Now().UnixNano() / int64(time.Millisecond)
	m.lastMuteSlot = &slotIndex
	m.state.Mutex.Unlock()

	// Créer un type pour le paramètre
	paramType := ParamTypeChainMute
	isQuantized := true
	valueFloat := float64(0)
	if value {
		valueFloat = 1.0
	}

	// Mettre à jour les informations du slot
	m.UpdateSlotInfo(slotIndex, map[string]interface{}{
		"type":          paramType,
		"trackName":     trackName,
		"trackColor":    trackColor,
		"chainPath":     chainPathInt,
		"value":         valueFloat,
		"min":           float64(0),
		"max":           float64(1),
		"isQuantized":   &isQuantized,
		"parameterName": "chain mute",
	})

	// Envoyer un message OSC de confirmation à Live
	// Convertir le chemin en JSON pour éviter les problèmes de parsing côté Python
	jsonChainPath, err := chainPathToJSON(chainPath)
	if err != nil {
		return
	}
	m.BaseMode.Send(OscAddressLearnSlot, []interface{}{slotIndex, int(paramType), ChainTrackIndex, jsonChainPath})

	// Nous n'envoyons pas directement les messages ls et lo ici
	// car UpdateSlotInfo va déclencher l'envoi des messages appropriés
	// via le displayManager

	// Désactiver le mode apprentissage
	m.disableLearningMode()
}

func (m *LiveLearnMode) handleChainLearningSolo(args []interface{}) {
	if len(args) < 3 {
		log.Println("handleChainLearningSolo: arguments insuffisants")
		return
	}

	// Extraire les arguments
	chainPath, ok := args[1].([]interface{})
	if !ok {
		log.Println("handleChainLearningSolo: chainPath n'est pas un tableau")
		return
	}

	// Convertir chainPath en []int
	chainPathInt, ok := convertChainPath(chainPath)
	if !ok {
		return
	}

	valueInt, ok := ParseOscInt(args[2])
	if !ok {
		log.Println("handleChainLearningSolo: impossible de convertir value en entier")
		return
	}
	value := valueInt == 1 // Convertir en booléen

	// Récupérer les informations de la piste (simplifiées pour cet exemple)
	trackName := "Current Track"
	trackColor := "0"

	// Récupérer l'index du slot actif
	slotIndex := m.getActiveSlotIndex()

	// Vérifier si un message mute est arrivé récemment pour le même slot
	m.state.Mutex.Lock()
	currentTime := time.Now().UnixNano() / int64(time.Millisecond)
	recentMute := (currentTime - m.lastMuteTime) < m.muteDebounceTime && m.lastMuteSlot != nil && *m.lastMuteSlot == slotIndex
	if recentMute {
		// Réinitialiser les variables de mute pour éviter le traitement du mute
		m.lastMuteTime = 0
		m.lastMuteSlot = nil
	}
	m.state.Mutex.Unlock()

	// Créer un type pour le paramètre
	paramType := ParamTypeChainSolo
	isQuantized := true
	valueFloat := float64(0)
	if value {
		valueFloat = 1.0
	}

	// Mettre à jour les informations du slot
	m.UpdateSlotInfo(slotIndex, map[string]interface{}{
		"type":          paramType,
		"trackName":     trackName,
		"trackColor":    trackColor,
		"chainPath":     chainPathInt,
		"value":         valueFloat,
		"min":           float64(0),
		"max":           float64(1),
		"isQuantized":   &isQuantized,
		"parameterName": "chain solo",
	})

	// Envoyer un message OSC de confirmation à Live
	// Convertir le chemin en JSON pour éviter les problèmes de parsing côté Python
	jsonChainPath, err := chainPathToJSON(chainPath)
	if err != nil {
		return
	}
	m.BaseMode.Send(OscAddressLearnSlot, []interface{}{slotIndex, int(paramType), ChainTrackIndex, jsonChainPath})

	// Nous n'envoyons pas directement les messages ls et lo ici
	// car UpdateSlotInfo va déclencher l'envoi des messages appropriés
	// via le displayManager

	// Désactiver le mode apprentissage
	m.disableLearningMode()
}

// --- Handlers pour les propriétés des paramètres de device verrouillé ---

func (m *LiveLearnMode) handleGetLockedDeviceParamProps(args []interface{}) {
	log.Printf("[DEBUG] handleGetLockedDeviceParamProps - Start - args: %v", args)

	if len(args) < 6 {
		log.Println("handleGetLockedDeviceParamProps: arguments insuffisants")
		return
	}

	// Extraire les arguments
	paramIndex, ok := ParseOscInt(args[1])
	if !ok {
		log.Println("handleGetLockedDeviceParamProps: impossible de convertir paramIndex en entier")
		return
	}

	min, ok := ParseOscFloat(args[2])
	if !ok {
		log.Println("handleGetLockedDeviceParamProps: impossible de convertir min en float64")
		return
	}

	max, ok := ParseOscFloat(args[3])
	if !ok {
		log.Println("handleGetLockedDeviceParamProps: impossible de convertir max en float64")
		return
	}

	// Gérer isQuantized qui peut être un booléen ou un entier
	var isQuantized bool
	switch v := args[4].(type) {
	case bool:
		isQuantized = v
		log.Printf("[DEBUG] handleGetLockedDeviceParamProps: isQuantized est un booléen: %v", isQuantized)
	case int:
		isQuantized = v == 1
		log.Printf("[DEBUG] handleGetLockedDeviceParamProps: isQuantized est un entier: %d -> %v", v, isQuantized)
	case int32:
		isQuantized = v == 1
		log.Printf("[DEBUG] handleGetLockedDeviceParamProps: isQuantized est un int32: %d -> %v", v, isQuantized)
	case float32:
		isQuantized = v > 0.5
		log.Printf("[DEBUG] handleGetLockedDeviceParamProps: isQuantized est un float32: %f -> %v", v, isQuantized)
	case float64:
		isQuantized = v > 0.5
		log.Printf("[DEBUG] handleGetLockedDeviceParamProps: isQuantized est un float64: %f -> %v", v, isQuantized)
	default:
		log.Printf("[ERROR] handleGetLockedDeviceParamProps: type inattendu pour isQuantized: %T", args[4])
		return
	}

	paramName, ok := args[5].(string)
	if !ok {
		log.Println("handleGetLockedDeviceParamProps: paramName n'est pas une chaîne")
		return
	}

	// Récupérer l'index du slot actif
	slotIndex := m.getActiveSlotIndex()

	// Créer un type pour le paramètre
	paramType := ParamTypeDevice

	// Mettre à jour les informations du slot
	updates := map[string]interface{}{
		"type":           paramType,
		"parameterName":  paramName,
		"parameterIndex": paramIndex,
		"deviceIndex":    LockedDeviceIndex,
		"min":            min,
		"max":            max,
		"isQuantized":    isQuantized, // Passer directement la valeur booléenne, pas un pointeur
	}

	log.Printf("[DEBUG] handleGetLockedDeviceParamProps - Mise à jour du slot %d avec: %+v", slotIndex, updates)
	m.UpdateSlotInfoOnly(slotIndex, updates)

	log.Printf("[DEBUG] Propriétés du paramètre verrouillé mises à jour: paramIndex=%d, min=%f, max=%f, isQuantized=%t, paramName=%s",
		paramIndex, min, max, isQuantized, paramName)

	// Vérifier l'état du slot après la mise à jour pour le débogage
	m.state.Mutex.RLock()
	slot, exists := m.state.Slots[slotIndex]
	m.state.Mutex.RUnlock()

	if exists && slot.Type != nil {
		isQuantizedValue := false
		if slot.IsQuantized != nil {
			isQuantizedValue = *slot.IsQuantized
		}

		log.Printf("[DEBUG] handleGetLockedDeviceParamProps - End - Slot %d mis à jour avec succès: type=%d, paramName=%s, isQuantized=%v (adresse: %p)",
			slotIndex, *slot.Type, slot.ParameterName, isQuantizedValue, slot.IsQuantized)
	} else {
		log.Printf("[DEBUG] handleGetLockedDeviceParamProps - End - ERREUR: Slot %d non mis à jour correctement: exists=%v, hasType=%v",
			slotIndex, exists, exists && slot.Type != nil)
	}

	// Note: Nous n'envoyons pas de message OSC de confirmation ici
	// car nous attendons encore la valeur du paramètre qui sera traitée par handleDeviceLearningParamValue
}

// Note: La fonction handleReadyToListen a été déplacée dans liveLearn_handlers.go
