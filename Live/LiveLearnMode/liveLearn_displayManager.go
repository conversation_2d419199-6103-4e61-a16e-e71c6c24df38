package livelearnmode

import (
	"fmt"
	"log"
	"math"
	communication "oscbridge/Communication"
	live "oscbridge/Live"
	"oscbridge/Live/utils"
)

// LiveLearnDisplayManager gère l'affichage du mode learn
type LiveLearnDisplayManager struct {
	communicationManager *communication.CommunicationManager
	liveTrackManager     *live.LiveTrackManager
	state                *LiveLearnModeState
	volumeConverter      *utils.VolumeConverter
	volumeSendConverter  *utils.VolumeSendConverter
	parentMode           *LiveLearnMode
}

// NewLiveLearnDisplayManager crée une nouvelle instance de LiveLearnDisplayManager
func NewLiveLearnDisplayManager(commManager *communication.CommunicationManager, trackManager *live.LiveTrackManager, state *LiveLearnModeState) *LiveLearnDisplayManager {
	return &LiveLearnDisplayManager{
		communicationManager: commManager,
		liveTrackManager:     trackManager,
		state:                state,
		volumeConverter:      utils.NewVolumeConverter(),
		volumeSendConverter:  utils.NewVolumeSendConverter(),
	}
}

// SetParentMode définit le mode parent
func (m *LiveLearnDisplayManager) SetParentMode(parent *LiveLearnMode) {
	m.parentMode = parent
}

// UpdatePage met à jour l'affichage de la page courante
func (m *LiveLearnDisplayManager) UpdatePage(page int) {
	if m.communicationManager == nil {
		return
	}

	// Vérifier si parentMode est nil pour éviter une panique
	isActive := true
	if m.parentMode != nil {
		isActive = m.parentMode.IsActive()
	}

	// Note: Dans la version JS, le nombre total de pages n'est pas inclus dans le message

	// Envoyer le message de mise à jour de page
	// Note: Format rigoureusement identique à l'ancien projet JS
	m.communicationManager.SendMessage(fmt.Sprintf("lp,%d", page), isActive)
}

// UpdateSlot met à jour l'affichage d'un slot
func (m *LiveLearnDisplayManager) UpdateSlot(slotIndex int, slotInfo *SlotInfo) {
	if m.communicationManager == nil || slotInfo == nil {
		return
	}

	// Vérifier si parentMode est nil pour éviter une panique
	isActive := true
	if m.parentMode != nil {
		isActive = m.parentMode.IsActive()
	}

	// Calculer l'index relatif du slot sur la page courante
	startSlot := (m.state.CurrentPage - 1) * SlotsPerPage
	relativeIndex := slotIndex - startSlot

	// Vérifier si le slot est visible sur la page courante
	if relativeIndex < 0 || relativeIndex >= SlotsPerPage {
		return
	}

	// Utiliser l'index relatif directement (0-7)
	displaySlot := relativeIndex

	// Si le slot n'a pas de type défini, l'afficher comme vide
	if slotInfo.Type == nil {
		// Format rigoureusement identique à l'ancien projet JS
		m.communicationManager.SendMessage(fmt.Sprintf("ls,%d,null,-,-,-,-,-", displaySlot), isActive)
		return
	}

	// Préparer les valeurs d'affichage
	paramName := slotInfo.ParameterName
	if paramName == "" {
		paramName = "---"
	}

	// Ajouter des logs détaillés pour le débogage de isQuantized
	if slotInfo.Type != nil && *slotInfo.Type == ParamTypeDevice {
		if slotInfo.IsQuantized != nil {
			log.Printf("DEBUG: Vérification isQuantized - slot: %d, type: %d, isQuantized: %v (adresse: %p), paramName: %s",
				slotIndex, *slotInfo.Type, *slotInfo.IsQuantized, slotInfo.IsQuantized, slotInfo.ParameterName)
		} else {
			log.Printf("DEBUG: Vérification isQuantized - slot: %d, type: %d, isQuantized: nil, paramName: %s",
				slotIndex, *slotInfo.Type, slotInfo.ParameterName)
		}
	}

	// Ajouter le préfixe "|| " pour les paramètres de device quantifiés
	if slotInfo.Type != nil && *slotInfo.Type == ParamTypeDevice && slotInfo.IsQuantized != nil && *slotInfo.IsQuantized {
		paramName = "|| " + paramName
		log.Printf("DEBUG: Paramètre quantifié détecté - slot: %d, type: %d, isQuantized: %v, paramName avant: %s, paramName après: %s",
			slotIndex, *slotInfo.Type, *slotInfo.IsQuantized, slotInfo.ParameterName, paramName)
	}

	// Calculer le pourcentage et la valeur d'affichage
	percentValue := 0
	displayValue := "---"

	if slotInfo.Value != nil {
		value := *slotInfo.Value

		// Traitement spécifique selon le type de paramètre
		if slotInfo.Type != nil {
			switch *slotInfo.Type {
			case ParamTypeVolume, ParamTypeChainVolume:
				percentValue = int(math.Round(value * 100))
				displayValue = m.volumeConverter.ToDb(value)
			case ParamTypePan, ParamTypeChainPan:
				percentValue = int(math.Round((value+1)*50))
				displayValue = m.formatPanDisplay(value)
			case ParamTypeSend:
				percentValue = int(math.Round(value * 100))
				displayValue = m.volumeSendConverter.ToDb(value)
			case ParamTypeMute, ParamTypeSolo, ParamTypeChainMute, ParamTypeChainSolo:
				if value > 0.5 {
					percentValue = 100
					displayValue = "On"
				} else {
					percentValue = 0
					displayValue = "Off"
				}
			case ParamTypeDevice:
				// Pour les paramètres de device, toujours privilégier ValueString s'il est disponible
				if slotInfo.ValueString != "" {
					// Utiliser la chaîne de valeur stockée
					displayValue = slotInfo.ValueString
				} else {
					// Fallback sur le formatage de la valeur brute
					displayValue = fmt.Sprintf("%.2f", value)
				}

				// Calculer le pourcentage pour l'affichage de la barre
				if slotInfo.Min != nil && slotInfo.Max != nil {
					min, max := *slotInfo.Min, *slotInfo.Max
					if max > min {
						percentValue = int(math.Round((value-min)/(max-min) * 100))
					} else {
						percentValue = int(math.Round(value * 100))
					}
				} else {
					percentValue = int(math.Round(value * 100))
				}
			}
		}
	}

	// Formater la valeur en pourcentage avec 3 chiffres (comme dans JS)
	formattedValue := fmt.Sprintf("%03d", percentValue)

	// Ajouter des logs détaillés pour le débogage des valeurs de device
	if slotInfo.Type != nil && *slotInfo.Type == ParamTypeDevice {
		log.Printf("DEBUG: UpdateSlot - Device param details - slot: %d, paramName: %s, value: %v, valueString: '%s', isQuantized: %v",
			slotIndex, paramName, slotInfo.Value, slotInfo.ValueString, slotInfo.IsQuantized)
	}

	// Envoyer le message de mise à jour du slot (format identique à JS)
	messageToSend := fmt.Sprintf("ls,%d,%s,%s,%s", relativeIndex, paramName, formattedValue, displayValue)
	log.Printf("TRACE: UpdateSlot - Envoi du message ls pour le slot %d (type: %v): %s",
		slotIndex,
		slotInfo.Type,
		messageToSend)
	m.communicationManager.SendMessage(messageToSend, isActive)

	// Envoyer également le message "lo" pour mettre à jour les propriétés de l'objet
	// Cela est nécessaire car handleSlotProperties n'envoie plus directement ce message
	if slotInfo.TrackName != "" {
		deviceNameToSend := ""
		if slotInfo.DeviceName != "" {
			deviceNameToSend = fmt.Sprintf("• %s", slotInfo.DeviceName)
		}

		log.Printf("TRACE: UpdateSlot - Envoi du message lo pour le slot %d: lo,%d,%s,%s,%s", slotIndex, relativeIndex, deviceNameToSend, slotInfo.TrackName, slotInfo.TrackColor)
		m.communicationManager.SendMessage(
			fmt.Sprintf("lo,%d,%s,%s,%s", relativeIndex, deviceNameToSend, slotInfo.TrackName, slotInfo.TrackColor),
			isActive,
		)
	}
}

// UpdateAllSlots met à jour l'affichage de tous les slots visibles
func (m *LiveLearnDisplayManager) UpdateAllSlots() {
	if m.state == nil {
		return
	}

	log.Printf("DEBUG: UpdateAllSlots - Mise à jour de tous les slots pour la page %d", m.state.CurrentPage)

	startSlot := (m.state.CurrentPage - 1) * SlotsPerPage
	endSlot := startSlot + SlotsPerPage

	for i := startSlot; i < endSlot; i++ {
		if slotInfo, exists := m.state.Slots[i]; exists {
			// Ajouter des logs détaillés pour les slots de type device
			if slotInfo.Type != nil && *slotInfo.Type == ParamTypeDevice {
				log.Printf("DEBUG: UpdateAllSlots - Slot %d (device): paramName: %s, value: %v, valueString: '%s'",
					i, slotInfo.ParameterName, slotInfo.Value, slotInfo.ValueString)
			}
			m.UpdateSlot(i, slotInfo)
		}
	}

	log.Printf("DEBUG: UpdateAllSlots - Fin de la mise à jour pour la page %d", m.state.CurrentPage)
}

// ClearSlot efface l'affichage d'un slot
func (m *LiveLearnDisplayManager) ClearSlot(slotIndex int) {
	if m.communicationManager == nil {
		return
	}

	// Vérifier si parentMode est nil pour éviter une panique
	isActive := true
	if m.parentMode != nil {
		isActive = m.parentMode.IsActive()
	}

	// Calculer l'index relatif du slot sur la page courante
	startSlot := (m.state.CurrentPage - 1) * SlotsPerPage
	relativeIndex := slotIndex - startSlot

	// Vérifier si le slot est visible sur la page courante
	if relativeIndex < 0 || relativeIndex >= SlotsPerPage {
		return
	}

	// Envoyer les messages pour effacer le slot
	// Format rigoureusement identique à l'ancien projet JS
	m.communicationManager.SendMessage(fmt.Sprintf("ls,%d,null,-,-,-,-,-", relativeIndex), isActive)
}

// UpdateLearningStatus met à jour l'affichage du statut d'apprentissage
func (m *LiveLearnDisplayManager) UpdateLearningStatus(isLearning bool, slotIndex int) {
	if m.communicationManager == nil {
		return
	}

	// Vérifier si parentMode est nil pour éviter une panique
	isActive := true
	if m.parentMode != nil {
		isActive = m.parentMode.IsActive()
	}

	if isLearning {
		// Calculer l'index relatif du slot sur la page courante
		startSlot := (m.state.CurrentPage - 1) * SlotsPerPage
		relativeIndex := slotIndex - startSlot

		// Vérifier si le slot est visible sur la page courante
		if relativeIndex >= 0 && relativeIndex < SlotsPerPage {
			// Dans la version JS, le statut d'apprentissage est indiqué par un message ls avec "learn"
			m.communicationManager.SendMessage(fmt.Sprintf("ls,%d,learn,-,-,-,-,-", relativeIndex), isActive)

			// Également envoyer le message ll pour la compatibilité
			m.communicationManager.SendMessage(fmt.Sprintf("ll,%d", relativeIndex), isActive)
		}
	} else {
		// Désactiver le mode apprentissage
		m.communicationManager.SendMessage("ll,0", isActive)
	}
}

// UpdateModeDisplay met à jour l'affichage du mode
func (m *LiveLearnDisplayManager) UpdateModeDisplay() {
	if m.communicationManager == nil {
		return
	}

	// Vérifier si parentMode est nil pour éviter une panique
	isActive := true
	if m.parentMode != nil {
		isActive = m.parentMode.IsActive()
	}

	// Envoyer le message de mode (3 = learn mode)
	m.communicationManager.SendMessage("mo,3", isActive)
}

// formatPanDisplay formate l'affichage du panoramique
func (m *LiveLearnDisplayManager) formatPanDisplay(panValue float64) string {
	// Limiter la valeur entre -1 et 1
	panValue = math.Max(-1, math.Min(1, panValue))

	if panValue == 0 {
		return "C" // Centre
	} else if panValue < 0 {
		// Gauche (L)
		percentage := int(math.Round(math.Abs(panValue) * 50))
		return fmt.Sprintf("%dL", percentage)
	} else {
		// Droite (R)
		percentage := int(math.Round(panValue * 50))
		return fmt.Sprintf("%dR", percentage)
	}
}

// shortString tronque une chaîne si elle dépasse la longueur maximale
func (m *LiveLearnDisplayManager) shortString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen-3] + "..."
}