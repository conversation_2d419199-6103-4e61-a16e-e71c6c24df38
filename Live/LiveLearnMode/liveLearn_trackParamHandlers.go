package livelearnmode

import (
	"fmt"
	"log"
	"math"
)

// --- Handlers pour les mises à jour de paramètres de piste ---

func (m *LiveLearnMode) handleTrackLearnGetVolume(args []interface{}) {
	if len(args) < 2 {
		log.Println("handleTrackLearnGetVolume: arguments insuffisants")
		return
	}

	// Extraire les arguments
	slotIndex, ok := ParseOscInt(args[0])
	if !ok {
		log.Println("handleTrackLearnGetVolume: impossible de convertir slotIndex en entier")
		return
	}

	value, ok := ParseOscFloat(args[1])
	if !ok {
		log.Println("handleTrackLearnGetVolume: impossible de convertir value en float64")
		return
	}

	// Vérifier si le slot existe et a un type défini
	m.state.Mutex.RLock()
	slot, exists := m.state.Slots[slotIndex]
	hasType := exists && slot.Type != nil && *slot.Type == ParamTypeVolume
	m.state.Mutex.RUnlock()

	if !exists || !hasType {
		return
	}

	// Mettre à jour la valeur (sans mettre à jour l'affichage)
	m.UpdateSlotInfoOnly(slotIndex, map[string]interface{}{
		"value": value,
	})

	// Calculer la page et l'index relatif pour l'affichage
	slotPage := (slotIndex / SlotsPerPage) + 1
	displaySlot := slotIndex % SlotsPerPage
	isOnCurrentPage := slotPage == m.state.CurrentPage

	// Mettre à jour l'affichage si le slot est sur la page courante
	if isOnCurrentPage {
		volumePercent := int(math.Round(value * 100))
		volumeDb := m.volumeConverter.ToDb(value)
		// Formater la valeur avec padStart(3, '0') comme dans le JS
		formattedValue := fmt.Sprintf("%03d", volumePercent)
		m.commManager.SendMessage(fmt.Sprintf("lu,%d,%s,%s dB", displaySlot, formattedValue, volumeDb), m.isActive)
	}
}

func (m *LiveLearnMode) handleTrackLearnGetPanning(args []interface{}) {
	if len(args) < 2 {
		log.Println("handleTrackLearnGetPanning: arguments insuffisants")
		return
	}

	// Extraire les arguments
	slotIndex, ok := ParseOscInt(args[0])
	if !ok {
		log.Println("handleTrackLearnGetPanning: impossible de convertir slotIndex en entier")
		return
	}

	value, ok := ParseOscFloat(args[1])
	if !ok {
		log.Println("handleTrackLearnGetPanning: impossible de convertir value en float64")
		return
	}

	// Vérifier si le slot existe et a un type défini
	m.state.Mutex.RLock()
	slot, exists := m.state.Slots[slotIndex]
	hasType := exists && slot.Type != nil && *slot.Type == ParamTypePan
	m.state.Mutex.RUnlock()

	if !exists || !hasType {
		return
	}

	// Mettre à jour la valeur (sans mettre à jour l'affichage)
	m.UpdateSlotInfoOnly(slotIndex, map[string]interface{}{
		"value": value,
	})

	// Calculer la page et l'index relatif pour l'affichage
	slotPage := (slotIndex / SlotsPerPage) + 1
	displaySlot := slotIndex % SlotsPerPage
	isOnCurrentPage := slotPage == m.state.CurrentPage

	// Mettre à jour l'affichage si le slot est sur la page courante
	if isOnCurrentPage {
		panPercent := int(math.Round((value + 1) * 50))
		panDisplay := m.displayManager.formatPanDisplay(value)
		// Formater la valeur avec padStart(3, '0') comme dans le JS
		formattedValue := fmt.Sprintf("%03d", panPercent)
		m.commManager.SendMessage(fmt.Sprintf("lu,%d,%s,%s", displaySlot, formattedValue, panDisplay), m.isActive)
	}
}

func (m *LiveLearnMode) handleTrackLearnGetSends(args []interface{}) {
	if len(args) < 2 {
		log.Println("handleTrackLearnGetSends: arguments insuffisants")
		return
	}

	// Extraire les arguments
	slotIndex, ok := ParseOscInt(args[0])
	if !ok {
		log.Println("handleTrackLearnGetSends: impossible de convertir slotIndex en entier")
		return
	}

	value, ok := ParseOscFloat(args[1])
	if !ok {
		log.Println("handleTrackLearnGetSends: impossible de convertir value en float64")
		return
	}

	// Vérifier si le slot existe et a un type défini
	m.state.Mutex.RLock()
	slot, exists := m.state.Slots[slotIndex]
	hasType := exists && slot.Type != nil && *slot.Type == ParamTypeSend
	m.state.Mutex.RUnlock()

	if !exists || !hasType {
		return
	}

	// Mettre à jour la valeur (sans mettre à jour l'affichage)
	m.UpdateSlotInfoOnly(slotIndex, map[string]interface{}{
		"value": value,
	})

	// Calculer la page et l'index relatif pour l'affichage
	slotPage := (slotIndex / SlotsPerPage) + 1
	displaySlot := slotIndex % SlotsPerPage
	isOnCurrentPage := slotPage == m.state.CurrentPage

	// Mettre à jour l'affichage si le slot est sur la page courante
	if isOnCurrentPage {
		sendPercent := int(math.Round(value * 100))
		sendDb := m.volumeSendConverter.ToDb(value)
		// Formater la valeur avec padStart(3, '0') comme dans le JS
		formattedValue := fmt.Sprintf("%03d", sendPercent)
		m.commManager.SendMessage(fmt.Sprintf("lu,%d,%s,%s dB", displaySlot, formattedValue, sendDb), m.isActive)
	}
}

func (m *LiveLearnMode) handleTrackLearnGetMute(args []interface{}) {
	if len(args) < 2 {
		log.Println("handleTrackLearnGetMute: arguments insuffisants")
		return
	}

	// Extraire les arguments
	slotIndex, ok := ParseOscInt(args[0])
	if !ok {
		log.Println("handleTrackLearnGetMute: impossible de convertir slotIndex en entier")
		return
	}

	valueInt, ok := ParseOscInt(args[1])
	if !ok {
		log.Println("handleTrackLearnGetMute: impossible de convertir value en entier")
		return
	}
	value := valueInt == 1 // Convertir en booléen

	// Vérifier si le slot existe et a un type défini
	m.state.Mutex.RLock()
	slot, exists := m.state.Slots[slotIndex]
	hasType := exists && slot.Type != nil && *slot.Type == ParamTypeMute
	m.state.Mutex.RUnlock()

	if !exists || !hasType {
		return
	}

	// Mettre à jour la valeur
	valueFloat := float64(0)
	if value {
		valueFloat = 1.0
	}

	m.UpdateSlotInfoOnly(slotIndex, map[string]interface{}{
		"value": valueFloat,
	})

	// Calculer la page et l'index relatif pour l'affichage
	slotPage := (slotIndex / SlotsPerPage) + 1
	displaySlot := slotIndex % SlotsPerPage
	isOnCurrentPage := slotPage == m.state.CurrentPage

	// Mettre à jour l'affichage si le slot est sur la page courante
	if isOnCurrentPage {
		percentValue := 0
		display := "off"
		if value {
			percentValue = 100
			display = "on"
		}
		// Formater la valeur avec padStart(3, '0') comme dans le JS
		formattedValue := fmt.Sprintf("%03d", percentValue)
		m.commManager.SendMessage(fmt.Sprintf("lu,%d,%s,%s", displaySlot, formattedValue, display), m.isActive)
	}
}

func (m *LiveLearnMode) handleTrackLearnGetSolo(args []interface{}) {
	if len(args) < 2 {
		log.Println("handleTrackLearnGetSolo: arguments insuffisants")
		return
	}

	// Extraire les arguments
	slotIndex, ok := ParseOscInt(args[0])
	if !ok {
		log.Println("handleTrackLearnGetSolo: impossible de convertir slotIndex en entier")
		return
	}

	valueInt, ok := ParseOscInt(args[1])
	if !ok {
		log.Println("handleTrackLearnGetSolo: impossible de convertir value en entier")
		return
	}
	value := valueInt == 1 // Convertir en booléen

	// Vérifier si le slot existe et a un type défini
	m.state.Mutex.RLock()
	slot, exists := m.state.Slots[slotIndex]
	hasType := exists && slot.Type != nil && *slot.Type == ParamTypeSolo
	m.state.Mutex.RUnlock()

	if !exists || !hasType {
		return
	}

	// Mettre à jour la valeur
	valueFloat := float64(0)
	if value {
		valueFloat = 1.0
	}

	m.UpdateSlotInfoOnly(slotIndex, map[string]interface{}{
		"value": valueFloat,
	})

	// Calculer la page et l'index relatif pour l'affichage
	slotPage := (slotIndex / SlotsPerPage) + 1
	displaySlot := slotIndex % SlotsPerPage
	isOnCurrentPage := slotPage == m.state.CurrentPage

	// Mettre à jour l'affichage si le slot est sur la page courante
	if isOnCurrentPage {
		percentValue := 0
		display := "off"
		if value {
			percentValue = 100
			display = "on"
		}
		// Formater la valeur avec padStart(3, '0') comme dans le JS
		formattedValue := fmt.Sprintf("%03d", percentValue)
		m.commManager.SendMessage(fmt.Sprintf("lu,%d,%s,%s", displaySlot, formattedValue, display), m.isActive)
	}
}
