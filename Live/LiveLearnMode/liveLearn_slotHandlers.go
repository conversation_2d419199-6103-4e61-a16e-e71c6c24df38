package livelearnmode

import (
	"fmt"
	"log"
)

// ParseOscInt convertit une valeur OSC en int
func ParseOscInt(val interface{}) (int, bool) {
	// OSC peut envoyer des int32 ou int64
	switch v := val.(type) {
	case int:
		return v, true
	case int32:
		return int(v), true
	case int64:
		// Attention: perte potentielle de précision si > max int
		return int(v), true
	case float32:
		return int(v), true
	case float64:
		return int(v), true
	default:
		log.Printf("Erreur de parsing OSC: type inattendu pour int: %T", val)
		return 0, false
	}
}

// --- Handlers pour les propriétés des slots ---

func (m *LiveLearnMode) handleSlotProperties(args []any) {
	// Ajouter un identifiant unique pour ce traitement pour le débogage
	processID := fmt.Sprintf("%p", &args)
	log.Printf("handleSlotProperties: début du traitement (processID: %s)", processID)

	if len(args) < 4 {
		log.Println("handleSlotProperties: arguments insuffisants")
		return
	}

	// Extraire les arguments
	slotIndex, ok := ParseOscInt(args[0])
	if !ok {
		log.Println("handleSlotProperties: impossible de convertir slotIndex en entier")
		return
	}

	deviceName, _ := args[1].(string)
	trackName, _ := args[2].(string)

	// Extraire la couleur de la piste (peut être un entier ou une chaîne)
	var trackColor string
	switch v := args[3].(type) {
	case string:
		trackColor = v
	case int32:
		trackColor = fmt.Sprintf("%d", v)
	case int:
		trackColor = fmt.Sprintf("%d", v)
	case int64:
		trackColor = fmt.Sprintf("%d", v)
	case float32:
		trackColor = fmt.Sprintf("%d", int(v))
	case float64:
		trackColor = fmt.Sprintf("%d", int(v))
	default:
		log.Printf("handleSlotProperties: type inattendu pour trackColor: %T", args[3])
		trackColor = "0" // Valeur par défaut
	}

	// Vérifier si le slot existe et a un type défini
	m.state.Mutex.RLock()
	existingSlot, exists := m.state.Slots[slotIndex]
	hasType := exists && existingSlot.Type != nil

	// Ajouter des logs détaillés pour le débogage
	if exists && existingSlot.Type != nil {
		log.Printf("handleSlotProperties: slot %d existe avec type=%d, paramName=%s, value=%v (processID: %s)",
			slotIndex,
			*existingSlot.Type,
			existingSlot.ParameterName,
			existingSlot.Value,
			processID)
	} else {
		log.Printf("handleSlotProperties: slot %d - exists=%v, hasType=%v (processID: %s)",
			slotIndex, exists, hasType, processID)
	}

	m.state.Mutex.RUnlock()

	if !exists || !hasType {
		log.Printf("handleSlotProperties: slot %d n'existe pas ou n'a pas de type défini (processID: %s)",
			slotIndex, processID)
		return
	}

	// Mettre à jour les informations du slot (état interne uniquement)
	updates := map[string]interface{}{
		"deviceName": deviceName,
		"trackName":  trackName,
		"trackColor": trackColor,
	}

	// Mettre à jour l'état interne
	slot := m.UpdateSlotInfoOnly(slotIndex, updates)

	// Calculer la page et l'index relatif pour l'affichage
	slotPage := (slotIndex / SlotsPerPage) + 1
	displaySlot := slotIndex % SlotsPerPage
	isOnCurrentPage := slotPage == m.state.CurrentPage

	// Mettre à jour l'affichage si le slot est sur la page courante
	if isOnCurrentPage && slot != nil {
		// Envoyer UNIQUEMENT le message lo (pas de message ls)
		// Le message ls est envoyé par d'autres handlers (comme handleTrackLearningVolume)
		deviceNameToSend := ""
		if slot.DeviceName != "" {
			deviceNameToSend = fmt.Sprintf("• %s", slot.DeviceName)
		}

		// Construire le message à envoyer
		loMessage := fmt.Sprintf("lo,%d,%s,%s,%s", displaySlot, deviceNameToSend, slot.TrackName, slot.TrackColor)

		// Vérifier si ce message a déjà été envoyé récemment pour ce slot
		LoMessageMutex.Lock()
		lastMessage, exists := LastSentLoMessages[slotIndex]
		shouldSend := !exists || lastMessage != loMessage

		if shouldSend {
			// Mettre à jour le dernier message envoyé
			LastSentLoMessages[slotIndex] = loMessage
			LoMessageMutex.Unlock()

			log.Printf("handleSlotProperties: envoi du message lo pour le slot %d: %s (processID: %s)",
				slotIndex, loMessage, processID)

			m.commManager.SendMessage(loMessage, m.isActive)
		} else {
			LoMessageMutex.Unlock()
			log.Printf("handleSlotProperties: message lo ignoré car identique au précédent pour le slot %d (processID: %s)",
				slotIndex, processID)
		}
	}

	log.Printf("handleSlotProperties: fin du traitement (processID: %s)", processID)
}

func (m *LiveLearnMode) handleSlotCleared(args []any) {
	if len(args) < 1 {
		log.Println("handleSlotCleared: arguments insuffisants")
		return
	}

	// Extraire l'index du slot
	slotIndex, ok := ParseOscInt(args[0])
	if !ok {
		log.Println("handleSlotCleared: impossible de convertir slotIndex en entier")
		return
	}

	// Afficher plus d'informations pour le débogage
	m.state.Mutex.RLock()
	currentPage := m.state.CurrentPage
	m.state.Mutex.RUnlock()
	log.Printf("Effacement du slot %d (page actuelle: %d, isActive: %v)", slotIndex, currentPage, m.isActive)

	// Calculer la page et l'index relatif pour l'affichage
	slotPage := (slotIndex / SlotsPerPage) + 1
	displaySlot := slotIndex % SlotsPerPage

	// IMPORTANT: Ne pas appeler m.ClearSlot() ici car cela créerait une boucle infinie:
	// 1. handleSlotCleared reçoit /live/learn/slot/cleared
	// 2. ClearSlot envoie /live/learn/del_slot
	// 3. Python reçoit /live/learn/del_slot et renvoie /live/learn/slot/cleared
	// 4. Et le cycle recommence
	//
	// Au lieu de cela, on met à jour l'état interne directement:
	m.state.Mutex.Lock()
	if _, exists := m.state.Slots[slotIndex]; exists {
		m.state.Slots[slotIndex] = NewSlotInfo(slotIndex)
	}
	m.state.Mutex.Unlock()

	// Si le slot est sur la page courante, envoyer un message spécifique au format exact du JS
	if slotPage == currentPage {
		// Format exact du JS: ls,displaySlot,null,-,-,-,-,-
		message := fmt.Sprintf("ls,%d,null,-,-,-,-,-", displaySlot)
		log.Printf("Envoi du message d'effacement: %s (isActive: %v)", message, m.isActive)
		m.commManager.SendMessage(message, m.isActive)
	} else {
		log.Printf("Slot %d non affiché sur la page courante %d (page du slot: %d)",
			slotIndex, currentPage, slotPage)
	}
}
