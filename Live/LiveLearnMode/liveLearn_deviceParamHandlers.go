package livelearnmode

import (
	"fmt"
	"log"
	"math"
	"strconv"
)

// --- Handlers pour les mises à jour de paramètres de device ---

func (m *LiveLearnMode) handleDeviceLearnGetParamValue(args []interface{}) {
	if len(args) < 4 {
		log.Println("handleDeviceLearnGetParamValue: arguments insuffisants")
		return
	}

	// Extraire les arguments
	slotIndex, ok := ParseOscInt(args[0])
	if !ok {
		log.Println("handleDeviceLearnGetParamValue: impossible de convertir slotIndex en entier")
		return
	}

	value, ok := ParseOscFloat(args[2])
	if !ok {
		log.Println("handleDeviceLearnGetParamValue: impossible de convertir value en float64")
		return
	}

	valueString, ok := args[3].(string)
	if !ok {
		log.Println("handleDeviceLearnGetParamValue: valueString n'est pas une chaîne")
		valueString = fmt.Sprintf("%.2f", value)
	}

	// Vérifier si le slot existe et a un type défini
	m.state.Mutex.RLock()
	slot, exists := m.state.Slots[slotIndex]
	hasType := exists && slot.Type != nil && *slot.Type == ParamTypeDevice
	isLearning := m.state.IsLearning
	m.state.Mutex.RUnlock()

	if !exists || !hasType {
		return
	}

	// Ajouter des logs détaillés pour le débogage
	log.Printf("DEBUG: handleDeviceLearnGetParamValue - Mise à jour du slot %d avec value: %v, valueString: '%s'",
		slotIndex, value, valueString)

	// Mettre à jour les valeurs (sans mettre à jour l'affichage)
	m.UpdateSlotInfoOnly(slotIndex, map[string]interface{}{
		"value":       value,
		"valueString": valueString,
	})

	// Vérifier que la mise à jour a bien été effectuée
	m.state.Mutex.RLock()
	updatedSlot, exists := m.state.Slots[slotIndex]
	m.state.Mutex.RUnlock()
	if exists {
		log.Printf("DEBUG: handleDeviceLearnGetParamValue - Après mise à jour: slot %d, value: %v, valueString: '%s'",
			slotIndex, *updatedSlot.Value, updatedSlot.ValueString)
	}

	// Calculer la page et l'index relatif pour l'affichage
	slotPage := (slotIndex / SlotsPerPage) + 1
	displaySlot := slotIndex % SlotsPerPage
	isOnCurrentPage := slotPage == m.state.CurrentPage

	// Note: Ces variables ne sont plus utilisées directement dans les messages lu
	// mais sont conservées pour une éventuelle utilisation future

	// Récupérer les indices de device et de paramètre
	var deviceIndex, parameterIndex int
	if slot.DeviceIndex != nil {
		deviceIndex = *slot.DeviceIndex
	}
	if slot.ParameterIndex != nil {
		parameterIndex = *slot.ParameterIndex
	}

	// Envoyer un message OSC de confirmation à Live seulement si on est en mode apprentissage
	if isLearning {
		log.Printf("[DEBUG] handleDeviceLearnGetParamValue: envoi du message de confirmation pour le slot %d", slotIndex)
		m.BaseMode.Send(OscAddressLearnSlot, []interface{}{slotIndex, int(ParamTypeDevice), ChainTrackIndex, deviceIndex, parameterIndex})
	}

	// Mettre à jour l'affichage si le slot est sur la page courante
	if isOnCurrentPage {
		if slot.Min != nil && slot.Max != nil && *slot.Max > *slot.Min {
			// Normaliser la valeur entre 0 et 100
			normalizedValue := int(math.Round(((value - *slot.Min) / (*slot.Max - *slot.Min)) * 100))
			// Formater la valeur avec padStart(3, '0') comme dans le JS
			formattedValue := fmt.Sprintf("%03d", normalizedValue)
			// Utiliser lu pour les mises à jour de valeurs (format identique à JS)
			m.commManager.SendMessage(fmt.Sprintf("lu,%d,%s,%s", displaySlot, formattedValue, valueString), m.isActive)
		} else {
			// Fallback pour les cas où min et max ne sont pas définis ou égaux
			// Utiliser directement la valeur comme pourcentage
			normalizedValue := int(math.Round(value * 100))
			formattedValue := fmt.Sprintf("%03d", normalizedValue)
			// Utiliser lu pour les mises à jour de valeurs (format identique à JS)
			m.commManager.SendMessage(fmt.Sprintf("lu,%d,%s,%s", displaySlot, formattedValue, valueString), m.isActive)
		}
	}

	// Désactiver le mode apprentissage seulement si on est en mode apprentissage
	if isLearning {
		log.Printf("[DEBUG] handleDeviceLearnGetParamValue: désactivation du mode apprentissage pour le slot %d", slotIndex)
		m.state.Mutex.Lock()
		m.state.IsLearning = false
		m.state.Mutex.Unlock()

		// Mettre à jour l'affichage du statut d'apprentissage
		m.displayManager.UpdateLearningStatus(false, 0)
	}
}

// --- Handlers pour les paramètres de device ---

func (m *LiveLearnMode) handleDeviceLearningBulkParams(args []interface{}) {
	if len(args) < 6 { // Au minimum deviceIndex + un paramètre (nom, isQuantized, min, max)
		log.Println("handleDeviceLearningBulkParams: arguments insuffisants")
		return
	}

	// Extraire l'index du device
	deviceIndex, ok := ParseOscInt(args[1])
	if !ok {
		log.Println("handleDeviceLearningBulkParams: impossible de convertir deviceIndex en entier")
		return
	}

	// Créer ou récupérer les paramètres du device
	deviceParams, exists := m.deviceParameters[deviceIndex]
	if !exists {
		deviceParams = &DeviceParameters{
			Names:       make([]string, 0),
			IsQuantized: make([]bool, 0),
			Min:         make([]float64, 0),
			Max:         make([]float64, 0),
			DeviceName:  fmt.Sprintf("Device %d", deviceIndex),
		}
		m.deviceParameters[deviceIndex] = deviceParams
	}

	// Traiter les paramètres par groupes de 4 (nom, isQuantized, min, max)
	for i := 2; i < len(args)-3; i += 4 {
		paramIndex := (i - 2) / 4

		// Étendre les slices si nécessaire
		for len(deviceParams.Names) <= paramIndex {
			deviceParams.Names = append(deviceParams.Names, "")
			deviceParams.IsQuantized = append(deviceParams.IsQuantized, false)
			deviceParams.Min = append(deviceParams.Min, 0)
			deviceParams.Max = append(deviceParams.Max, 1)
		}

		// Nom du paramètre
		if paramName, ok := args[i].(string); ok {
			deviceParams.Names[paramIndex] = paramName
		}

		// Paramètre quantifié
		if isQuantized, ok := args[i+1].(int); ok {
			deviceParams.IsQuantized[paramIndex] = isQuantized == 1
		} else if isQuantized, ok := args[i+1].(bool); ok {
			deviceParams.IsQuantized[paramIndex] = isQuantized
		}

		// Valeur minimale
		if minVal, ok := args[i+2].(float64); ok {
			deviceParams.Min[paramIndex] = minVal
		} else if minValStr, ok := args[i+2].(string); ok {
			if minVal, err := strconv.ParseFloat(minValStr, 64); err == nil {
				deviceParams.Min[paramIndex] = minVal
			}
		}

		// Valeur maximale
		if maxVal, ok := args[i+3].(float64); ok {
			deviceParams.Max[paramIndex] = maxVal
		} else if maxValStr, ok := args[i+3].(string); ok {
			if maxVal, err := strconv.ParseFloat(maxValStr, 64); err == nil {
				deviceParams.Max[paramIndex] = maxVal
			}
		}
	}

	log.Printf("Paramètres mis à jour pour le device %d: %d paramètres", deviceIndex, len(deviceParams.Names))
}

func (m *LiveLearnMode) handleDeviceLearningName(args []interface{}) {
	if len(args) < 3 {
		log.Println("handleDeviceLearningName: arguments insuffisants")
		return
	}

	// Extraire l'index du device et son nom
	deviceIndex, ok := ParseOscInt(args[1])
	if !ok {
		log.Println("handleDeviceLearningName: impossible de convertir deviceIndex en entier")
		return
	}

	deviceName, ok := args[2].(string)
	if !ok {
		log.Println("handleDeviceLearningName: deviceName n'est pas une chaîne")
		return
	}

	// Créer ou récupérer les paramètres du device
	deviceParams, exists := m.deviceParameters[deviceIndex]
	if !exists {
		deviceParams = &DeviceParameters{
			Names:       make([]string, 0),
			IsQuantized: make([]bool, 0),
			Min:         make([]float64, 0),
			Max:         make([]float64, 0),
		}
		m.deviceParameters[deviceIndex] = deviceParams
	}

	// Mettre à jour le nom du device
	deviceParams.DeviceName = deviceName
	log.Printf("Nom du device %d mis à jour: %s", deviceIndex, deviceName)
}

// handleDeviceParameters gère les messages de paramètres de device
// Cette fonction traite à la fois les messages bulk_parameters et name
func (m *LiveLearnMode) handleDeviceParameters(args []interface{}, address string) {
	log.Printf("[DEBUG] handleDeviceParameters - Start - address: %s, args: %v", address, args)
	log.Printf("[DEBUG] handleDeviceParameters - Nombre d'arguments: %d", len(args))

	// L'index du device est toujours le deuxième argument
	if len(args) < 2 {
		log.Println("handleDeviceParameters: arguments insuffisants")
		return
	}

	deviceIndex, ok := ParseOscInt(args[1])
	if !ok {
		log.Println("handleDeviceParameters: impossible de convertir deviceIndex en entier")
		return
	}

	// S'assurer que l'entrée pour ce device existe dans la map
	deviceParams, exists := m.deviceParameters[deviceIndex]
	if !exists {
		deviceParams = &DeviceParameters{
			Names:       make([]string, 0),
			IsQuantized: make([]bool, 0),
			Min:         make([]float64, 0),
			Max:         make([]float64, 0),
			DeviceName:  fmt.Sprintf("Device %d", deviceIndex), // Nom par défaut
		}
		m.deviceParameters[deviceIndex] = deviceParams
	}

	if address == OscAddressDeviceLearningBulkParams {
		// Le message contient plusieurs propriétés de paramètres
		// Format des args: [?, deviceIndex, param1Name, param1IsQuantized, param1Min, param1Max, param2Name, ...]
		log.Printf("[DEBUG] Traitement des paramètres bulk pour le device %d, nombre d'arguments: %d", deviceIndex, len(args))

		// Afficher tous les arguments pour le débogage
		for i, arg := range args {
			log.Printf("[DEBUG] Arg %d: %v (type: %T)", i, arg, arg)
		}

		// Traiter les paramètres par groupes de 4 (nom, isQuantized, min, max)
		log.Printf("[DEBUG] Début du traitement des paramètres, len(args)=%d", len(args))
		if len(args) <= 5 {
			log.Printf("[DEBUG] Pas assez d'arguments pour traiter les paramètres")
			return
		}

		for i := 2; i+3 < len(args); i += 4 {
			paramIndex := (i - 2) / 4 // Calculer l'index basé sur la position

			// Afficher les informations de traitement pour le débogage
			log.Printf("[DEBUG] Traitement du paramètre à l'index %d (args[%d])", paramIndex, i)

			// Étendre les slices si nécessaire
			for len(deviceParams.Names) <= paramIndex {
				deviceParams.Names = append(deviceParams.Names, "")
				deviceParams.IsQuantized = append(deviceParams.IsQuantized, false)
				deviceParams.Min = append(deviceParams.Min, 0)
				deviceParams.Max = append(deviceParams.Max, 1)
			}

			// Nom du paramètre
			if paramName, ok := args[i].(string); ok {
				log.Printf("[DEBUG] Paramètre %d: nom = %s", paramIndex, paramName)
				deviceParams.Names[paramIndex] = paramName
			} else {
				log.Printf("[DEBUG] Paramètre %d: nom non valide, type = %T", paramIndex, args[i])
			}

			// Paramètre quantifié
			if isQuantized, ok := args[i+1].(int); ok {
				deviceParams.IsQuantized[paramIndex] = isQuantized == 1
				log.Printf("[DEBUG] Paramètre %d: isQuantized = %v (int)", paramIndex, isQuantized == 1)
			} else if isQuantized, ok := args[i+1].(bool); ok {
				deviceParams.IsQuantized[paramIndex] = isQuantized
				log.Printf("[DEBUG] Paramètre %d: isQuantized = %v (bool)", paramIndex, isQuantized)
			} else {
				log.Printf("[DEBUG] Paramètre %d: isQuantized non valide, type = %T", paramIndex, args[i+1])
			}

			// Valeur minimale
			if minVal, ok := args[i+2].(float64); ok {
				deviceParams.Min[paramIndex] = minVal
				log.Printf("[DEBUG] Paramètre %d: min = %v (float64)", paramIndex, minVal)
			} else if minVal, ok := args[i+2].(float32); ok {
				deviceParams.Min[paramIndex] = float64(minVal)
				log.Printf("[DEBUG] Paramètre %d: min = %v (float32)", paramIndex, minVal)
			} else if minValStr, ok := args[i+2].(string); ok {
				if minVal, err := strconv.ParseFloat(minValStr, 64); err == nil {
					deviceParams.Min[paramIndex] = minVal
					log.Printf("[DEBUG] Paramètre %d: min = %v (string->float)", paramIndex, minVal)
				} else {
					log.Printf("[DEBUG] Paramètre %d: erreur de conversion de min: %v", paramIndex, err)
				}
			} else {
				log.Printf("[DEBUG] Paramètre %d: min non valide, type = %T", paramIndex, args[i+2])
			}

			// Valeur maximale
			if maxVal, ok := args[i+3].(float64); ok {
				deviceParams.Max[paramIndex] = maxVal
				log.Printf("[DEBUG] Paramètre %d: max = %v (float64)", paramIndex, maxVal)
			} else if maxVal, ok := args[i+3].(float32); ok {
				deviceParams.Max[paramIndex] = float64(maxVal)
				log.Printf("[DEBUG] Paramètre %d: max = %v (float32)", paramIndex, maxVal)
			} else if maxValStr, ok := args[i+3].(string); ok {
				if maxVal, err := strconv.ParseFloat(maxValStr, 64); err == nil {
					deviceParams.Max[paramIndex] = maxVal
					log.Printf("[DEBUG] Paramètre %d: max = %v (string->float)", paramIndex, maxVal)
				} else {
					log.Printf("[DEBUG] Paramètre %d: erreur de conversion de max: %v", paramIndex, err)
				}
			} else {
				log.Printf("[DEBUG] Paramètre %d: max non valide, type = %T", paramIndex, args[i+3])
			}
		}

		// Afficher les paramètres mis à jour pour le débogage
		log.Printf("[DEBUG] Paramètres bulk mis à jour pour le device %d:", deviceIndex)
		for i, name := range deviceParams.Names {
			log.Printf("[DEBUG]   Param %d: nom=%s, isQuantized=%v, min=%v, max=%v",
				i, name, deviceParams.IsQuantized[i], deviceParams.Min[i], deviceParams.Max[i])
		}

	} else if address == OscAddressDeviceLearningName {
		// Le message contient le nom du device
		// Format des args: [?, deviceIndex, deviceName]
		if len(args) < 3 {
			log.Println("handleDeviceParameters: arguments insuffisants pour le nom du device")
			return
		}

		deviceName, ok := args[2].(string)
		if !ok {
			log.Println("handleDeviceParameters: deviceName n'est pas une chaîne")
			return
		}

		deviceParams.DeviceName = deviceName
		log.Printf("[DEBUG] Nom du device reçu - index: %d, nom: %s", deviceIndex, deviceParams.DeviceName)
	}

	log.Printf("[DEBUG] Map des paramètres de device après mise à jour: %v", m.deviceParameters)
}
