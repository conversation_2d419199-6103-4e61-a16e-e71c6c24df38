package livelearnmode

import (
	"log"
)

// handleReadyToListen traite le message "prêt à écouter" de Live
func (m *LiveLearnMode) handleReadyToListen(args []interface{}) {
	log.Println("Live est prêt à écouter les messages d'apprentissage")
	
	// Vérifier si nous sommes en mode apprentissage
	m.state.Mutex.RLock()
	isLearning := m.state.IsLearning
	m.state.Mutex.RUnlock()
	
	if isLearning {
		log.Printf("Mode apprentissage actif - Slot actif: %v", m.state.ActiveSlot)
	} else {
		log.Printf("Mode apprentissage inactif - En attente d'un touch pour activer l'apprentissage")
	}
}
