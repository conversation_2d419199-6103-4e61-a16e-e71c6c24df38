package livelearnmode

import (
	"fmt"
	"log"
)

// registerAllOSCHandlers enregistre tous les handlers OSC pour le mode learn
func (m *LiveLearnMode) registerAllOSCHandlers() {
	log.Println("Enregistrement de tous les handlers OSC pour le mode learn...")

	// Handlers pour les mises à jour de paramètres de piste
	m.registerHandler(OscAddressTrackLearnGetVolume, m.handleTrackLearnGetVolume, "Handler pour les mises à jour de volume de piste")
	m.registerHandler(OscAddressTrackLearnGetPanning, m.handleTrackLearnGetPanning, "Handler pour les mises à jour de panoramique de piste")
	m.registerHandler(OscAddressTrackLearnGetSends, m.handleTrackLearnGetSends, "Handler pour les mises à jour de sends de piste")
	m.registerHandler(OscAddressTrackLearnGetMute, m.handleTrackLearnGetMute, "Handler pour les mises à jour de mute de piste")
	m.registerHandler(OscAddressTrackLearnGetSolo, m.handleTrackLearnGetSolo, "Handler pour les mises à jour de solo de piste")

	// Handlers pour les mises à jour de paramètres de device
	m.registerHandler(OscAddressDeviceLearnGetParamValue, m.handleDeviceLearnGetParamValue, "Handler pour les mises à jour de paramètres de device")

	// Handlers pour les mises à jour de paramètres de chaîne
	m.registerHandler(OscAddressChainLearnGetVolume, m.handleChainLearnGetVolume, "Handler pour les mises à jour de volume de chaîne")
	m.registerHandler(OscAddressChainLearnGetPanning, m.handleChainLearnGetPanning, "Handler pour les mises à jour de panoramique de chaîne")
	m.registerHandler(OscAddressChainLearnGetMute, m.handleChainLearnGetMute, "Handler pour les mises à jour de mute de chaîne")
	m.registerHandler(OscAddressChainLearnGetSolo, m.handleChainLearnGetSolo, "Handler pour les mises à jour de solo de chaîne")

	// Handler pour le slot effacé
	m.registerHandler(OscAddressLearnSlotCleared, m.handleSlotCleared, "Handler pour le slot effacé")

	// Handlers pour les paramètres de device
	m.registerHandler(OscAddressDeviceLearningBulkParams, func(args []interface{}) {
		m.handleOscMessage(args, OscAddressDeviceLearningBulkParams)
	}, "Handler pour les paramètres de device")

	m.registerHandler(OscAddressDeviceLearningName, func(args []interface{}) {
		m.handleOscMessage(args, OscAddressDeviceLearningName)
	}, "Handler pour le nom de device")

	// Handlers pour l'état "prêt à écouter"
	m.registerHandler(OscAddressReadyToListen, func(args []interface{}) {
		m.handleOscMessage(args, OscAddressReadyToListen)
	}, "Handler pour l'état prêt à écouter")

	// Handlers pour l'apprentissage des paramètres de piste
	m.registerHandler(OscAddressTrackLearningVolume, func(args []interface{}) {
		m.handleOscMessage(args, OscAddressTrackLearningVolume)
	}, "Handler pour l'apprentissage du volume de piste")

	m.registerHandler(OscAddressTrackLearningPanning, func(args []interface{}) {
		m.handleOscMessage(args, OscAddressTrackLearningPanning)
	}, "Handler pour l'apprentissage du panoramique de piste")

	m.registerHandler(OscAddressTrackLearningSends, func(args []interface{}) {
		m.handleOscMessage(args, OscAddressTrackLearningSends)
	}, "Handler pour l'apprentissage des sends de piste")

	m.registerHandler(OscAddressTrackLearningMute, func(args []interface{}) {
		m.handleOscMessage(args, OscAddressTrackLearningMute)
	}, "Handler pour l'apprentissage du mute de piste")

	m.registerHandler(OscAddressTrackLearningSolo, func(args []interface{}) {
		m.handleOscMessage(args, OscAddressTrackLearningSolo)
	}, "Handler pour l'apprentissage du solo de piste")

	// Handlers pour l'apprentissage des paramètres de device
	m.registerHandler(OscAddressDeviceLearningParamValue, func(args []interface{}) {
		m.handleOscMessage(args, OscAddressDeviceLearningParamValue)
	}, "Handler pour l'apprentissage des paramètres de device")

	m.registerHandler(OscAddressDeviceLearningParamValueStr, func(args []interface{}) {
		m.handleOscMessage(args, OscAddressDeviceLearningParamValueStr)
	}, "Handler pour l'apprentissage des paramètres de device (string)")

	// Handlers pour l'apprentissage des paramètres de chaîne
	m.registerHandler(OscAddressChainLearningVolume, func(args []interface{}) {
		m.handleOscMessage(args, OscAddressChainLearningVolume)
	}, "Handler pour l'apprentissage du volume de chaîne")

	m.registerHandler(OscAddressChainLearningPanning, func(args []interface{}) {
		m.handleOscMessage(args, OscAddressChainLearningPanning)
	}, "Handler pour l'apprentissage du panoramique de chaîne")

	m.registerHandler(OscAddressChainLearningMute, func(args []interface{}) {
		m.handleOscMessage(args, OscAddressChainLearningMute)
	}, "Handler pour l'apprentissage du mute de chaîne")

	m.registerHandler(OscAddressChainLearningSolo, func(args []interface{}) {
		m.handleOscMessage(args, OscAddressChainLearningSolo)
	}, "Handler pour l'apprentissage du solo de chaîne")

	// Handlers pour les propriétés des paramètres de device verrouillé
	m.registerHandler(OscAddressGetLockedDeviceParamProps, func(args []interface{}) {
		m.handleOscMessage(args, OscAddressGetLockedDeviceParamProps)
	}, "Handler pour les propriétés des paramètres de device verrouillé")

	// Handlers pour les propriétés de slot
	m.registerHandler(OscAddressLearnSlotGetProperties, func(args []interface{}) {
		log.Printf("Handler OscAddressLearnSlotGetProperties appelé (adresse: %s)", OscAddressLearnSlotGetProperties)
		m.handleOscMessage(args, OscAddressLearnSlotGetProperties)
	}, "Handler pour les propriétés de slot")

	log.Println("Tous les handlers OSC pour le mode learn enregistrés avec succès.")
}

// registerHandler est une fonction utilitaire pour enregistrer un handler OSC
func (m *LiveLearnMode) registerHandler(address string, handler func([]interface{}), description string) {
	// Désenregistrer d'abord pour éviter les enregistrements multiples
	m.BaseMode.UnregisterHandler(address)

	// Enregistrer le handler avec une description complète
	fullDescription := fmt.Sprintf("Handler pour %s en mode learn", description)
	m.BaseMode.RegisterHandler(address, handler, fullDescription)
}

// unregisterAllOSCHandlers supprime tous les handlers OSC
func (m *LiveLearnMode) unregisterAllOSCHandlers() {
	log.Println("Suppression de tous les handlers OSC pour le mode learn...")

	// Handlers pour les mises à jour de paramètres de piste
	m.BaseMode.UnregisterHandler(OscAddressTrackLearnGetVolume)
	m.BaseMode.UnregisterHandler(OscAddressTrackLearnGetPanning)
	m.BaseMode.UnregisterHandler(OscAddressTrackLearnGetSends)
	m.BaseMode.UnregisterHandler(OscAddressTrackLearnGetMute)
	m.BaseMode.UnregisterHandler(OscAddressTrackLearnGetSolo)

	// Handlers pour les mises à jour de paramètres de device
	m.BaseMode.UnregisterHandler(OscAddressDeviceLearnGetParamValue)

	// Handlers pour les mises à jour de paramètres de chaîne
	m.BaseMode.UnregisterHandler(OscAddressChainLearnGetVolume)
	m.BaseMode.UnregisterHandler(OscAddressChainLearnGetPanning)
	m.BaseMode.UnregisterHandler(OscAddressChainLearnGetMute)
	m.BaseMode.UnregisterHandler(OscAddressChainLearnGetSolo)

	// Handler pour le slot effacé
	m.BaseMode.UnregisterHandler(OscAddressLearnSlotCleared)

	// Handlers pour les paramètres de device
	m.BaseMode.UnregisterHandler(OscAddressDeviceLearningBulkParams)
	m.BaseMode.UnregisterHandler(OscAddressDeviceLearningName)

	// Handlers pour l'état "prêt à écouter"
	m.BaseMode.UnregisterHandler(OscAddressReadyToListen)

	// Handlers pour l'apprentissage des paramètres de piste
	m.BaseMode.UnregisterHandler(OscAddressTrackLearningVolume)
	m.BaseMode.UnregisterHandler(OscAddressTrackLearningPanning)
	m.BaseMode.UnregisterHandler(OscAddressTrackLearningSends)
	m.BaseMode.UnregisterHandler(OscAddressTrackLearningMute)
	m.BaseMode.UnregisterHandler(OscAddressTrackLearningSolo)

	// Handlers pour l'apprentissage des paramètres de device
	m.BaseMode.UnregisterHandler(OscAddressDeviceLearningParamValue)
	m.BaseMode.UnregisterHandler(OscAddressDeviceLearningParamValueStr)

	// Handlers pour l'apprentissage des paramètres de chaîne
	m.BaseMode.UnregisterHandler(OscAddressChainLearningVolume)
	m.BaseMode.UnregisterHandler(OscAddressChainLearningPanning)
	m.BaseMode.UnregisterHandler(OscAddressChainLearningMute)
	m.BaseMode.UnregisterHandler(OscAddressChainLearningSolo)

	// Handlers pour les propriétés des paramètres de device verrouillé
	m.BaseMode.UnregisterHandler(OscAddressGetLockedDeviceParamProps)

	// Handlers pour les propriétés de slot
	m.BaseMode.UnregisterHandler(OscAddressLearnSlotGetProperties)

	log.Println("Tous les handlers OSC pour le mode learn supprimés avec succès.")
}
