package livelearnmode

import (
	"sync"
)

// Variables globales pour la gestion des messages
var (
	// Cache pour éviter les messages dupliqués
	LastSentLoMessages = make(map[int]string)
	LoMessageMutex     sync.Mutex
)

// Configuration constants
const (
	SlotsPerPage = 8 // Nombre de slots par page
)

// Parameter types for learn mode
const (
	ParamTypeVolume      = 1
	ParamTypePan         = 2
	ParamTypeSend        = 3
	ParamTypeDevice      = 4
	ParamTypeChainVolume = 5
	ParamTypeChainPan    = 6
	ParamTypeMute        = 7
	ParamTypeSolo        = 8
	ParamTypeChainMute   = 9
	ParamTypeChainSolo   = 10
)

// Special indices
const (
	LockedDeviceIndex = -4 // Index spécial pour un device verrouillé
	ChainTrackIndex   = -2 // Index spécial pour les paramètres de chaîne
)

// Constantes pour les adresses OSC d'écoute
const (
	// Adresses pour les propriétés et mises à jour de slots
	OscAddressLearnSlotGetProperties    = "/live/learn/slot/reference"
	OscAddressLearnSlotCleared          = "/live/learn/slot/cleared"

	// Adresses pour les mises à jour de paramètres de piste
	OscAddressTrackLearnGetPanning      = "/live/tracklearn/get/panning"
	OscAddressTrackLearnGetVolume       = "/live/tracklearn/get/volume"
	OscAddressTrackLearnGetSends        = "/live/tracklearn/get/sends"
	OscAddressTrackLearnGetMute         = "/live/tracklearn/get/mute"
	OscAddressTrackLearnGetSolo         = "/live/tracklearn/get/solo"

	// Adresses pour les mises à jour de paramètres de device
	OscAddressDeviceLearnGetParamValue  = "/live/devicelearn/get/parameter/value"

	// Adresses pour les mises à jour de paramètres de chaîne
	OscAddressChainLearnGetVolume       = "/live/chainlearn/get/volume"
	OscAddressChainLearnGetPanning      = "/live/chainlearn/get/panning"
	OscAddressChainLearnGetMute         = "/live/chainlearn/get/mute"
	OscAddressChainLearnGetSolo         = "/live/chainlearn/get/solo"

	// Adresses pour l'apprentissage des paramètres de device
	OscAddressDeviceLearningBulkParams    = "/live/device/learning/bulk_parameters"
	OscAddressDeviceLearningName          = "/live/device/learning/name"
	OscAddressDeviceLearningParamValue    = "/live/device/learning/parameter/value"
	OscAddressDeviceLearningParamValueStr = "/live/device/learning/parameter/value_string"

	// Adresses pour l'apprentissage des paramètres de piste
	OscAddressTrackLearningPanning        = "/live/track/learning/panning"
	OscAddressTrackLearningVolume         = "/live/track/learning/volume"
	OscAddressTrackLearningSends          = "/live/track/learning/sends"
	OscAddressTrackLearningMute           = "/live/track/learning/mute"
	OscAddressTrackLearningSolo           = "/live/track/learning/solo"

	// Adresses pour l'apprentissage des paramètres de chaîne
	OscAddressChainLearningVolume         = "/live/chain/learning/volume"
	OscAddressChainLearningPanning        = "/live/chain/learning/panning"
	OscAddressChainLearningMute           = "/live/chain/learning/mute"
	OscAddressChainLearningSolo           = "/live/chain/learning/solo"

	// Autres adresses
	OscAddressReadyToListen               = "/live/readyToListen"
	OscAddressGetLockedDeviceParamProps   = "/live/get/lockedDevice/paramProperties"
)

// Constantes pour les adresses OSC d'envoi
const (
	OscAddressLearnSlot        = "/live/learn/slot"
	OscAddressLearnStop        = "/live/learn/stop"
	OscAddressLearnStopDevice  = "/live/learn/stop_device"
	OscAddressLearnStopTrack   = "/live/learn/stop_track"
	OscAddressLearnSetupTrack  = "/live/learn/setup_track"
	OscAddressLearnSetupDevice = "/live/learn/setup_device"
	OscAddressLearnDelSlot     = "/live/learn/del_slot"
)

// SlotInfo représente les informations d'un slot d'apprentissage
type SlotInfo struct {
	Index          int
	Type           *int    // Type de paramètre (nil si non défini)
	TrackColor     string
	TrackName      string
	DeviceName     string
	ParameterName  string
	ParameterIndex *int     // Index du paramètre (nil si non applicable)
	SendIndex      *int     // Index du send (nil si non applicable)
	DeviceIndex    *int     // Index du device (nil si non applicable)
	ChainPath      []int    // Chemin de la chaîne (vide si non applicable)
	Value          *float64 // Valeur actuelle (nil si non définie)
	ValueString    string
	IsQuantized    *bool    // Indique si le paramètre est quantifié (nil si non applicable)
	Min            *float64 // Valeur minimale (nil si non définie)
	Max            *float64 // Valeur maximale (nil si non définie)
	Buffer         int      // Buffer pour les opérations internes
}

// DeviceParameters représente les paramètres d'un device
type DeviceParameters struct {
	Names       []string
	IsQuantized []bool
	Min         []float64
	Max         []float64
	DeviceName  string
}

// LiveLearnModeState représente l'état du mode learn
type LiveLearnModeState struct {
	IsActive       bool
	IsLearning     bool
	ActiveSlot     *int
	CurrentPage    int
	Slots          map[int]*SlotInfo
	EncoderBuffers map[int]float64  // Buffers pour les encoders (index -> valeur accumulée)
	Mutex          sync.RWMutex
}

// NewSlotInfo crée une nouvelle instance de SlotInfo avec les valeurs par défaut
func NewSlotInfo(index int) *SlotInfo {
	return &SlotInfo{
		Index:          index,
		Type:           nil,
		TrackColor:     "",
		TrackName:      "",
		DeviceName:     "",
		ParameterName:  "",
		ParameterIndex: nil,
		SendIndex:      nil,
		DeviceIndex:    nil,
		ChainPath:      make([]int, 0),
		Value:          nil,
		ValueString:    "",
		IsQuantized:    nil,
		Min:            nil,
		Max:            nil,
		Buffer:         0,
	}
}

// NewLiveLearnModeState crée une nouvelle instance de LiveLearnModeState avec les valeurs par défaut
func NewLiveLearnModeState() *LiveLearnModeState {
	slots := make(map[int]*SlotInfo)

	// Initialiser 32 slots vides
	for i := 0; i < 32; i++ {
		slots[i] = NewSlotInfo(i)
	}

	return &LiveLearnModeState{
		IsActive:       false,
		IsLearning:     false,
		ActiveSlot:     nil,
		CurrentPage:    1,
		Slots:          slots,
		EncoderBuffers: make(map[int]float64),  // Initialiser les buffers d'encoders
	}
}