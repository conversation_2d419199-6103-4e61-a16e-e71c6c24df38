package livelearnmode

import (
	"fmt"
	"log"
	"math"
)

// --- Handlers pour les mises à jour de paramètres de chaîne ---

func (m *LiveLearnMode) handleChainLearnGetVolume(args []interface{}) {
	if len(args) < 2 {
		log.Println("handleChainLearnGetVolume: arguments insuffisants")
		return
	}

	// Extraire les arguments
	slotIndex, ok := ParseOscInt(args[0])
	if !ok {
		log.Println("handleChainLearnGetVolume: impossible de convertir slotIndex en entier")
		return
	}

	value, ok := ParseOscFloat(args[1])
	if !ok {
		log.Println("handleChainLearnGetVolume: impossible de convertir value en float64")
		return
	}

	// Vérifier si le slot existe et a un type défini
	m.state.Mutex.RLock()
	slot, exists := m.state.Slots[slotIndex]
	hasType := exists && slot.Type != nil && *slot.Type == ParamTypeChainVolume
	m.state.Mutex.RUnlock()

	if !exists || !hasType {
		return
	}

	// Mettre à jour la valeur
	updates := map[string]interface{}{
		"value": value,
	}

	m.UpdateSlotInfoOnly(slotIndex, updates)

	// Calculer la page et l'index relatif pour l'affichage
	slotPage := (slotIndex / SlotsPerPage) + 1
	displaySlot := slotIndex % SlotsPerPage
	isOnCurrentPage := slotPage == m.state.CurrentPage

	// Mettre à jour l'affichage si le slot est sur la page courante
	if isOnCurrentPage {
		volumePercent := int(math.Round(value * 100))
		volumeDb := m.volumeConverter.ToDb(value)
		// Formater la valeur avec padStart(3, '0') comme dans le JS
		formattedValue := fmt.Sprintf("%03d", volumePercent)
		m.commManager.SendMessage(fmt.Sprintf("lu,%d,%s,%s dB", displaySlot, formattedValue, volumeDb), m.isActive)
	}
}

func (m *LiveLearnMode) handleChainLearnGetPanning(args []interface{}) {
	if len(args) < 2 {
		log.Println("handleChainLearnGetPanning: arguments insuffisants")
		return
	}

	// Extraire les arguments
	slotIndex, ok := ParseOscInt(args[0])
	if !ok {
		log.Println("handleChainLearnGetPanning: impossible de convertir slotIndex en entier")
		return
	}

	value, ok := ParseOscFloat(args[1])
	if !ok {
		log.Println("handleChainLearnGetPanning: impossible de convertir value en float64")
		return
	}

	// Vérifier si le slot existe et a un type défini
	m.state.Mutex.RLock()
	slot, exists := m.state.Slots[slotIndex]
	hasType := exists && slot.Type != nil && *slot.Type == ParamTypeChainPan
	m.state.Mutex.RUnlock()

	if !exists || !hasType {
		return
	}

	// Mettre à jour la valeur
	updates := map[string]interface{}{
		"value": value,
	}

	m.UpdateSlotInfoOnly(slotIndex, updates)

	// Calculer la page et l'index relatif pour l'affichage
	slotPage := (slotIndex / SlotsPerPage) + 1
	displaySlot := slotIndex % SlotsPerPage
	isOnCurrentPage := slotPage == m.state.CurrentPage

	// Mettre à jour l'affichage si le slot est sur la page courante
	if isOnCurrentPage {
		panPercent := int(math.Round((value + 1) * 50))
		panDisplay := m.displayManager.formatPanDisplay(value)
		// Formater la valeur avec padStart(3, '0') comme dans le JS
		formattedValue := fmt.Sprintf("%03d", panPercent)
		m.commManager.SendMessage(fmt.Sprintf("lu,%d,%s,%s", displaySlot, formattedValue, panDisplay), m.isActive)
	}
}

func (m *LiveLearnMode) handleChainLearnGetMute(args []interface{}) {
	if len(args) < 2 {
		log.Println("handleChainLearnGetMute: arguments insuffisants")
		return
	}

	// Extraire les arguments
	slotIndex, ok := ParseOscInt(args[0])
	if !ok {
		log.Println("handleChainLearnGetMute: impossible de convertir slotIndex en entier")
		return
	}

	valueInt, ok := ParseOscInt(args[1])
	if !ok {
		log.Println("handleChainLearnGetMute: impossible de convertir value en entier")
		return
	}
	value := valueInt == 1 // Convertir en booléen

	// Vérifier si le slot existe et a un type défini
	m.state.Mutex.RLock()
	slot, exists := m.state.Slots[slotIndex]
	hasType := exists && slot.Type != nil && *slot.Type == ParamTypeChainMute
	m.state.Mutex.RUnlock()

	if !exists || !hasType {
		return
	}

	// Mettre à jour la valeur
	valueFloat := float64(0)
	if value {
		valueFloat = 1.0
	}

	updates := map[string]interface{}{
		"value": valueFloat,
	}

	m.UpdateSlotInfoOnly(slotIndex, updates)

	// Calculer la page et l'index relatif pour l'affichage
	slotPage := (slotIndex / SlotsPerPage) + 1
	displaySlot := slotIndex % SlotsPerPage
	isOnCurrentPage := slotPage == m.state.CurrentPage

	// Mettre à jour l'affichage si le slot est sur la page courante
	if isOnCurrentPage {
		percentValue := 0
		display := "off"
		if value {
			percentValue = 100
			display = "on"
		}
		// Formater la valeur avec padStart(3, '0') comme dans le JS
		formattedValue := fmt.Sprintf("%03d", percentValue)
		m.commManager.SendMessage(fmt.Sprintf("lu,%d,%s,%s", displaySlot, formattedValue, display), m.isActive)
	}
}

func (m *LiveLearnMode) handleChainLearnGetSolo(args []interface{}) {
	if len(args) < 2 {
		log.Println("handleChainLearnGetSolo: arguments insuffisants")
		return
	}

	// Extraire les arguments
	slotIndex, ok := ParseOscInt(args[0])
	if !ok {
		log.Println("handleChainLearnGetSolo: impossible de convertir slotIndex en entier")
		return
	}

	valueInt, ok := ParseOscInt(args[1])
	if !ok {
		log.Println("handleChainLearnGetSolo: impossible de convertir value en entier")
		return
	}
	value := valueInt == 1 // Convertir en booléen

	// Vérifier si le slot existe et a un type défini
	m.state.Mutex.RLock()
	slot, exists := m.state.Slots[slotIndex]
	hasType := exists && slot.Type != nil && *slot.Type == ParamTypeChainSolo
	m.state.Mutex.RUnlock()

	if !exists || !hasType {
		return
	}

	// Mettre à jour la valeur
	valueFloat := float64(0)
	if value {
		valueFloat = 1.0
	}

	updates := map[string]interface{}{
		"value": valueFloat,
	}

	m.UpdateSlotInfoOnly(slotIndex, updates)

	// Calculer la page et l'index relatif pour l'affichage
	slotPage := (slotIndex / SlotsPerPage) + 1
	displaySlot := slotIndex % SlotsPerPage
	isOnCurrentPage := slotPage == m.state.CurrentPage

	// Mettre à jour l'affichage si le slot est sur la page courante
	if isOnCurrentPage {
		percentValue := 0
		display := "off"
		if value {
			percentValue = 100
			display = "on"
		}
		// Formater la valeur avec padStart(3, '0') comme dans le JS
		formattedValue := fmt.Sprintf("%03d", percentValue)
		m.commManager.SendMessage(fmt.Sprintf("lu,%d,%s,%s", displaySlot, formattedValue, display), m.isActive)
	}
}
