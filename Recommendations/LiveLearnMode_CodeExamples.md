# Exemples de code pour le refactoring du mode LiveLearnMode

Ce document présente des exemples concrets de code pour illustrer les recommandations de refactoring du mode LiveLearnMode.

## 1. Structure principale simplifiée

### Avant (liveLearnMode.go)
```go
// LiveLearnMode gère le mode learn de Live
type LiveLearnMode struct {
    *communication.BaseMode
    trackManager      *live.LiveTrackManager
    commManager       *communication.CommunicationManager
    displayManager    *LiveLearnDisplayManager
    state             *LiveLearnModeState
    deviceParameters  map[int]*DeviceParameters
    isActive          bool
    volumeConverter   *utils.VolumeConverter
    volumeSendConverter *utils.VolumeSendConverter
    hardwareHandler    func(communication.HardwareEvent)
    lastMuteTime      int64
    lastMuteSlot      *int
    muteDebounceTime  int64
}
```

### Après (mode/learn_mode.go)
```go
// LiveLearnMode gère le mode learn de Live
type LiveLearnMode struct {
    *communication.BaseMode
    state          *State
    displayManager *display.DisplayManager
    handlerManager *handlers.HandlerManager
    messageManager *messages.MessageManager
    utilsManager   *utils.UtilsManager
}

// NewLiveLearnMode crée une nouvelle instance du mode learn
func NewLiveLearnMode(trackManager *live.LiveTrackManager, commManager *communication.CommunicationManager) *LiveLearnMode {
    state := NewState()
    
    displayManager := display.NewDisplayManager(commManager, trackManager, state)
    handlerManager := handlers.NewHandlerManager(trackManager, commManager, state)
    messageManager := messages.NewMessageManager(commManager)
    utilsManager := utils.NewUtilsManager()
    
    mode := &LiveLearnMode{
        BaseMode:       communication.NewBaseMode(),
        state:          state,
        displayManager: displayManager,
        handlerManager: handlerManager,
        messageManager: messageManager,
        utilsManager:   utilsManager,
    }
    
    // Configurer les références circulaires
    displayManager.SetParentMode(mode)
    handlerManager.SetParentMode(mode)
    
    return mode
}
```

## 2. Gestion de l'état

### Avant (liveLearn_types.go)
```go
// LiveLearnModeState représente l'état du mode learn
type LiveLearnModeState struct {
    IsActive    bool
    IsLearning  bool
    ActiveSlot  *int
    CurrentPage int
    Slots       map[int]*SlotInfo
    Mutex       sync.RWMutex
}
```

### Après (mode/state.go)
```go
// State représente l'état complet du mode learn
type State struct {
    IsActive         bool
    IsLearning       bool
    ActiveSlot       *int
    CurrentPage      int
    Slots            map[int]*types.SlotInfo
    DeviceParameters map[int]*types.DeviceParameters
    LastMuteTime     int64
    LastMuteSlot     *int
    MuteDebounceTime int64
    Mutex            sync.RWMutex
}

// NewState crée une nouvelle instance de State
func NewState() *State {
    slots := make(map[int]*types.SlotInfo)
    
    // Initialiser 32 slots vides
    for i := 0; i < 32; i++ {
        slots[i] = types.NewSlotInfo(i)
    }
    
    return &State{
        IsActive:         false,
        IsLearning:       false,
        ActiveSlot:       nil,
        CurrentPage:      1,
        Slots:            slots,
        DeviceParameters: make(map[int]*types.DeviceParameters),
        MuteDebounceTime: 100, // 100ms de délai pour le debounce
    }
}

// GetSlot récupère un slot par son index
func (s *State) GetSlot(index int) (*types.SlotInfo, bool) {
    s.Mutex.RLock()
    defer s.Mutex.RUnlock()
    
    slot, exists := s.Slots[index]
    return slot, exists
}

// UpdateSlot met à jour un slot
func (s *State) UpdateSlot(index int, updates map[string]interface{}) *types.SlotInfo {
    s.Mutex.Lock()
    defer s.Mutex.Unlock()
    
    slot, exists := s.Slots[index]
    if !exists {
        return nil
    }
    
    // Mettre à jour les champs du slot
    for key, value := range updates {
        switch key {
        case "type":
            if v, ok := value.(int); ok {
                slot.Type = &v
            }
        case "trackName":
            if v, ok := value.(string); ok {
                slot.TrackName = v
            }
        // ... autres cas pour chaque champ
        }
    }
    
    return slot
}

// SetLearningMode active ou désactive le mode apprentissage
func (s *State) SetLearningMode(isLearning bool, activeSlot *int) {
    s.Mutex.Lock()
    defer s.Mutex.Unlock()
    
    s.IsLearning = isLearning
    s.ActiveSlot = activeSlot
}
```

## 3. Gestionnaire de handlers

### Avant (liveLearn_registration.go)
```go
// registerAllOSCHandlers enregistre tous les handlers OSC pour le mode learn
func (m *LiveLearnMode) registerAllOSCHandlers() {
    log.Println("Enregistrement de tous les handlers OSC pour le mode learn...")
    
    // Handlers pour les mises à jour de paramètres de piste
    m.registerHandler(OscAddressTrackLearnGetVolume, func(args []interface{}) {
        log.Printf("Handler OscAddressTrackLearnGetVolume appelé (adresse: %s)", OscAddressTrackLearnGetVolume)
        m.handleTrackLearnGetVolume(args)
    }, "mise à jour de volume de piste")
    
    // ... nombreux autres handlers
}
```

### Après (handlers/registration.go)
```go
// HandlerManager gère l'enregistrement et la désinscription des handlers OSC
type HandlerManager struct {
    baseMode     *communication.BaseMode
    trackManager *live.LiveTrackManager
    commManager  *communication.CommunicationManager
    state        *mode.State
    parentMode   *mode.LiveLearnMode
    
    // Sous-gestionnaires spécialisés
    slotHandlers    *SlotHandlers
    trackHandlers   *TrackHandlers
    deviceHandlers  *DeviceHandlers
    chainHandlers   *ChainHandlers
    learningHandlers *LearningHandlers
}

// NewHandlerManager crée une nouvelle instance de HandlerManager
func NewHandlerManager(trackManager *live.LiveTrackManager, commManager *communication.CommunicationManager, state *mode.State) *HandlerManager {
    manager := &HandlerManager{
        trackManager: trackManager,
        commManager:  commManager,
        state:        state,
    }
    
    // Initialiser les sous-gestionnaires
    manager.slotHandlers = NewSlotHandlers(manager)
    manager.trackHandlers = NewTrackHandlers(manager)
    manager.deviceHandlers = NewDeviceHandlers(manager)
    manager.chainHandlers = NewChainHandlers(manager)
    manager.learningHandlers = NewLearningHandlers(manager)
    
    return manager
}

// SetParentMode définit le mode parent
func (m *HandlerManager) SetParentMode(parent *mode.LiveLearnMode) {
    m.parentMode = parent
    m.baseMode = parent.BaseMode
}

// RegisterAllHandlers enregistre tous les handlers OSC
func (m *HandlerManager) RegisterAllHandlers() {
    log.Println("Enregistrement de tous les handlers OSC pour le mode learn...")
    
    // Enregistrer les handlers de chaque sous-gestionnaire
    m.slotHandlers.RegisterHandlers()
    m.trackHandlers.RegisterHandlers()
    m.deviceHandlers.RegisterHandlers()
    m.chainHandlers.RegisterHandlers()
    m.learningHandlers.RegisterHandlers()
    
    log.Println("Tous les handlers OSC pour le mode learn enregistrés avec succès.")
}

// UnregisterAllHandlers désenregistre tous les handlers OSC
func (m *HandlerManager) UnregisterAllHandlers() {
    log.Println("Désenregistrement de tous les handlers OSC pour le mode learn...")
    
    // Désenregistrer les handlers de chaque sous-gestionnaire
    m.slotHandlers.UnregisterHandlers()
    m.trackHandlers.UnregisterHandlers()
    m.deviceHandlers.UnregisterHandlers()
    m.chainHandlers.UnregisterHandlers()
    m.learningHandlers.UnregisterHandlers()
    
    log.Println("Tous les handlers OSC pour le mode learn désenregistrés avec succès.")
}
```

## 4. Gestionnaire d'affichage

### Avant (liveLearn_displayManager.go)
```go
// LiveLearnDisplayManager gère l'affichage du mode learn
type LiveLearnDisplayManager struct {
    communicationManager *communication.CommunicationManager
    liveTrackManager     *live.LiveTrackManager
    state                *LiveLearnModeState
    volumeConverter      *utils.VolumeConverter
    volumeSendConverter  *utils.VolumeSendConverter
    parentMode           *LiveLearnMode
}
```

### Après (display/display_manager.go)
```go
// DisplayManager gère l'affichage du mode learn
type DisplayManager struct {
    commManager       *communication.CommunicationManager
    trackManager      *live.LiveTrackManager
    state             *mode.State
    utils             *utils.UtilsManager
    parentMode        *mode.LiveLearnMode
}

// NewDisplayManager crée une nouvelle instance de DisplayManager
func NewDisplayManager(commManager *communication.CommunicationManager, trackManager *live.LiveTrackManager, state *mode.State) *DisplayManager {
    return &DisplayManager{
        commManager:  commManager,
        trackManager: trackManager,
        state:        state,
        utils:        utils.NewUtilsManager(),
    }
}

// SetParentMode définit le mode parent
func (m *DisplayManager) SetParentMode(parent *mode.LiveLearnMode) {
    m.parentMode = parent
}

// UpdatePage met à jour l'affichage de la page courante
func (m *DisplayManager) UpdatePage(page int) {
    if m.commManager == nil {
        return
    }
    
    // Vérifier si parentMode est nil pour éviter une panique
    isActive := true
    if m.parentMode != nil {
        isActive = m.parentMode.IsActive()
    }
    
    // Envoyer le message de mise à jour de page
    m.commManager.SendMessage(fmt.Sprintf("lp,%d", page), isActive)
}

// UpdateSlot met à jour l'affichage d'un slot
func (m *DisplayManager) UpdateSlot(slotIndex int) {
    // Calculer la page et l'index relatif pour l'affichage
    slotPage := (slotIndex / types.SlotsPerPage) + 1
    displaySlot := slotIndex % types.SlotsPerPage
    isOnCurrentPage := slotPage == m.state.CurrentPage
    
    if !isOnCurrentPage {
        return
    }
    
    // Récupérer les informations du slot
    slot, exists := m.state.GetSlot(slotIndex)
    if !exists || slot.Type == nil {
        return
    }
    
    // Mettre à jour l'affichage selon le type de paramètre
    switch *slot.Type {
    case types.ParamTypeVolume:
        m.updateVolumeSlotDisplay(displaySlot, slot)
    case types.ParamTypePan:
        m.updatePanSlotDisplay(displaySlot, slot)
    // ... autres cas pour chaque type de paramètre
    }
}

// updateVolumeSlotDisplay met à jour l'affichage d'un slot de volume
func (m *DisplayManager) updateVolumeSlotDisplay(displaySlot int, slot *types.SlotInfo) {
    if slot.Value == nil {
        return
    }
    
    value := *slot.Value
    volumePercent := int(math.Round(value * 100))
    volumeDb := m.utils.VolumeConverter.ToDb(value)
    
    // Formater la valeur avec padStart(3, '0') comme dans le JS
    formattedValue := fmt.Sprintf("%03d", volumePercent)
    
    // Envoyer le message de mise à jour
    isActive := m.parentMode != nil && m.parentMode.IsActive()
    m.commManager.SendMessage(fmt.Sprintf("ls,%d,volume,%s,%s dB", displaySlot, formattedValue, volumeDb), isActive)
    
    // Envoyer le message lo
    deviceName := ""
    m.commManager.SendMessage(fmt.Sprintf("lo,%d,%s,%s,%s", displaySlot, deviceName, slot.TrackName, slot.TrackColor), isActive)
}
```

## 5. Utilitaires

### Nouveau fichier (utils/parsers.go)
```go
// ParseOscInt convertit une valeur OSC en int
func ParseOscInt(val interface{}) (int, bool) {
    // OSC peut envoyer des int32 ou int64
    switch v := val.(type) {
    case int:
        return v, true
    case int32:
        return int(v), true
    case int64:
        // Attention: perte potentielle de précision si > max int
        return int(v), true
    case float32:
        return int(v), true
    case float64:
        return int(v), true
    default:
        log.Printf("Erreur de parsing OSC: type inattendu pour int: %T", val)
        return 0, false
    }
}

// ParseOscFloat convertit une valeur OSC en float64
func ParseOscFloat(val interface{}) (float64, bool) {
    // OSC peut envoyer des float32, float64 ou même des entiers qu'il faut convertir
    switch v := val.(type) {
    case float32:
        return float64(v), true
    case float64:
        return v, true
    case int:
        return float64(v), true
    case int32:
        return float64(v), true
    case int64:
        return float64(v), true
    default:
        log.Printf("Erreur de parsing OSC: type inattendu pour float: %T", val)
        return 0.0, false
    }
}
```

### Nouveau fichier (utils/utils_manager.go)
```go
// UtilsManager regroupe les utilitaires pour le mode learn
type UtilsManager struct {
    VolumeConverter    *VolumeConverter
    VolumeSendConverter *VolumeSendConverter
}

// NewUtilsManager crée une nouvelle instance de UtilsManager
func NewUtilsManager() *UtilsManager {
    return &UtilsManager{
        VolumeConverter:    NewVolumeConverter(),
        VolumeSendConverter: NewVolumeSendConverter(),
    }
}

// FormatPanDisplay formate l'affichage du panoramique
func (m *UtilsManager) FormatPanDisplay(panValue float64) string {
    // Limiter la valeur entre -1 et 1
    panValue = math.Max(-1, math.Min(1, panValue))
    
    if panValue == 0 {
        return "C" // Centre
    } else if panValue < 0 {
        // Gauche (L)
        percentage := int(math.Round(math.Abs(panValue) * 50))
        return fmt.Sprintf("%dL", percentage)
    } else {
        // Droite (R)
        percentage := int(math.Round(panValue * 50))
        return fmt.Sprintf("%dR", percentage)
    }
}

// ShortString tronque une chaîne si elle dépasse la longueur maximale
func (m *UtilsManager) ShortString(s string, maxLen int) string {
    if len(s) <= maxLen {
        return s
    }
    return s[:maxLen-3] + "..."
}
```

## 6. Gestionnaire de messages

### Nouveau fichier (messages/message_manager.go)
```go
// MessageManager gère l'envoi et la réception des messages OSC
type MessageManager struct {
    baseMode    *communication.BaseMode
    commManager *communication.CommunicationManager
}

// NewMessageManager crée une nouvelle instance de MessageManager
func NewMessageManager(commManager *communication.CommunicationManager) *MessageManager {
    return &MessageManager{
        commManager: commManager,
    }
}

// SetBaseMode définit le mode de base
func (m *MessageManager) SetBaseMode(baseMode *communication.BaseMode) {
    m.baseMode = baseMode
}

// SendLearnSlot envoie un message de slot d'apprentissage
func (m *MessageManager) SendLearnSlot(slotIndex, paramType, trackIndex int, extraParams ...interface{}) {
    // Préparer les arguments
    args := []interface{}{slotIndex, paramType, trackIndex}
    args = append(args, extraParams...)
    
    // Envoyer le message
    m.baseMode.Send(types.OscAddressLearnSlot, args)
}

// SendLearnStop envoie un message d'arrêt d'apprentissage
func (m *MessageManager) SendLearnStop() {
    m.baseMode.Send(types.OscAddressLearnStop)
}

// SendLearnSet envoie un message de définition de valeur
func (m *MessageManager) SendLearnSet(slotIndex int, value float64) {
    m.baseMode.Send("/live/learn/set", []interface{}{slotIndex, value})
}

// SendLearnSetValue envoie un message de définition de valeur avec des paramètres supplémentaires
func (m *MessageManager) SendLearnSetValue(slotIndex, paramType, indexBis int, value float64) {
    m.baseMode.Send("/live/learn/set/value", []interface{}{slotIndex, paramType, indexBis, value})
}
```

## 7. Handlers spécialisés

### Nouveau fichier (handlers/slot_handlers.go)
```go
// SlotHandlers gère les handlers liés aux slots
type SlotHandlers struct {
    manager *HandlerManager
}

// NewSlotHandlers crée une nouvelle instance de SlotHandlers
func NewSlotHandlers(manager *HandlerManager) *SlotHandlers {
    return &SlotHandlers{
        manager: manager,
    }
}

// RegisterHandlers enregistre les handlers liés aux slots
func (h *SlotHandlers) RegisterHandlers() {
    // Enregistrer le handler pour les propriétés de slot
    h.manager.baseMode.RegisterHandler(types.OscAddressLearnSlotGetProperties, h.HandleSlotProperties, 
        "Handler pour les propriétés de slot en mode learn")
    
    // Enregistrer le handler pour l'effacement de slot
    h.manager.baseMode.RegisterHandler(types.OscAddressLearnSlotCleared, h.HandleSlotCleared, 
        "Handler pour l'effacement de slot en mode learn")
}

// UnregisterHandlers désenregistre les handlers liés aux slots
func (h *SlotHandlers) UnregisterHandlers() {
    h.manager.baseMode.UnregisterHandler(types.OscAddressLearnSlotGetProperties)
    h.manager.baseMode.UnregisterHandler(types.OscAddressLearnSlotCleared)
}

// HandleSlotProperties traite les propriétés de slot
func (h *SlotHandlers) HandleSlotProperties(args []interface{}) {
    if len(args) < 8 {
        log.Println("HandleSlotProperties: arguments insuffisants")
        return
    }
    
    // Extraire les arguments
    slotIndex, ok := utils.ParseOscInt(args[0])
    if !ok {
        log.Println("HandleSlotProperties: impossible de convertir slotIndex en entier")
        return
    }
    
    // ... reste du code pour traiter les propriétés de slot
}

// HandleSlotCleared traite l'effacement de slot
func (h *SlotHandlers) HandleSlotCleared(args []interface{}) {
    if len(args) < 1 {
        log.Println("HandleSlotCleared: arguments insuffisants")
        return
    }
    
    // Extraire l'index du slot
    slotIndex, ok := utils.ParseOscInt(args[0])
    if !ok {
        log.Println("HandleSlotCleared: impossible de convertir slotIndex en entier")
        return
    }
    
    // ... reste du code pour traiter l'effacement de slot
}
```

Ces exemples illustrent comment le code pourrait être restructuré pour améliorer sa lisibilité et sa maintenabilité.
