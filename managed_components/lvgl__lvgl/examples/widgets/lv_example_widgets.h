/**
 * @file lv_example_widgets.h
 *
 */

#ifndef LV_EXAMPLE_WIDGETS_H
#define LV_EXAMPLE_WIDGETS_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/

/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 * GLOBAL PROTOTYPES
 **********************/
void lv_example_arc_1(void);
void lv_example_arc_2(void);

void lv_example_animimg_1(void);

void lv_example_bar_1(void);
void lv_example_bar_2(void);
void lv_example_bar_3(void);
void lv_example_bar_4(void);
void lv_example_bar_5(void);
void lv_example_bar_6(void);

void lv_example_btn_1(void);
void lv_example_btn_2(void);
void lv_example_btn_3(void);

void lv_example_btnmatrix_1(void);
void lv_example_btnmatrix_2(void);
void lv_example_btnmatrix_3(void);

void lv_example_calendar_1(void);

void lv_example_canvas_1(void);
void lv_example_canvas_2(void);

void lv_example_chart_1(void);
void lv_example_chart_2(void);
void lv_example_chart_3(void);
void lv_example_chart_4(void);
void lv_example_chart_5(void);
void lv_example_chart_6(void);
void lv_example_chart_7(void);
void lv_example_chart_8(void);
void lv_example_chart_9(void);

void lv_example_checkbox_1(void);
void lv_example_checkbox_2(void);

void lv_example_colorwheel_1(void);

void lv_example_dropdown_1(void);
void lv_example_dropdown_2(void);
void lv_example_dropdown_3(void);

void lv_example_img_1(void);
void lv_example_img_2(void);
void lv_example_img_3(void);
void lv_example_img_4(void);

void lv_example_imgbtn_1(void);

void lv_example_keyboard_1(void);

void lv_example_label_1(void);
void lv_example_label_2(void);
void lv_example_label_3(void);
void lv_example_label_4(void);
void lv_example_label_5(void);

void lv_example_led_1(void);

void lv_example_line_1(void);

void lv_example_list_1(void);
void lv_example_list_2(void);

void lv_example_menu_1(void);
void lv_example_menu_2(void);
void lv_example_menu_3(void);
void lv_example_menu_4(void);
void lv_example_menu_5(void);

void lv_example_meter_1(void);
void lv_example_meter_2(void);
void lv_example_meter_3(void);
void lv_example_meter_4(void);

void lv_example_msgbox_1(void);

void lv_example_obj_1(void);
void lv_example_obj_2(void);

void lv_example_roller_1(void);
void lv_example_roller_2(void);
void lv_example_roller_3(void);

void lv_example_slider_1(void);
void lv_example_slider_2(void);
void lv_example_slider_3(void);

void lv_example_spinbox_1(void);

void lv_example_spinner_1(void);

void lv_example_switch_1(void);

void lv_example_table_1(void);
void lv_example_table_2(void);

void lv_example_tabview_1(void);
void lv_example_tabview_2(void);

void lv_example_textarea_1(void);
void lv_example_textarea_2(void);
void lv_example_textarea_3(void);

void lv_example_tileview_1(void);

void lv_example_win_1(void);

void lv_example_span_1(void);

/**********************
 *      MACROS
 **********************/

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif /*LV_EX_WIDGETS_H*/
