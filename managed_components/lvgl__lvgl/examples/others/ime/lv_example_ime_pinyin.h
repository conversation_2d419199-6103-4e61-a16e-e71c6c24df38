/**
 * @file lv_example_ime_pinyin.h
 *
 */

#ifndef LV_EX_IME_PINYIN_H
#define LV_EX_IME_PINYIN_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/

/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 * GLOBAL PROTOTYPES
 **********************/
void lv_example_ime_pinyin_1(void);
void lv_example_ime_pinyin_2(void);

/**********************
 *      MACROS
 **********************/

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif /*LV_EX_IME_PINYIN_H*/
