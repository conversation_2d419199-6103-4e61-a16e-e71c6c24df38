from ableton.v2.control_surface import ControlSurface

from . import abletonosc
from .abletonosc.structure_listeners import StructureListeners

import importlib
import traceback
import logging
import os

logger = logging.getLogger("abletonosc")

class Manager(ControlSurface):
    def __init__(self, c_instance):
        ControlSurface.__init__(self, c_instance)

        self.log_level = "debug"
        self.handlers = []
        self.osc_server = abletonosc.OSCServer()
        
        self.lastSelectedDevice = None
        self.lastLockedDevice = None
        self.deviceLock = False  
        self.lockedDevice =  None
        
        self.trackLock = False  
        self.lockedTrack =  None
        self.learn_mode = False
        
        # Ajouter un dictionnaire pour stocker les tâches programmées
        self._scheduled_tasks = {}
        self._task_id_counter = 0

        self.is_selecting_rack_chain = False  # Nouveau: ajouter le flag au manager

        self.start_logging()
        self.init_api()

        # Démarrer le volumeMode après l'initialisation des handlers
        if self.volume_mode:
            self.volume_mode.start(None)
        # Initialize structure listeners        
        self.structure_listeners.setup_listeners()

        self.schedule_message(0, self.tick)
        self.show_message("AbletonOSC: Listening for OSC on port %d" % abletonosc.OSC_LISTEN_PORT)
        logger.info("Started AbletonOSC on address %s" % str(self.osc_server._local_addr))
        
        

        
    def get_visible_devices(self, track_or_chain):
        devices = []
        def add_devices(device_list, parent_visible=True):
            for device in device_list:
                # Ajouter l'appareil seulement si le parent est visible
                if parent_visible:
                    devices.append(device)
                # Pour les racks, vérifier s'ils sont repliés
                if device.can_have_chains and hasattr(device, 'chains'):
                    # Les appareils à l'intérieur sont visibles seulement si le rack est déplié
                    is_rack_expanded = not device.view.is_collapsed
                    if is_rack_expanded:
                        for chain in device.chains:
                            # Passer l'état de visibilité du parent aux appareils enfants
                            add_devices(chain.devices, parent_visible=True)
        
        add_devices(track_or_chain.devices, True)
        return devices
    
    def get_visible_tracks(self):
        """Retourne la liste de toutes les pistes visibles, y compris les return tracks et la piste master"""
        visible_basic_tracks = [track for track in self.song.tracks if track.is_visible]
        # Ajouter les return tracks et la piste master qui sont toujours visibles
        all_visible_tracks = visible_basic_tracks + list(self.song.return_tracks) + [self.song.master_track]
        return all_visible_tracks

    def find_parent_device(self, track, device):
        def search_in_chain(chain):
            for d in chain.devices:
                if d == device:
                    return chain
                if hasattr(d, 'chains'):
                    for c in d.chains:
                        result = search_in_chain(c)
                        if result:
                            return d
            return None

        return search_in_chain(track)
        
    def num_tracks_update(self):
        visible_tracks = [track for track in self.song.tracks if track.is_visible]
        num_visible_tracks = len(visible_tracks) + len(self.song.return_tracks) + 1  # +1 pour la piste master
        self.osc_server.send("/live/song/get/num_tracks", (num_visible_tracks,len(self.song.return_tracks)))
 
    def selected_track_device_update(self):
        logger.info("selected_track_device_update called")
        # Mise à jour du listener pour le device sélectionné
        try:
            self.song.view.selected_track.view.remove_selected_device_listener(self.on_selected_track_or_device_changed)
        except:
            pass  # Si le listener n'existait pas, on ignore l'erreur
        self.song.view.selected_track.view.add_selected_device_listener(self.on_selected_track_or_device_changed)
        
        try:
            selected_track = self.song.view.selected_track
            selected_device = selected_track.view.selected_device
            
            # Créer une liste de toutes les pistes visibles, plus les return tracks et la piste master
            visible_tracks = [track for track in self.song.tracks if track.is_visible]
            all_tracks = visible_tracks + list(self.song.return_tracks) + [self.song.master_track]
            total_tracks = len(all_tracks)

            if selected_track in visible_tracks:
                selected_track_index = visible_tracks.index(selected_track)
            elif selected_track in self.song.return_tracks:
                selected_track_index = len(visible_tracks) + list(self.song.return_tracks).index(selected_track)
            elif selected_track == self.song.master_track:
                selected_track_index = total_tracks - 1
            else:
                logger.warning("Selected track not found in any track list")
                return

            selected_device_index = -1
            num_visible_devices = 0
            num_parameters = 0
            
            # Obtenir tous les devices visibles
            all_devices = self.get_visible_devices(selected_track)
            num_visible_devices = len(all_devices)
            
            if selected_device:
                # Si le device n'est pas dans la liste, chercher son parent
                if selected_device not in all_devices:
                    parent_device = self.find_parent_device(selected_track, selected_device)
                    if parent_device:
                        selected_device = parent_device
                        self.song.view.select_device(parent_device)
                
                # Recalculer l'index après avoir potentiellement changé le device
                if selected_device in all_devices:
                    selected_device_index = all_devices.index(selected_device)
                    num_parameters = len(selected_device.parameters)
                    logger.info(f"Device trouvé: {selected_device.name} à l'index {selected_device_index}")
                else:
                    logger.warning(f"Device {selected_device.name} non trouvé dans les devices visibles")
            
            self.lastSelectedDevice = selected_device
            logger.info(f"Envoi /live/view/get/selected_device: track={selected_track_index}, device={selected_device_index}, visible={num_visible_devices}, params={num_parameters}")
            self.osc_server.send("/live/view/get/selected_device", (selected_track_index, selected_device_index, num_visible_devices, num_parameters))
            
        except Exception as e:
            logger.error(f"Erreur dans selected_track_device_update: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def on_return_tracks_changed(self):
        logger.info("on_return_tracks_changed called")
        try:            
            track_handler = next((handler for handler in self.handlers if isinstance(handler, abletonosc.TrackHandler)), None)
            if track_handler:
                track_handler.add_names_return_tracks_listener()
            
        except Exception as e:
            logger.error(f"Une erreur s'est produite lors du traitement des changements de return tracks : {e}")
  
    def on_tracks_changed(self):
        logger.info("on_tracks_changed called")
        
        try:
            self.num_tracks_update()
            
            if self.volume_mode:
                self.volume_mode._update_listeners()
            
            # Utiliser la nouvelle méthode de vérification
            track_handler = next((handler for handler in self.handlers if isinstance(handler, abletonosc.TrackHandler)), None)
            if track_handler:
                track_handler.track_mode.verify_lock_track_validity()
            
            learnhandler = next((handler for handler in self.handlers if isinstance(handler, abletonosc.LearnModeHandler)), None)
            if learnhandler:
                learnhandler.verify_learn_slots_validity()
            self.structure_listeners._add_track_folding_listeners()
         
        except Exception as e:
            logger.error(f"Une erreur s'est produite lors du traitement des changements de pistes : {e}")
  
    
    def on_selected_track_or_device_changed(self):
        logger.info("on_selected_track_or_device_changed appelé")
        try:
            # Vérifier et réinitialiser le flag
            logger.info("is_selecting_rack_chain: %s", self.is_selecting_rack_chain)
            if self.is_selecting_rack_chain:
                logger.info("Ignorer on_selected_track_or_device_changed pendant select_rack_chain")
                self.is_selecting_rack_chain = False
                return
                
            self.selected_track_device_update()
                        
            
            # Gestion des listeners de track pour le track mode
            track_handler = next((handler for handler in self.handlers if isinstance(handler, abletonosc.TrackHandler)), None)
            if track_handler:                
                track_handler.track_mode.start_selected_track_listeners()
             
            # Gestion des listeners de device pour le device mode
            device_handler = next((handler for handler in self.handlers if isinstance(handler, abletonosc.DeviceHandler)), None)
            if device_handler:
                device_handler.device_mode.start_selected_device_listeners() 
                if self.learn_mode:
                    device_handler.device_learn.setup_learn_listeners()
               
            # Gestion du deviceLock (code existant)
            if self.deviceLock: 
                device_handler = next((handler for handler in self.handlers if isinstance(handler, abletonosc.DeviceHandler)), None)
                if device_handler:
                    device_handler.device_mode.verify_lock_device_validity()
                else:
                    logger.info("Aucun périphérique verrouillé trouvé")
                    self.deviceLock = False                   
                    self.osc_server.send("/live/device_unlock")
          
            learnhandler = next((handler for handler in self.handlers if isinstance(handler, abletonosc.LearnModeHandler)), None)
            if learnhandler:
                learnhandler.verify_learn_slots_validity()                
            
            # self.structure_listeners._add_rack_collapse_listeners()
            
        except Exception as e:
            logger.error(f"Une erreur s'est produite lors du traitement de on_selected_track_or_device_changed : {e}")

    
            
    def start_logging(self):
        """
        Start logging to a local logfile (logs/abletonosc.log),
        and relay error messages via OSC.
        """
        module_path = os.path.dirname(os.path.realpath(__file__))
        log_dir = os.path.join(module_path, "logs")
        if not os.path.exists(log_dir):
            os.mkdir(log_dir, 0o755)
        log_path = os.path.join(log_dir, "abletonosc.log")
        self.log_file_handler = logging.FileHandler(log_path)
        self.log_file_handler.setLevel(self.log_level.upper())
        formatter = logging.Formatter('(%(asctime)s) [%(levelname)s] %(message)s')
        self.log_file_handler.setFormatter(formatter)
        logger.addHandler(self.log_file_handler)

        class LiveOSCErrorLogHandler(logging.StreamHandler):
            def emit(handler, record):
                try:
                    message = handler.format(record)
                    colon_index = message.index(":")
                    message = message[colon_index + 2:]
                except ValueError:
                    # Si ":" n'est pas trouvé, utilisez le message entier
                    message = handler.format(record)
                
                try:
                    self.osc_server.send("/live/error", (message,))
                except OSError:
                    # Si la connexion est morte, ignorez silencieusement les erreurs car il n'y a pas grand-chose d'autre à faire
                    pass
        self.live_osc_error_handler = LiveOSCErrorLogHandler()
        self.live_osc_error_handler.setLevel(logging.ERROR)
        logger.addHandler(self.live_osc_error_handler)

    def stop_logging(self):
        logger.removeHandler(self.log_file_handler)
        logger.removeHandler(self.live_osc_error_handler)

    def init_api(self):
        def test_callback(params):
            self.show_message("Received OSC OK")
            self.osc_server.send("/live/test", ("ok",))
            
        def reload_callback(params):
            self.reload_imports()
        def get_log_level_callback(params):
            return (self.log_level,)
        def set_log_level_callback(params):
            log_level = params[0]
            assert log_level in ("debug", "info", "warning", "error", "critical")
            self.log_level = log_level
            self.log_file_handler.setLevel(self.log_level.upper())

        self.osc_server.add_handler("/live/test", test_callback)
        self.osc_server.add_handler("/live/api/reload", reload_callback)
        self.osc_server.add_handler("/live/api/get/log_level", get_log_level_callback)
        self.osc_server.add_handler("/live/api/set/log_level", set_log_level_callback)
        self.osc_server.add_handler("/live/song/get/num_return_tracks", lambda _: (len(self.song.return_tracks),))

        with self.component_guard():
            self.handlers = [
                abletonosc.SongHandler(self),
                abletonosc.ApplicationHandler(self),
                abletonosc.ClipHandler(self),
                abletonosc.ClipSlotHandler(self),
                abletonosc.TrackHandler(self),
                abletonosc.DeviceHandler(self),
                abletonosc.LearnModeHandler(self),
                abletonosc.ViewHandler(self),
                abletonosc.BrowserHandler(self)
            ]
            
            # Initialiser volume_mode après la création des handlers
            self.volume_mode = None
            for handler in self.handlers:
                if isinstance(handler, abletonosc.TrackHandler):
                    self.volume_mode = handler.volume_mode
                    break

            self.structure_listeners = StructureListeners(self)

    def clear_api(self):
        self.osc_server.clear_handlers()
        for handler in self.handlers:
            handler.clear_api()

    def schedule_task(self, delay, callback):
        """
        Programme une tâche à exécuter après un délai spécifié (en secondes)
        
        Args:
            delay (float): Délai en secondes
            callback (callable): Fonction à exécuter
        """
        task_id = self._task_id_counter
        self._task_id_counter += 1
        
        # Convertir le délai en nombre de ticks (100ms par tick)
        ticks = int(delay * 10)  # 10 ticks par seconde
        if ticks < 1:
            ticks = 1
        
        self._scheduled_tasks[task_id] = {
            'ticks_remaining': ticks,
            'callback': callback
        }
        
        return task_id

    def tick(self):
        """
        Called once per 100ms "tick".
        """
        #logger.debug("Tick...")
        
        # Gérer les tâches programmées
        tasks_to_remove = []
        for task_id, task in self._scheduled_tasks.items():
            task['ticks_remaining'] -= 1
            if task['ticks_remaining'] <= 0:
                try:
                    task['callback']()
                except Exception as e:
                    logger.error(f"Error executing scheduled task: {e}")
                tasks_to_remove.append(task_id)
        
        # Nettoyer les tâches terminées
        for task_id in tasks_to_remove:
            del self._scheduled_tasks[task_id]
        
        # Traiter les mises à jour de paramètres en attente
        device_handler = next((handler for handler in self.handlers if isinstance(handler, abletonosc.DeviceHandler)), None)
        track_handler = next((handler for handler in self.handlers if isinstance(handler, abletonosc.TrackHandler)), None)
        learn_handler = next((handler for handler in self.handlers if isinstance(handler, abletonosc.LearnModeHandler)), None)
        
        if device_handler:
            # Traiter les mises à jour du mode device
            if hasattr(device_handler.device_mode, 'parameter_throttler'):
                device_handler.device_mode.parameter_throttler.send_pending_updates()
            
            # Traiter les mises à jour du mode learn
            if hasattr(device_handler.device_learn, 'parameter_throttler'):
                device_handler.device_learn.parameter_throttler.send_pending_updates()
                
        if track_handler:
            # Traiter les mises à jour du track mode
            if hasattr(track_handler.track_mode, 'parameter_throttler'):
                track_handler.track_mode.parameter_throttler.send_pending_updates()
                
            # Traiter les mises à jour du track learn
            if hasattr(track_handler.track_learn, 'parameter_throttler'):
                track_handler.track_learn.parameter_throttler.send_pending_updates()
                
            # Traiter les mises à jour du volume mode
            if hasattr(track_handler.volume_mode, 'parameter_throttler'):
                track_handler.volume_mode.parameter_throttler.send_pending_updates()
                
        if learn_handler and hasattr(learn_handler, 'parameter_throttler'):
            learn_handler.parameter_throttler.send_pending_updates()
        
        # Traitement OSC normal
        self.osc_server.process()
        self.schedule_message(1, self.tick)

    def reload_imports(self):
        try:
            importlib.reload(abletonosc.application)
            importlib.reload(abletonosc.clip)
            importlib.reload(abletonosc.clip_slot)
            importlib.reload(abletonosc.device)
            importlib.reload(abletonosc.device_mode)
            importlib.reload(abletonosc.handler)
            importlib.reload(abletonosc.osc_server)
            importlib.reload(abletonosc.song)
            importlib.reload(abletonosc.track)
            importlib.reload(abletonosc.track_mode)
            importlib.reload(abletonosc.learn)
            importlib.reload(abletonosc.view)
            importlib.reload(abletonosc.browser)
        except Exception as e:
            exc = traceback.format_exc()
            logging.warning(exc)

        self.clear_api()
        self.init_api()
        logger.info("Reloaded code")

    def disconnect(self):
        self.osc_server.send("/live/disconnect")
        self.show_message("Disconnecting...")
        logger.info("Disconnecting...")
        self.stop_logging()
        self.osc_server.shutdown()
        super().disconnect()
