package debug

import (
	"log"
	communication "oscbridge/Communication"
	live "oscbridge/Live"
	livelearnmode "oscbridge/Live/LiveLearnMode"
	livetrackmode "oscbridge/Live/LiveTrackMode"
	"oscbridge/Live/liveVolumeMode"

	"github.com/eiannone/keyboard"
)

// SetupModeChangeHandlers configure les gestionnaires pour changer de mode
func SetupModeChangeHandlers(debugManager *DebugManager, manager *live.LiveModeManager) {
	debugManager.RegisterRuneHandler('1', func() {
		log.Println("Changement de mode: volume")
		if err := manager.SwitchMode("volume", live.SwitchModeOptions{}); err != nil {
			log.Printf("Erreur lors du changement de mode: %v", err)
		}
	})

	debugManager.RegisterRuneHandler('2', func() {
		log.Println("Changement de mode: track")
		if err := manager.SwitchMode("track", live.SwitchModeOptions{}); err != nil {
			log.Printf("Erreur lors du changement de mode: %v", err)
		}
	})

	debugManager.RegisterRuneHandler('3', func() {
		log.Println("Changement de mode: device")
		if err := manager.SwitchMode("device", live.SwitchModeOptions{}); err != nil {
			log.Printf("Erreur lors du changement de mode: %v", err)
		}
	})

	debugManager.RegisterRuneHandler('4', func() {
		log.Println("Changement de mode: browser")
		if err := manager.SwitchMode("browser", live.SwitchModeOptions{}); err != nil {
			log.Printf("Erreur lors du changement de mode: %v", err)
		}
	})

	debugManager.RegisterRuneHandler('5', func() {
		log.Println("Changement de mode: learn")
		if err := manager.SwitchMode("learn", live.SwitchModeOptions{}); err != nil {
			log.Printf("Erreur lors du changement de mode: %v", err)
		}
	})

	log.Println("Gestionnaires de changement de mode configurés (touches 1-5)")
}

// SetupVolumeNavigationHandlers configure les gestionnaires pour la navigation dans le mode volume
func SetupVolumeNavigationHandlers(debugManager *DebugManager, manager *live.LiveModeManager) {
	// Ajout des gestionnaires pour les touches de navigation dans le mode LiveVolumeMode
	debugManager.RegisterRuneHandler('a', func() {
		log.Println("Action: Page précédente (PageDown)")
		if activeMode, exists := manager.GetMode("volume"); exists {
			if volumeMode, ok := activeMode.(*liveVolumeMode.LiveVolumeMode); ok {
				volumeMode.PageDown()
				log.Println("PageDown exécuté")
			}
		}
	})

	debugManager.RegisterRuneHandler('z', func() {
		log.Println("Action: Page suivante (PageUp)")
		if activeMode, exists := manager.GetMode("volume"); exists {
			if volumeMode, ok := activeMode.(*liveVolumeMode.LiveVolumeMode); ok {
				volumeMode.PageUp()
				log.Println("PageUp exécuté")
			}
		}
	})

	debugManager.RegisterRuneHandler('e', func() {
		log.Println("Action: Mode précédent (ModeDown)")
		if activeMode, exists := manager.GetMode("volume"); exists {
			if volumeMode, ok := activeMode.(*liveVolumeMode.LiveVolumeMode); ok {
				volumeMode.ModeDown()
				log.Println("ModeDown exécuté")
			}
		}
	})

	debugManager.RegisterRuneHandler('r', func() {
		log.Println("Action: Mode suivant (ModeUp)")
		if activeMode, exists := manager.GetMode("volume"); exists {
			if volumeMode, ok := activeMode.(*liveVolumeMode.LiveVolumeMode); ok {
				volumeMode.ModeUp()
				log.Println("ModeUp exécuté")
			}
		}
	})

	// Ajout des gestionnaires pour les touches spéciales Page Up et Page Down
	debugManager.RegisterKeyHandler(keyboard.KeyPgup, func() {
		log.Println("Action: Page suivante (touche Page Up)")
		if activeMode, exists := manager.GetMode("volume"); exists {
			if volumeMode, ok := activeMode.(*liveVolumeMode.LiveVolumeMode); ok {
				volumeMode.PageUp()
				log.Println("PageUp exécuté via touche spéciale")
			}
		}
	})

	debugManager.RegisterKeyHandler(keyboard.KeyPgdn, func() {
		log.Println("Action: Page précédente (touche Page Down)")
		if activeMode, exists := manager.GetMode("volume"); exists {
			if volumeMode, ok := activeMode.(*liveVolumeMode.LiveVolumeMode); ok {
				volumeMode.PageDown()
				log.Println("PageDown exécuté via touche spéciale")
			}
		}
	})

	// Ajout d'un gestionnaire pour afficher les informations de page
	debugManager.RegisterRuneHandler('p', func() {
		log.Println("Affichage des informations de page")
		if activeMode, exists := manager.GetMode("volume"); exists {
			if volumeMode, ok := activeMode.(*liveVolumeMode.LiveVolumeMode); ok {
				pageInfo := volumeMode.GetPageInfo()
				PrintDebugInfo("Informations de page", pageInfo)
			}
		}
	})

	// Ajout du gestionnaire pour la touche 'w' (Lock)
	debugManager.RegisterRuneHandler('w', func() {
		log.Println("Action: Activer le verrouillage de page (Lock)")
		if activeMode, exists := manager.GetMode("volume"); exists {
			if volumeMode, ok := activeMode.(*liveVolumeMode.LiveVolumeMode); ok {
				volumeMode.EnableLock()
				log.Println("EnableLock exécuté")
			}
		}
	})

	// Ajout du gestionnaire pour la touche 'x' (Unlock)
	debugManager.RegisterRuneHandler('x', func() {
		log.Println("Action: Désactiver le verrouillage de page (Unlock)")
		if activeMode, exists := manager.GetMode("volume"); exists {
			if volumeMode, ok := activeMode.(*liveVolumeMode.LiveVolumeMode); ok {
				volumeMode.DisableLock()
				log.Println("DisableLock exécuté")
			}
		}
	})

	log.Println("Gestionnaires de navigation du mode Volume configurés (touches a, z, e, r, p, w, x et Page Up/Down)")
}

// SetupTrackModeHandlers configure les gestionnaires pour le mode Track
func SetupTrackModeHandlers(debugManager *DebugManager, manager *live.LiveModeManager) {
	// Ajout du gestionnaire pour la touche 'l' (ToggleLock pour le mode Track)
	debugManager.RegisterRuneHandler('&', func() {
		log.Println("Action: Toggle Track Lock")
		if activeMode, exists := manager.GetMode("track"); exists {
			if trackMode, ok := activeMode.(*livetrackmode.LiveTrackMode); ok {
				trackMode.ToggleLock()
				log.Println("ToggleLock pour TrackMode exécuté")
			} else {
				log.Printf("Erreur: Impossible de caster le mode actif en LiveTrackMode pour ToggleLock. Type actuel: %T", activeMode)
			}
		} else {
			log.Println("Erreur: Mode Track non trouvé ou non actif pour ToggleLock")
		}
	})

	log.Println("Gestionnaires du mode Track configurés (touche l)")
}

// SetupDeviceModeHandlers configure les gestionnaires pour le mode Device
func SetupDeviceModeHandlers(debugManager *DebugManager, manager *live.LiveModeManager) {
	// Ajout du gestionnaire pour la touche 'ç' (EnableLock pour le mode Device)
	debugManager.RegisterRuneHandler('ç', func() {
		log.Println("Action: Activer le verrouillage du device (EnableLock)")
		if activeMode, exists := manager.GetMode("device"); exists {
			if deviceMode, ok := activeMode.(interface{ EnableLock() }); ok {
				deviceMode.EnableLock()
				log.Println("EnableLock pour DeviceMode exécuté")
			} else {
				log.Printf("Erreur: Impossible d'appeler EnableLock sur le mode actif. Type actuel: %T", activeMode)
			}
		} else {
			log.Println("Erreur: Mode Device non trouvé ou non actif pour EnableLock")
		}
	})

	// Ajout du gestionnaire pour la touche 'à' (DisableLock pour le mode Device)
	debugManager.RegisterRuneHandler('à', func() {
		log.Println("Action: Désactiver le verrouillage du device (DisableLock)")
		if activeMode, exists := manager.GetMode("device"); exists {
			if deviceMode, ok := activeMode.(interface{ DisableLock() }); ok {
				deviceMode.DisableLock()
				log.Println("DisableLock pour DeviceMode exécuté")
			} else {
				log.Printf("Erreur: Impossible d'appeler DisableLock sur le mode actif. Type actuel: %T", activeMode)
			}
		} else {
			log.Println("Erreur: Mode Device non trouvé ou non actif pour DisableLock")
		}
	})

	// Ajout du gestionnaire pour la touche ')' (PageUp pour le mode Device)
	debugManager.RegisterRuneHandler(')', func() {
		log.Println("Action: Page suivante du device (PageUp)")
		if activeMode, exists := manager.GetMode("device"); exists {
			// Créer un événement de bouton pour le bouton 1 (PageUp)
			buttonEvent := &communication.ButtonEvent{
				Index: 1,
			}
			// Créer un événement hardware avec le bouton
			hardwareEvent := communication.HardwareEvent{
				Type:        "button",
				ButtonEvent: buttonEvent,
			}

			// Appeler HandleHardwareEvent sur le mode actif
			if hwReceiver, ok := activeMode.(communication.HardwareEventReceiver); ok {
				hwReceiver.HandleHardwareEvent(hardwareEvent)
				log.Println("PageUp pour DeviceMode exécuté via simulation de bouton")
			} else {
				log.Printf("Erreur: Le mode actif n'implémente pas HardwareEventReceiver. Type actuel: %T", activeMode)
			}
		} else {
			log.Println("Erreur: Mode Device non trouvé ou non actif pour PageUp")
		}
	})

	// Ajout du gestionnaire pour la touche '=' (PageDown pour le mode Device)
	debugManager.RegisterRuneHandler('=', func() {
		log.Println("Action: Page précédente du device (PageDown)")
		if activeMode, exists := manager.GetMode("device"); exists {
			// Créer un événement de bouton pour le bouton 0 (PageDown)
			buttonEvent := &communication.ButtonEvent{
				Index: 0,
			}
			// Créer un événement hardware avec le bouton
			hardwareEvent := communication.HardwareEvent{
				Type:        "button",
				ButtonEvent: buttonEvent,
			}

			// Appeler HandleHardwareEvent sur le mode actif
			if hwReceiver, ok := activeMode.(communication.HardwareEventReceiver); ok {
				hwReceiver.HandleHardwareEvent(hardwareEvent)
				log.Println("PageDown pour DeviceMode exécuté via simulation de bouton")
			} else {
				log.Printf("Erreur: Le mode actif n'implémente pas HardwareEventReceiver. Type actuel: %T", activeMode)
			}
		} else {
			log.Println("Erreur: Mode Device non trouvé ou non actif pour PageDown")
		}
	})

	log.Println("Gestionnaires du mode Device configurés (touches ç, à, ), =)")
}

// SetupLearnModeHandlers configure les gestionnaires pour le mode Learn
func SetupLearnModeHandlers(debugManager *DebugManager, manager *live.LiveModeManager) {
	// Ajout du gestionnaire pour la touche '/' (PageDown pour le mode Learn)
	debugManager.RegisterRuneHandler('/', func() {
		log.Println("Action: Page précédente du mode Learn (PageDown)")
		if activeMode, exists := manager.GetMode("learn"); exists {
			if learnMode, ok := activeMode.(*livelearnmode.LiveLearnMode); ok {
				learnMode.PageDown()
				log.Println("PageDown pour LearnMode exécuté")
			} else {
				log.Printf("Erreur: Impossible de caster le mode actif en LiveLearnMode pour PageDown. Type actuel: %T", activeMode)
			}
		} else {
			log.Println("Erreur: Mode Learn non trouvé ou non actif pour PageDown")
		}
	})

	// Ajout du gestionnaire pour la touche '*' (PageUp pour le mode Learn)
	debugManager.RegisterRuneHandler('*', func() {
		log.Println("Action: Page suivante du mode Learn (PageUp)")
		if activeMode, exists := manager.GetMode("learn"); exists {
			if learnMode, ok := activeMode.(*livelearnmode.LiveLearnMode); ok {
				learnMode.PageUp()
				log.Println("PageUp pour LearnMode exécuté")
			} else {
				log.Printf("Erreur: Impossible de caster le mode actif en LiveLearnMode pour PageUp. Type actuel: %T", activeMode)
			}
		} else {
			log.Println("Erreur: Mode Learn non trouvé ou non actif pour PageUp")
		}
	})

	log.Println("Gestionnaires du mode Learn configurés (touches /, *)")
}

// SetupArrowKeyHandlers configure les gestionnaires pour les touches fléchées
func SetupArrowKeyHandlers(debugManager *DebugManager) {
	// Ajouter des handlers pour les touches spéciales
	debugManager.RegisterKeyHandler(keyboard.KeyArrowUp, func() {
		log.Println("Action: Augmenter la valeur du paramètre actif")
		// Appeler la fonction correspondante ici
	})

	debugManager.RegisterKeyHandler(keyboard.KeyArrowDown, func() {
		log.Println("Action: Diminuer la valeur du paramètre actif")
		// Appeler la fonction correspondante ici
	})

	log.Println("Gestionnaires de touches fléchées configurés")
}

// SetupAllHandlers configure tous les gestionnaires de touches pour l'application
func SetupAllHandlers(debugManager *DebugManager, manager *live.LiveModeManager) {
	// Configurer les handlers par défaut (aide et quitter)
	debugManager.SetupDefaultHandlers()

	// Configurer les handlers de changement de mode
	SetupModeChangeHandlers(debugManager, manager)

	// Configurer les handlers de navigation dans le mode volume
	SetupVolumeNavigationHandlers(debugManager, manager)

	// Configurer les handlers du mode Track
	SetupTrackModeHandlers(debugManager, manager)

	// Configurer les handlers du mode Device
	SetupDeviceModeHandlers(debugManager, manager)

	// Configurer les handlers du mode Learn
	SetupLearnModeHandlers(debugManager, manager)

	// Configurer les handlers des touches fléchées
	SetupArrowKeyHandlers(debugManager)

	// Configurer les handlers pour QuickView
	debugManager.RegisterRuneHandler('é', func() {
		log.Println("Action: Entrer en QuickView")
		manager.EnterTrackQuickView()
	})

	debugManager.RegisterRuneHandler('"', func() {
		log.Println("Action: Sortir de QuickView")
		manager.ExitTrackQuickView()
	})

	log.Println("Tous les gestionnaires de touches ont été configurés")
}

// PrintKeyboardHelp affiche l'aide pour les commandes clavier disponibles
func PrintKeyboardHelp() {
	log.Println("=== Commandes clavier disponibles ===")
	/* log.Println("Commandes générales:")
	log.Println("  h       : Afficher l'aide")
	log.Println("  q       : Quitter l'application")
	log.Println("  Échap   : Quitter l'application")
	log.Println("  Ctrl+C  : Quitter l'application")

	log.Println("\nChangement de mode:")
	log.Println("  1 : Mode Volume")
	log.Println("  2 : Mode Track")
	log.Println("  3 : Mode Device")
	log.Println("  4 : Mode Browser")
	log.Println("  5 : Mode Learn")

	log.Println("\nNavigation dans le mode Volume:")
	log.Println("  a / Page Down : Page précédente")
	log.Println("  z / Page Up   : Page suivante")
	log.Println("  e             : Mode précédent")
	log.Println("  r             : Mode suivant")
	log.Println("  p             : Afficher les informations de page")
	log.Println("  w             : Verrouiller la page actuelle (Volume Lock)")
	log.Println("  x             : Déverrouiller la page actuelle (Volume Unlock)")

	log.Println("\nNavigation/Actions dans le mode Track:")
	log.Println("  &             : Verrouiller/Déverrouiller la piste sélectionnée (Track Lock)")

	log.Println("\nNavigation/Actions dans le mode Device:")
	log.Println("  ç             : Activer le verrouillage du device (EnableLock)")
	log.Println("  à             : Désactiver le verrouillage du device (DisableLock)")
	log.Println("  )             : Page suivante du device (PageUp)")
	log.Println("  =             : Page précédente du device (PageDown)")

	log.Println("\nNavigation/Actions dans le mode Learn:")
	log.Println("  /             : Page précédente (PageDown)")
	log.Println("  *             : Page suivante (PageUp)")

	log.Println("\nQuickView:")
	log.Println("  é             : Entrer en QuickView")
	log.Println("  \"             : Sortir de QuickView")

	log.Println("\nAutres commandes:")
	log.Println("  ↑ : Augmenter la valeur")
	log.Println("  ↓ : Diminuer la valeur")
	log.Println("====================================") */
}
