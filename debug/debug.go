package debug

import (
	"fmt"
	"log"
	"os"
	"strings"
	"syscall"

	"github.com/eiannone/keyboard"
)

// DebugManager gère les fonctions de débogage et le gestionnaire de clavier
type DebugManager struct {
	keyboardManager *KeyboardManager
	isActive        bool
}

// NewDebugManager crée une nouvelle instance de DebugManager
func NewDebugManager() *DebugManager {
	return &DebugManager{
		keyboardManager: NewKeyboardManager(),
		isActive:        false,
	}
}

// Start active le gestionnaire de débogage et démarre l'écoute du clavier
func (dm *DebugManager) Start() error {
	if dm.isActive {
		return fmt.Errorf("le gestionnaire de débogage est déjà actif")
	}

	if err := dm.keyboardManager.Start(); err != nil {
		return fmt.Errorf("impossible de démarrer le gestionnaire de clavier: %w", err)
	}

	dm.isActive = true
	log.Println("Gestionnaire de débogage activé")
	return nil
}

// Stop arrête le gestionnaire de débogage et l'écoute du clavier
func (dm *DebugManager) Stop() {
	if !dm.isActive {
		return
	}

	dm.keyboardManager.Stop()
	dm.isActive = false
	log.Println("Gestionnaire de débogage désactivé")
}

// IsActive retourne true si le gestionnaire de débogage est actif
func (dm *DebugManager) IsActive() bool {
	return dm.isActive
}

// RegisterKeyHandler enregistre un gestionnaire pour une touche spéciale
func (dm *DebugManager) RegisterKeyHandler(key keyboard.Key, handler KeyHandler) {
	dm.keyboardManager.RegisterKeyHandler(key, handler)
}

// RegisterRuneHandler enregistre un gestionnaire pour un caractère
func (dm *DebugManager) RegisterRuneHandler(r rune, handler KeyHandler) {
	dm.keyboardManager.RegisterRuneHandler(r, handler)
}

// SetupDefaultHandlers configure des gestionnaires par défaut pour des fonctions de débogage courantes
func (dm *DebugManager) SetupDefaultHandlers() {
	// Exemple : afficher le mapping des touches lorsque 'h' est pressé (help)
	dm.RegisterRuneHandler('h', func() {
		log.Println(dm.keyboardManager.GetKeyMap())
	})

	// Ajouter un gestionnaire pour la touche Échap pour quitter
	dm.RegisterKeyHandler(keyboard.KeyEsc, func() {
		log.Println("Touche Échap pressée - Arrêt de l'application demandé")
		// Envoyer un signal SIGINT pour simuler Ctrl+C
		p, err := os.FindProcess(os.Getpid())
		if err != nil {
			log.Printf("Erreur lors de la recherche du processus: %v", err)
			return
		}
		if err := p.Signal(syscall.SIGINT); err != nil {
			log.Printf("Erreur lors de l'envoi du signal: %v", err)
		}
	})

	// Ajouter aussi un gestionnaire pour la touche 'q'
	dm.RegisterRuneHandler('q', func() {
		log.Println("Touche 'q' pressée - Arrêt de l'application demandé")
		// Envoyer un signal SIGINT pour simuler Ctrl+C
		p, err := os.FindProcess(os.Getpid())
		if err != nil {
			log.Printf("Erreur lors de la recherche du processus: %v", err)
			return
		}
		if err := p.Signal(syscall.SIGINT); err != nil {
			log.Printf("Erreur lors de l'envoi du signal: %v", err)
		}
	})

	log.Println("Gestionnaires de débogage par défaut configurés")
	log.Println("Appuyez sur 'h' pour afficher l'aide, 'q' ou Échap pour quitter, Ctrl+C pour quitter")
}

// PrintDebugInfo affiche des informations de débogage personnalisées
func PrintDebugInfo(title string, info map[string]interface{}) {
	// Construire une chaîne formatée avec les informations de débogage
	var builder strings.Builder

	builder.WriteString("\n=== " + title + " ===\n")

	for key, value := range info {
		builder.WriteString(fmt.Sprintf("%s: %v\n", key, value))
	}

	builder.WriteString("===================\n")

	// Afficher la chaîne formatée
	log.Println(builder.String())
}

// CreateToggleFunc crée une fonction qui bascule une variable booléenne
func CreateToggleFunc(name string, toggle *bool) KeyHandler {
	return func() {
		*toggle = !*toggle
		log.Printf("Toggle '%s' est maintenant: %v", name, *toggle)
	}
}

// CreateCycleFunc crée une fonction qui cycle à travers une liste de valeurs
func CreateCycleFunc(name string, values []string, currentIndex *int) KeyHandler {
	return func() {
		*currentIndex = (*currentIndex + 1) % len(values)
		log.Printf("Cycle '%s' est maintenant: %s", name, values[*currentIndex])
	}
}
