package debug

import (
	"fmt"
	"log"
)

// Voici un exemple d'utilisation du gestionnaire de débogage dans votre application principale

/*
Exemple d'intégration dans main.go:

import (
	"log"
	"oscbridge/debug"
	// autres imports...
)

func main() {
	// Initialiser le gestionnaire de débogage
	debugManager := debug.NewDebugManager()

	// Configurer des handlers personnalisés
	debugManager.RegisterRuneHandler('1', func() {
		log.Println("Mode 1 activé")
		// Fonction pour activer le mode 1
	})

	debugManager.RegisterRuneHandler('2', func() {
		log.Println("Mode 2 activé")
		// Fonction pour activer le mode 2
	})

	// Ajouter des handlers pour les touches spéciales
	debugManager.RegisterKeyHandler(keyboard.KeyArrowUp, func() {
		log.Println("Flèche Haut pressée")
		// Action pour la flèche haut
	})

	// Configurer également les handlers par défaut
	debugManager.SetupDefaultHandlers()

	// Démarrer le gestionnaire de débogage
	if err := debugManager.Start(); err != nil {
		log.Printf("Erreur lors du démarrage du gestionnaire de débogage: %v", err)
	}

	// Assurer la fermeture propre du gestionnaire de débogage
	defer debugManager.Stop()

	// Reste du code de votre application...
}
*/

// ExempleDeBoguage montre comment configurer et utiliser le gestionnaire de débogage
func ExempleDeBoguage() {
	// Créer une nouvelle instance de DebugManager
	debugManager := NewDebugManager()

	// Variables de débogage d'exemple
	debugMode := false
	verboseMode := false
	modes := []string{"Normal", "Édition", "Visualisation"}
	currentModeIndex := 0

	// Configurer des gestionnaires personnalisés
	debugManager.RegisterRuneHandler('d', CreateToggleFunc("Mode debug", &debugMode))
	debugManager.RegisterRuneHandler('v', CreateToggleFunc("Mode verbose", &verboseMode))
	debugManager.RegisterRuneHandler('m', CreateCycleFunc("Mode d'application", modes, &currentModeIndex))

	// Ajouter un gestionnaire pour afficher des informations de débogage
	debugManager.RegisterRuneHandler('i', func() {
		info := map[string]interface{}{
			"Mode debug":         debugMode,
			"Mode verbose":       verboseMode,
			"Mode d'application": modes[currentModeIndex],
			"Timestamp":          fmt.Sprintf("%v", "timestamp_actuel"),
		}
		PrintDebugInfo("État de l'application", info)
	})

	// Configurer les gestionnaires par défaut (help et quit)
	debugManager.SetupDefaultHandlers()

	// Démarrer le gestionnaire de débogage
	if err := debugManager.Start(); err != nil {
		log.Printf("Erreur lors du démarrage du gestionnaire de débogage: %v", err)
		return
	}

	log.Println("Exemple de débogage démarré")
	log.Println("Appuyez sur 'h' pour voir les commandes disponibles")
	log.Println("Appuyez sur 'q' pour quitter")

	// Cette fonction est seulement un exemple et ne bloque pas l'exécution
	// Dans une application réelle, le programme continuerait à s'exécuter ici
}

// ListeDesCommandesClavier affiche une liste des commandes clavier couramment utilisées
func ListeDesCommandesClavier() {
	commandesMap := map[string]string{
		"h":     "Afficher l'aide",
		"q":     "Quitter l'application",
		"d":     "Activer/désactiver le mode debug",
		"v":     "Activer/désactiver le mode verbose",
		"m":     "Cycler entre les modes d'application",
		"i":     "Afficher les informations de débogage",
		"↑":     "Flèche haut (exemple)",
		"↓":     "Flèche bas (exemple)",
		"←":     "Flèche gauche (exemple)",
		"→":     "Flèche droite (exemple)",
		"Esc":   "Sortir du mode actuel",
		"Enter": "Confirmer une action",
		"1-9":   "Sélectionner une option numérotée",
	}

	fmt.Println("\n=== Liste des commandes clavier ===")
	for touche, description := range commandesMap {
		fmt.Printf("%-10s: %s\n", touche, description)
	}
	fmt.Println("==================================\n")
}
