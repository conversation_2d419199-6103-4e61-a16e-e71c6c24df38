package debug

import (
	"fmt"
	"log"
	"sync"

	"github.com/eiannone/keyboard"
)

// KeyHandler est une fonction qui est appelée quand une touche est pressée
type KeyHandler func()

// KeyboardManager gère l'écoute du clavier et exécute des fonctions associées aux touches
type KeyboardManager struct {
	handlers     map[keyboard.Key]KeyHandler
	runeHandlers map[rune]KeyHandler
	isRunning    bool
	stopChan     chan struct{}
	wg           sync.WaitGroup
}

// NewKeyboardManager crée une nouvelle instance de KeyboardManager
func NewKeyboardManager() *KeyboardManager {
	return &KeyboardManager{
		handlers:     make(map[keyboard.Key]KeyHandler),
		runeHandlers: make(map[rune]KeyHandler),
		isRunning:    false,
		stopChan:     make(chan struct{}),
	}
}

// RegisterKeyHandler associe une fonction à une touche spéciale
func (km *KeyboardManager) RegisterKeyHandler(key keyboard.Key, handler <PERSON><PERSON><PERSON><PERSON>) {
	km.handlers[key] = handler
	log.Printf("Handler enregistré pour la touche spéciale: %v", key)
}

// RegisterRuneHandler associe une fonction à un caractère (lettre, chiffre, etc.)
func (km *KeyboardManager) RegisterRuneHandler(r rune, handler KeyHandler) {
	km.runeHandlers[r] = handler
	log.Printf("Handler enregistré pour le caractère: %c", r)
}

// Start commence à écouter les entrées clavier dans une goroutine
func (km *KeyboardManager) Start() error {
	if km.isRunning {
		return fmt.Errorf("le gestionnaire de clavier est déjà en cours d'exécution")
	}

	// Ouvrir le clavier pour la capture
	if err := keyboard.Open(); err != nil {
		return fmt.Errorf("impossible d'ouvrir le clavier: %w", err)
	}

	km.isRunning = true
	km.wg.Add(1)

	// Lancer l'écoute dans une goroutine
	go func() {
		defer km.wg.Done()
		defer keyboard.Close()

		// Canal pour recevoir les événements clavier
		keyEvents, err := keyboard.GetKeys(10)
		if err != nil {
			log.Printf("Erreur lors de la création du canal d'événements clavier: %v", err)
			return
		}

		log.Println("Démarrage de l'écoute du clavier pour le débogage")
		log.Println("Appuyez sur Ctrl+C pour quitter")

		for {
			select {
			case <-km.stopChan:
				log.Println("Arrêt de l'écoute du clavier")
				return
			case event := <-keyEvents:
				if event.Err != nil {
					log.Printf("Erreur lors de la lecture du clavier: %v", event.Err)
					continue
				}

				// Vérifier si une touche spéciale a été pressée
				if handler, exists := km.handlers[event.Key]; exists && event.Key != 0 {
					log.Printf("Touche spéciale pressée: %v", event.Key)
					handler()
					continue
				}

				// Vérifier si un caractère a été pressé
				if handler, exists := km.runeHandlers[event.Rune]; exists && event.Rune != 0 {
					log.Printf("Caractère pressé: %c", event.Rune)
					handler()
					continue
				}

				// Afficher l'information sur la touche pressée si aucun handler n'existe
				if event.Key != 0 {
					log.Printf("Touche sans handler: %v", event.Key)
				} else if event.Rune != 0 {
					log.Printf("Caractère sans handler: %c (%d)", event.Rune, event.Rune)
				}
			}
		}
	}()

	return nil
}

// Stop arrête l'écoute du clavier
func (km *KeyboardManager) Stop() {
	if !km.isRunning {
		return
	}

	close(km.stopChan)
	km.wg.Wait()
	km.isRunning = false
	log.Println("Gestionnaire de clavier arrêté")
}

// ListRegisteredKeys affiche toutes les touches enregistrées et leurs handlers associés
func (km *KeyboardManager) ListRegisteredKeys() {
	log.Println("Touches spéciales enregistrées:")
	for key := range km.handlers {
		log.Printf("- Touche: %v", key)
	}

	log.Println("Caractères enregistrés:")
	for r := range km.runeHandlers {
		log.Printf("- Caractère: %c (%d)", r, r)
	}
}

// GetKeyMap renvoie une chaîne de caractères décrivant les touches et leurs fonctions
func (km *KeyboardManager) GetKeyMap() string {
	var result string

	result += "=== Carte des touches de débogage ===\n"

	if len(km.handlers) > 0 {
		result += "Touches spéciales:\n"
		for key := range km.handlers {
			result += fmt.Sprintf("- %v\n", key)
		}
	}

	if len(km.runeHandlers) > 0 {
		result += "Caractères:\n"
		for r := range km.runeHandlers {
			result += fmt.Sprintf("- '%c'\n", r)
		}
	}

	return result
}
