# Test de la logique corrigée du mode learn

## ✅ Problème identifié et corrigé

### ❌ Ancienne logique (FAUSSE)
- Cherchait "un device en cours d'apprentissage"
- Pensait que les encoders contrôlaient directement les paramètres d'un device
- Copiait la logique du mode device

### ✅ Nouvelle logique (CORRECTE)
- **32 slots** (0-31) organisés en 4 pages de 8 slots
- **8 encoders** correspondent aux **8 slots de la page courante**
- Chaque slot peut contenir **n'importe quel type de paramètre**
- **Encodeur 0** → Slot 0 de la page courante → Paramètre assigné à ce slot
- **Encodeur 1** → Slot 1 de la page courante → Paramètre assigné à ce slot, etc.

## 🔧 Architecture corrigée

### Go (OSCBridgeGo)
```go
// Dans handleEncoderEvent
encoder_index := event.Index  // 0-7
parameter_index := encoder_index + 1  // 1-8 pour Python
// Envoie: /live/learn/adjust/parameter [parameter_index, delta]
```

### Python (AbletonOSC)
```python
def adjust_learn_parameter(self, params):
    encoder_index = int(params[0]) - 1  # Go envoie 1-8, on veut 0-7
    delta_value = float(params[1])
    
    # Calculer l'index du slot (0-7 pour la page courante)
    slot_index = encoder_index
    
    # Récupérer les données du slot
    slot_data = self.learn_slots[slot_index]
    param_type = slot_data.get("param_type")
    
    # Traiter selon le type de paramètre
    if param_type == 1:  # Volume de piste
        self._adjust_track_volume(slot_data, delta_value)
    elif param_type == 2:  # Pan de piste
        self._adjust_track_pan(slot_data, delta_value)
    # etc...
```

## 📋 Types de paramètres supportés

| Type | Description           | Méthode                    |
|------|-----------------------|----------------------------|
| 1    | Volume de piste       | `_adjust_track_volume`     |
| 2    | Panoramique de piste  | `_adjust_track_pan`        |
| 3    | Send de piste         | `_adjust_track_send`       |
| 4    | Paramètre de device   | `_adjust_device_parameter` |
| 5    | Volume de chaîne      | `_adjust_chain_volume`     |
| 6    | Pan de chaîne         | `_adjust_chain_pan`        |
| 7    | Mute de piste         | `_adjust_track_mute`       |
| 8    | Solo de piste         | `_adjust_track_solo`       |
| 9    | Mute de chaîne        | `_adjust_chain_mute`       |
| 10   | Solo de chaîne        | `_adjust_chain_solo`       |

## 🎛️ Logique de scaling par type

### Paramètres continus
- **Volume/Pan/Send** : `delta / 5.0` (comme mode volume)
- **Device normal** : `delta / 5.0` (paramètres 0-1)
- **Device rack** : `delta * 25.4` (paramètres 0-127)

### Paramètres quantifiés (device)
- Système de buffers avec seuil 0.5
- Incrémentation/décrémentation par pas discrets
- Non-circulaires (bornés aux extrémités)

### Paramètres booléens (mute/solo)
- Seuil de 0.1 pour déclencher le toggle
- Évite les toggles accidentels

## 🔄 Flux de données corrigé

1. **Encodeur tourné** → Go reçoit l'événement (index 0-7)
2. **Buffer & seuil** → Accumulation jusqu'à 0.005
3. **OSC vers Python** → `/live/learn/adjust/parameter [encoder_index+1, delta]`
4. **Calcul du slot** → `slot_index = encoder_index`
5. **Récupération du slot** → `slot_data = learn_slots[slot_index]`
6. **Type de paramètre** → `param_type = slot_data.get("param_type")`
7. **Ajustement** → Appel de la méthode appropriée selon le type
8. **Application** → Modification effective du paramètre assigné

## 🎯 Exemple concret

### Scénario
- Page 1 active (slots 0-7)
- Slot 0 : Volume de la piste 1 (type 1)
- Slot 1 : Paramètre de device (type 4)
- Slot 2 : Send 1 de la piste 2 (type 3)

### Action : Tourner l'encodeur 1
1. Go : `encoder_index = 1`
2. Go : Envoie `/live/learn/adjust/parameter [2, 0.05]`
3. Python : `encoder_index = 2 - 1 = 1`
4. Python : `slot_index = 1`
5. Python : `slot_data = learn_slots[1]` → Device parameter
6. Python : `param_type = 4`
7. Python : Appel `_adjust_device_parameter(slot_data, 0.05)`
8. Python : Ajuste le paramètre du device assigné au slot 1

## ✅ Avantages de la nouvelle logique

1. **Cohérence** : Respecte l'architecture réelle du mode learn
2. **Flexibilité** : Supporte tous les types de paramètres
3. **Simplicité** : Logique claire et directe
4. **Extensibilité** : Facile d'ajouter de nouveaux types
5. **Performance** : Pas de recherche complexe de "device en apprentissage"

## 🚀 Prêt pour les tests

L'implémentation corrigée est maintenant prête à être testée avec :
- Différents types de paramètres assignés aux slots
- Paramètres quantifiés et continus
- Scaling adaptatif selon le type
- Gestion des buffers pour les paramètres quantifiés
